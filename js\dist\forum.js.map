{"version": 3, "file": "forum.js", "sources": ["../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/ssr-window.esm.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/utils.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/swiper-core.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/create-element-if-not-defined.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/modules/navigation.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/classes-to-selector.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/modules/pagination.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/modules/autoplay.mjs", "../src/forum/utils/dom-utils.ts", "../src/common/config/constants.ts", "../src/forum/utils/mobile-detection.ts", "../src/common/config/defaults.ts", "../src/forum/components/slideshow-manager.ts", "../src/forum/utils/error-handler.ts", "../src/forum/utils/config-manager.ts", "../src/forum/index.ts"], "sourcesContent": ["/**\n * SSR Window 5.0.1\n * Better handling for window object in SSR environment\n * https://github.com/nolimits4web/ssr-window\n *\n * Copyright 2025, <PERSON>\n *\n * Licensed under MIT\n *\n * Released on: June 27, 2025\n */\n/* eslint-disable no-param-reassign */\nfunction isObject(obj) {\n  return obj !== null && typeof obj === 'object' && 'constructor' in obj && obj.constructor === Object;\n}\nfunction extend(target, src) {\n  if (target === void 0) {\n    target = {};\n  }\n  if (src === void 0) {\n    src = {};\n  }\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  Object.keys(src).filter(key => noExtend.indexOf(key) < 0).forEach(key => {\n    if (typeof target[key] === 'undefined') target[key] = src[key];else if (isObject(src[key]) && isObject(target[key]) && Object.keys(src[key]).length > 0) {\n      extend(target[key], src[key]);\n    }\n  });\n}\nconst ssrDocument = {\n  body: {},\n  addEventListener() {},\n  removeEventListener() {},\n  activeElement: {\n    blur() {},\n    nodeName: ''\n  },\n  querySelector() {\n    return null;\n  },\n  querySelectorAll() {\n    return [];\n  },\n  getElementById() {\n    return null;\n  },\n  createEvent() {\n    return {\n      initEvent() {}\n    };\n  },\n  createElement() {\n    return {\n      children: [],\n      childNodes: [],\n      style: {},\n      setAttribute() {},\n      getElementsByTagName() {\n        return [];\n      }\n    };\n  },\n  createElementNS() {\n    return {};\n  },\n  importNode() {\n    return null;\n  },\n  location: {\n    hash: '',\n    host: '',\n    hostname: '',\n    href: '',\n    origin: '',\n    pathname: '',\n    protocol: '',\n    search: ''\n  }\n};\nfunction getDocument() {\n  const doc = typeof document !== 'undefined' ? document : {};\n  extend(doc, ssrDocument);\n  return doc;\n}\nconst ssrWindow = {\n  document: ssrDocument,\n  navigator: {\n    userAgent: ''\n  },\n  location: {\n    hash: '',\n    host: '',\n    hostname: '',\n    href: '',\n    origin: '',\n    pathname: '',\n    protocol: '',\n    search: ''\n  },\n  history: {\n    replaceState() {},\n    pushState() {},\n    go() {},\n    back() {}\n  },\n  CustomEvent: function CustomEvent() {\n    return this;\n  },\n  addEventListener() {},\n  removeEventListener() {},\n  getComputedStyle() {\n    return {\n      getPropertyValue() {\n        return '';\n      }\n    };\n  },\n  Image() {},\n  Date() {},\n  screen: {},\n  setTimeout() {},\n  clearTimeout() {},\n  matchMedia() {\n    return {};\n  },\n  requestAnimationFrame(callback) {\n    if (typeof setTimeout === 'undefined') {\n      callback();\n      return null;\n    }\n    return setTimeout(callback, 0);\n  },\n  cancelAnimationFrame(id) {\n    if (typeof setTimeout === 'undefined') {\n      return;\n    }\n    clearTimeout(id);\n  }\n};\nfunction getWindow() {\n  const win = typeof window !== 'undefined' ? window : {};\n  extend(win, ssrWindow);\n  return win;\n}\n\nexport { getWindow as a, getDocument as g };\n", "import { a as getWindow, g as getDocument } from './ssr-window.esm.mjs';\n\nfunction classesToTokens(classes) {\n  if (classes === void 0) {\n    classes = '';\n  }\n  return classes.trim().split(' ').filter(c => !!c.trim());\n}\n\nfunction deleteProps(obj) {\n  const object = obj;\n  Object.keys(object).forEach(key => {\n    try {\n      object[key] = null;\n    } catch (e) {\n      // no getter for object\n    }\n    try {\n      delete object[key];\n    } catch (e) {\n      // something got wrong\n    }\n  });\n}\nfunction nextTick(callback, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  return setTimeout(callback, delay);\n}\nfunction now() {\n  return Date.now();\n}\nfunction getComputedStyle(el) {\n  const window = getWindow();\n  let style;\n  if (window.getComputedStyle) {\n    style = window.getComputedStyle(el, null);\n  }\n  if (!style && el.currentStyle) {\n    style = el.currentStyle;\n  }\n  if (!style) {\n    style = el.style;\n  }\n  return style;\n}\nfunction getTranslate(el, axis) {\n  if (axis === void 0) {\n    axis = 'x';\n  }\n  const window = getWindow();\n  let matrix;\n  let curTransform;\n  let transformMatrix;\n  const curStyle = getComputedStyle(el);\n  if (window.WebKitCSSMatrix) {\n    curTransform = curStyle.transform || curStyle.webkitTransform;\n    if (curTransform.split(',').length > 6) {\n      curTransform = curTransform.split(', ').map(a => a.replace(',', '.')).join(', ');\n    }\n    // Some old versions of Webkit choke when 'none' is passed; pass\n    // empty string instead in this case\n    transformMatrix = new window.WebKitCSSMatrix(curTransform === 'none' ? '' : curTransform);\n  } else {\n    transformMatrix = curStyle.MozTransform || curStyle.OTransform || curStyle.MsTransform || curStyle.msTransform || curStyle.transform || curStyle.getPropertyValue('transform').replace('translate(', 'matrix(1, 0, 0, 1,');\n    matrix = transformMatrix.toString().split(',');\n  }\n  if (axis === 'x') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m41;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[12]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[4]);\n  }\n  if (axis === 'y') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m42;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[13]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[5]);\n  }\n  return curTransform || 0;\n}\nfunction isObject(o) {\n  return typeof o === 'object' && o !== null && o.constructor && Object.prototype.toString.call(o).slice(8, -1) === 'Object';\n}\nfunction isNode(node) {\n  // eslint-disable-next-line\n  if (typeof window !== 'undefined' && typeof window.HTMLElement !== 'undefined') {\n    return node instanceof HTMLElement;\n  }\n  return node && (node.nodeType === 1 || node.nodeType === 11);\n}\nfunction extend() {\n  const to = Object(arguments.length <= 0 ? undefined : arguments[0]);\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  for (let i = 1; i < arguments.length; i += 1) {\n    const nextSource = i < 0 || arguments.length <= i ? undefined : arguments[i];\n    if (nextSource !== undefined && nextSource !== null && !isNode(nextSource)) {\n      const keysArray = Object.keys(Object(nextSource)).filter(key => noExtend.indexOf(key) < 0);\n      for (let nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex += 1) {\n        const nextKey = keysArray[nextIndex];\n        const desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);\n        if (desc !== undefined && desc.enumerable) {\n          if (isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else if (!isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            to[nextKey] = {};\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else {\n            to[nextKey] = nextSource[nextKey];\n          }\n        }\n      }\n    }\n  }\n  return to;\n}\nfunction setCSSProperty(el, varName, varValue) {\n  el.style.setProperty(varName, varValue);\n}\nfunction animateCSSModeScroll(_ref) {\n  let {\n    swiper,\n    targetPosition,\n    side\n  } = _ref;\n  const window = getWindow();\n  const startPosition = -swiper.translate;\n  let startTime = null;\n  let time;\n  const duration = swiper.params.speed;\n  swiper.wrapperEl.style.scrollSnapType = 'none';\n  window.cancelAnimationFrame(swiper.cssModeFrameID);\n  const dir = targetPosition > startPosition ? 'next' : 'prev';\n  const isOutOfBound = (current, target) => {\n    return dir === 'next' && current >= target || dir === 'prev' && current <= target;\n  };\n  const animate = () => {\n    time = new Date().getTime();\n    if (startTime === null) {\n      startTime = time;\n    }\n    const progress = Math.max(Math.min((time - startTime) / duration, 1), 0);\n    const easeProgress = 0.5 - Math.cos(progress * Math.PI) / 2;\n    let currentPosition = startPosition + easeProgress * (targetPosition - startPosition);\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      currentPosition = targetPosition;\n    }\n    swiper.wrapperEl.scrollTo({\n      [side]: currentPosition\n    });\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      swiper.wrapperEl.style.overflow = 'hidden';\n      swiper.wrapperEl.style.scrollSnapType = '';\n      setTimeout(() => {\n        swiper.wrapperEl.style.overflow = '';\n        swiper.wrapperEl.scrollTo({\n          [side]: currentPosition\n        });\n      });\n      window.cancelAnimationFrame(swiper.cssModeFrameID);\n      return;\n    }\n    swiper.cssModeFrameID = window.requestAnimationFrame(animate);\n  };\n  animate();\n}\nfunction getSlideTransformEl(slideEl) {\n  return slideEl.querySelector('.swiper-slide-transform') || slideEl.shadowRoot && slideEl.shadowRoot.querySelector('.swiper-slide-transform') || slideEl;\n}\nfunction elementChildren(element, selector) {\n  if (selector === void 0) {\n    selector = '';\n  }\n  const window = getWindow();\n  const children = [...element.children];\n  if (window.HTMLSlotElement && element instanceof HTMLSlotElement) {\n    children.push(...element.assignedElements());\n  }\n  if (!selector) {\n    return children;\n  }\n  return children.filter(el => el.matches(selector));\n}\nfunction elementIsChildOfSlot(el, slot) {\n  // Breadth-first search through all parent's children and assigned elements\n  const elementsQueue = [slot];\n  while (elementsQueue.length > 0) {\n    const elementToCheck = elementsQueue.shift();\n    if (el === elementToCheck) {\n      return true;\n    }\n    elementsQueue.push(...elementToCheck.children, ...(elementToCheck.shadowRoot ? elementToCheck.shadowRoot.children : []), ...(elementToCheck.assignedElements ? elementToCheck.assignedElements() : []));\n  }\n}\nfunction elementIsChildOf(el, parent) {\n  const window = getWindow();\n  let isChild = parent.contains(el);\n  if (!isChild && window.HTMLSlotElement && parent instanceof HTMLSlotElement) {\n    const children = [...parent.assignedElements()];\n    isChild = children.includes(el);\n    if (!isChild) {\n      isChild = elementIsChildOfSlot(el, parent);\n    }\n  }\n  return isChild;\n}\nfunction showWarning(text) {\n  try {\n    console.warn(text);\n    return;\n  } catch (err) {\n    // err\n  }\n}\nfunction createElement(tag, classes) {\n  if (classes === void 0) {\n    classes = [];\n  }\n  const el = document.createElement(tag);\n  el.classList.add(...(Array.isArray(classes) ? classes : classesToTokens(classes)));\n  return el;\n}\nfunction elementOffset(el) {\n  const window = getWindow();\n  const document = getDocument();\n  const box = el.getBoundingClientRect();\n  const body = document.body;\n  const clientTop = el.clientTop || body.clientTop || 0;\n  const clientLeft = el.clientLeft || body.clientLeft || 0;\n  const scrollTop = el === window ? window.scrollY : el.scrollTop;\n  const scrollLeft = el === window ? window.scrollX : el.scrollLeft;\n  return {\n    top: box.top + scrollTop - clientTop,\n    left: box.left + scrollLeft - clientLeft\n  };\n}\nfunction elementPrevAll(el, selector) {\n  const prevEls = [];\n  while (el.previousElementSibling) {\n    const prev = el.previousElementSibling; // eslint-disable-line\n    if (selector) {\n      if (prev.matches(selector)) prevEls.push(prev);\n    } else prevEls.push(prev);\n    el = prev;\n  }\n  return prevEls;\n}\nfunction elementNextAll(el, selector) {\n  const nextEls = [];\n  while (el.nextElementSibling) {\n    const next = el.nextElementSibling; // eslint-disable-line\n    if (selector) {\n      if (next.matches(selector)) nextEls.push(next);\n    } else nextEls.push(next);\n    el = next;\n  }\n  return nextEls;\n}\nfunction elementStyle(el, prop) {\n  const window = getWindow();\n  return window.getComputedStyle(el, null).getPropertyValue(prop);\n}\nfunction elementIndex(el) {\n  let child = el;\n  let i;\n  if (child) {\n    i = 0;\n    // eslint-disable-next-line\n    while ((child = child.previousSibling) !== null) {\n      if (child.nodeType === 1) i += 1;\n    }\n    return i;\n  }\n  return undefined;\n}\nfunction elementParents(el, selector) {\n  const parents = []; // eslint-disable-line\n  let parent = el.parentElement; // eslint-disable-line\n  while (parent) {\n    if (selector) {\n      if (parent.matches(selector)) parents.push(parent);\n    } else {\n      parents.push(parent);\n    }\n    parent = parent.parentElement;\n  }\n  return parents;\n}\nfunction elementTransitionEnd(el, callback) {\n  function fireCallBack(e) {\n    if (e.target !== el) return;\n    callback.call(el, e);\n    el.removeEventListener('transitionend', fireCallBack);\n  }\n  if (callback) {\n    el.addEventListener('transitionend', fireCallBack);\n  }\n}\nfunction elementOuterSize(el, size, includeMargins) {\n  const window = getWindow();\n  if (includeMargins) {\n    return el[size === 'width' ? 'offsetWidth' : 'offsetHeight'] + parseFloat(window.getComputedStyle(el, null).getPropertyValue(size === 'width' ? 'margin-right' : 'margin-top')) + parseFloat(window.getComputedStyle(el, null).getPropertyValue(size === 'width' ? 'margin-left' : 'margin-bottom'));\n  }\n  return el.offsetWidth;\n}\nfunction makeElementsArray(el) {\n  return (Array.isArray(el) ? el : [el]).filter(e => !!e);\n}\nfunction getRotateFix(swiper) {\n  return v => {\n    if (Math.abs(v) > 0 && swiper.browser && swiper.browser.need3dFix && Math.abs(v) % 90 === 0) {\n      return v + 0.001;\n    }\n    return v;\n  };\n}\nfunction setInnerHTML(el, html) {\n  if (html === void 0) {\n    html = '';\n  }\n  if (typeof trustedTypes !== 'undefined') {\n    el.innerHTML = trustedTypes.createPolicy('html', {\n      createHTML: s => s\n    }).createHTML(html);\n  } else {\n    el.innerHTML = html;\n  }\n}\n\nexport { setCSSProperty as a, elementParents as b, createElement as c, elementOffset as d, elementChildren as e, now as f, getSlideTransformEl as g, elementOuterSize as h, elementIndex as i, classesToTokens as j, getTranslate as k, elementTransitionEnd as l, makeElementsArray as m, nextTick as n, isObject as o, getRotateFix as p, elementStyle as q, elementNextAll as r, setInnerHTML as s, elementPrevAll as t, animateCSSModeScroll as u, showWarning as v, elementIsChildOf as w, extend as x, deleteProps as y };\n", "import { a as getWindow, g as getDocument } from './ssr-window.esm.mjs';\nimport { b as elementParents, q as elementStyle, e as elementChildren, a as setCSSProperty, h as elementOuterSize, r as elementNextAll, t as elementPrevAll, k as getTranslate, u as animateCSSModeScroll, n as nextTick, v as showWarning, c as createElement, w as elementIsChildOf, f as now, x as extend, i as elementIndex, y as deleteProps } from './utils.mjs';\n\nlet support;\nfunction calcSupport() {\n  const window = getWindow();\n  const document = getDocument();\n  return {\n    smoothScroll: document.documentElement && document.documentElement.style && 'scrollBehavior' in document.documentElement.style,\n    touch: !!('ontouchstart' in window || window.DocumentTouch && document instanceof window.DocumentTouch)\n  };\n}\nfunction getSupport() {\n  if (!support) {\n    support = calcSupport();\n  }\n  return support;\n}\n\nlet deviceCached;\nfunction calcDevice(_temp) {\n  let {\n    userAgent\n  } = _temp === void 0 ? {} : _temp;\n  const support = getSupport();\n  const window = getWindow();\n  const platform = window.navigator.platform;\n  const ua = userAgent || window.navigator.userAgent;\n  const device = {\n    ios: false,\n    android: false\n  };\n  const screenWidth = window.screen.width;\n  const screenHeight = window.screen.height;\n  const android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n  let ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n  const ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n  const iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n  const windows = platform === 'Win32';\n  let macos = platform === 'MacIntel';\n\n  // iPadOs 13 fix\n  const iPadScreens = ['1024x1366', '1366x1024', '834x1194', '1194x834', '834x1112', '1112x834', '768x1024', '1024x768', '820x1180', '1180x820', '810x1080', '1080x810'];\n  if (!ipad && macos && support.touch && iPadScreens.indexOf(`${screenWidth}x${screenHeight}`) >= 0) {\n    ipad = ua.match(/(Version)\\/([\\d.]+)/);\n    if (!ipad) ipad = [0, 1, '13_0_0'];\n    macos = false;\n  }\n\n  // Android\n  if (android && !windows) {\n    device.os = 'android';\n    device.android = true;\n  }\n  if (ipad || iphone || ipod) {\n    device.os = 'ios';\n    device.ios = true;\n  }\n\n  // Export object\n  return device;\n}\nfunction getDevice(overrides) {\n  if (overrides === void 0) {\n    overrides = {};\n  }\n  if (!deviceCached) {\n    deviceCached = calcDevice(overrides);\n  }\n  return deviceCached;\n}\n\nlet browser;\nfunction calcBrowser() {\n  const window = getWindow();\n  const device = getDevice();\n  let needPerspectiveFix = false;\n  function isSafari() {\n    const ua = window.navigator.userAgent.toLowerCase();\n    return ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0;\n  }\n  if (isSafari()) {\n    const ua = String(window.navigator.userAgent);\n    if (ua.includes('Version/')) {\n      const [major, minor] = ua.split('Version/')[1].split(' ')[0].split('.').map(num => Number(num));\n      needPerspectiveFix = major < 16 || major === 16 && minor < 2;\n    }\n  }\n  const isWebView = /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(window.navigator.userAgent);\n  const isSafariBrowser = isSafari();\n  const need3dFix = isSafariBrowser || isWebView && device.ios;\n  return {\n    isSafari: needPerspectiveFix || isSafariBrowser,\n    needPerspectiveFix,\n    need3dFix,\n    isWebView\n  };\n}\nfunction getBrowser() {\n  if (!browser) {\n    browser = calcBrowser();\n  }\n  return browser;\n}\n\nfunction Resize(_ref) {\n  let {\n    swiper,\n    on,\n    emit\n  } = _ref;\n  const window = getWindow();\n  let observer = null;\n  let animationFrame = null;\n  const resizeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('beforeResize');\n    emit('resize');\n  };\n  const createObserver = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    observer = new ResizeObserver(entries => {\n      animationFrame = window.requestAnimationFrame(() => {\n        const {\n          width,\n          height\n        } = swiper;\n        let newWidth = width;\n        let newHeight = height;\n        entries.forEach(_ref2 => {\n          let {\n            contentBoxSize,\n            contentRect,\n            target\n          } = _ref2;\n          if (target && target !== swiper.el) return;\n          newWidth = contentRect ? contentRect.width : (contentBoxSize[0] || contentBoxSize).inlineSize;\n          newHeight = contentRect ? contentRect.height : (contentBoxSize[0] || contentBoxSize).blockSize;\n        });\n        if (newWidth !== width || newHeight !== height) {\n          resizeHandler();\n        }\n      });\n    });\n    observer.observe(swiper.el);\n  };\n  const removeObserver = () => {\n    if (animationFrame) {\n      window.cancelAnimationFrame(animationFrame);\n    }\n    if (observer && observer.unobserve && swiper.el) {\n      observer.unobserve(swiper.el);\n      observer = null;\n    }\n  };\n  const orientationChangeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('orientationchange');\n  };\n  on('init', () => {\n    if (swiper.params.resizeObserver && typeof window.ResizeObserver !== 'undefined') {\n      createObserver();\n      return;\n    }\n    window.addEventListener('resize', resizeHandler);\n    window.addEventListener('orientationchange', orientationChangeHandler);\n  });\n  on('destroy', () => {\n    removeObserver();\n    window.removeEventListener('resize', resizeHandler);\n    window.removeEventListener('orientationchange', orientationChangeHandler);\n  });\n}\n\nfunction Observer(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const observers = [];\n  const window = getWindow();\n  const attach = function (target, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    const ObserverFunc = window.MutationObserver || window.WebkitMutationObserver;\n    const observer = new ObserverFunc(mutations => {\n      // The observerUpdate event should only be triggered\n      // once despite the number of mutations.  Additional\n      // triggers are redundant and are very costly\n      if (swiper.__preventObserver__) return;\n      if (mutations.length === 1) {\n        emit('observerUpdate', mutations[0]);\n        return;\n      }\n      const observerUpdate = function observerUpdate() {\n        emit('observerUpdate', mutations[0]);\n      };\n      if (window.requestAnimationFrame) {\n        window.requestAnimationFrame(observerUpdate);\n      } else {\n        window.setTimeout(observerUpdate, 0);\n      }\n    });\n    observer.observe(target, {\n      attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n      childList: swiper.isElement || (typeof options.childList === 'undefined' ? true : options).childList,\n      characterData: typeof options.characterData === 'undefined' ? true : options.characterData\n    });\n    observers.push(observer);\n  };\n  const init = () => {\n    if (!swiper.params.observer) return;\n    if (swiper.params.observeParents) {\n      const containerParents = elementParents(swiper.hostEl);\n      for (let i = 0; i < containerParents.length; i += 1) {\n        attach(containerParents[i]);\n      }\n    }\n    // Observe container\n    attach(swiper.hostEl, {\n      childList: swiper.params.observeSlideChildren\n    });\n\n    // Observe wrapper\n    attach(swiper.wrapperEl, {\n      attributes: false\n    });\n  };\n  const destroy = () => {\n    observers.forEach(observer => {\n      observer.disconnect();\n    });\n    observers.splice(0, observers.length);\n  };\n  extendParams({\n    observer: false,\n    observeParents: false,\n    observeSlideChildren: false\n  });\n  on('init', init);\n  on('destroy', destroy);\n}\n\n/* eslint-disable no-underscore-dangle */\n\nvar eventsEmitter = {\n  on(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach(event => {\n      if (!self.eventsListeners[event]) self.eventsListeners[event] = [];\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  },\n  once(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    function onceHandler() {\n      self.off(events, onceHandler);\n      if (onceHandler.__emitterProxy) {\n        delete onceHandler.__emitterProxy;\n      }\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      handler.apply(self, args);\n    }\n    onceHandler.__emitterProxy = handler;\n    return self.on(events, onceHandler, priority);\n  },\n  onAny(handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    if (self.eventsAnyListeners.indexOf(handler) < 0) {\n      self.eventsAnyListeners[method](handler);\n    }\n    return self;\n  },\n  offAny(handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsAnyListeners) return self;\n    const index = self.eventsAnyListeners.indexOf(handler);\n    if (index >= 0) {\n      self.eventsAnyListeners.splice(index, 1);\n    }\n    return self;\n  },\n  off(events, handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    events.split(' ').forEach(event => {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else if (self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach((eventHandler, index) => {\n          if (eventHandler === handler || eventHandler.__emitterProxy && eventHandler.__emitterProxy === handler) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  },\n  emit() {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    let events;\n    let data;\n    let context;\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n    data.unshift(context);\n    const eventsArray = Array.isArray(events) ? events : events.split(' ');\n    eventsArray.forEach(event => {\n      if (self.eventsAnyListeners && self.eventsAnyListeners.length) {\n        self.eventsAnyListeners.forEach(eventHandler => {\n          eventHandler.apply(context, [event, ...data]);\n        });\n      }\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach(eventHandler => {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  }\n};\n\nfunction updateSize() {\n  const swiper = this;\n  let width;\n  let height;\n  const el = swiper.el;\n  if (typeof swiper.params.width !== 'undefined' && swiper.params.width !== null) {\n    width = swiper.params.width;\n  } else {\n    width = el.clientWidth;\n  }\n  if (typeof swiper.params.height !== 'undefined' && swiper.params.height !== null) {\n    height = swiper.params.height;\n  } else {\n    height = el.clientHeight;\n  }\n  if (width === 0 && swiper.isHorizontal() || height === 0 && swiper.isVertical()) {\n    return;\n  }\n\n  // Subtract paddings\n  width = width - parseInt(elementStyle(el, 'padding-left') || 0, 10) - parseInt(elementStyle(el, 'padding-right') || 0, 10);\n  height = height - parseInt(elementStyle(el, 'padding-top') || 0, 10) - parseInt(elementStyle(el, 'padding-bottom') || 0, 10);\n  if (Number.isNaN(width)) width = 0;\n  if (Number.isNaN(height)) height = 0;\n  Object.assign(swiper, {\n    width,\n    height,\n    size: swiper.isHorizontal() ? width : height\n  });\n}\n\nfunction updateSlides() {\n  const swiper = this;\n  function getDirectionPropertyValue(node, label) {\n    return parseFloat(node.getPropertyValue(swiper.getDirectionLabel(label)) || 0);\n  }\n  const params = swiper.params;\n  const {\n    wrapperEl,\n    slidesEl,\n    size: swiperSize,\n    rtlTranslate: rtl,\n    wrongRTL\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n  const slides = elementChildren(slidesEl, `.${swiper.params.slideClass}, swiper-slide`);\n  const slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n  let snapGrid = [];\n  const slidesGrid = [];\n  const slidesSizesGrid = [];\n  let offsetBefore = params.slidesOffsetBefore;\n  if (typeof offsetBefore === 'function') {\n    offsetBefore = params.slidesOffsetBefore.call(swiper);\n  }\n  let offsetAfter = params.slidesOffsetAfter;\n  if (typeof offsetAfter === 'function') {\n    offsetAfter = params.slidesOffsetAfter.call(swiper);\n  }\n  const previousSnapGridLength = swiper.snapGrid.length;\n  const previousSlidesGridLength = swiper.slidesGrid.length;\n  let spaceBetween = params.spaceBetween;\n  let slidePosition = -offsetBefore;\n  let prevSlideSize = 0;\n  let index = 0;\n  if (typeof swiperSize === 'undefined') {\n    return;\n  }\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiperSize;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  swiper.virtualSize = -spaceBetween;\n\n  // reset margins\n  slides.forEach(slideEl => {\n    if (rtl) {\n      slideEl.style.marginLeft = '';\n    } else {\n      slideEl.style.marginRight = '';\n    }\n    slideEl.style.marginBottom = '';\n    slideEl.style.marginTop = '';\n  });\n\n  // reset cssMode offsets\n  if (params.centeredSlides && params.cssMode) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', '');\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', '');\n  }\n  const gridEnabled = params.grid && params.grid.rows > 1 && swiper.grid;\n  if (gridEnabled) {\n    swiper.grid.initSlides(slides);\n  } else if (swiper.grid) {\n    swiper.grid.unsetSlides();\n  }\n\n  // Calc slides\n  let slideSize;\n  const shouldResetSlideSize = params.slidesPerView === 'auto' && params.breakpoints && Object.keys(params.breakpoints).filter(key => {\n    return typeof params.breakpoints[key].slidesPerView !== 'undefined';\n  }).length > 0;\n  for (let i = 0; i < slidesLength; i += 1) {\n    slideSize = 0;\n    let slide;\n    if (slides[i]) slide = slides[i];\n    if (gridEnabled) {\n      swiper.grid.updateSlide(i, slide, slides);\n    }\n    if (slides[i] && elementStyle(slide, 'display') === 'none') continue; // eslint-disable-line\n\n    if (params.slidesPerView === 'auto') {\n      if (shouldResetSlideSize) {\n        slides[i].style[swiper.getDirectionLabel('width')] = ``;\n      }\n      const slideStyles = getComputedStyle(slide);\n      const currentTransform = slide.style.transform;\n      const currentWebKitTransform = slide.style.webkitTransform;\n      if (currentTransform) {\n        slide.style.transform = 'none';\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = 'none';\n      }\n      if (params.roundLengths) {\n        slideSize = swiper.isHorizontal() ? elementOuterSize(slide, 'width', true) : elementOuterSize(slide, 'height', true);\n      } else {\n        // eslint-disable-next-line\n        const width = getDirectionPropertyValue(slideStyles, 'width');\n        const paddingLeft = getDirectionPropertyValue(slideStyles, 'padding-left');\n        const paddingRight = getDirectionPropertyValue(slideStyles, 'padding-right');\n        const marginLeft = getDirectionPropertyValue(slideStyles, 'margin-left');\n        const marginRight = getDirectionPropertyValue(slideStyles, 'margin-right');\n        const boxSizing = slideStyles.getPropertyValue('box-sizing');\n        if (boxSizing && boxSizing === 'border-box') {\n          slideSize = width + marginLeft + marginRight;\n        } else {\n          const {\n            clientWidth,\n            offsetWidth\n          } = slide;\n          slideSize = width + paddingLeft + paddingRight + marginLeft + marginRight + (offsetWidth - clientWidth);\n        }\n      }\n      if (currentTransform) {\n        slide.style.transform = currentTransform;\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = currentWebKitTransform;\n      }\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n    } else {\n      slideSize = (swiperSize - (params.slidesPerView - 1) * spaceBetween) / params.slidesPerView;\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n      if (slides[i]) {\n        slides[i].style[swiper.getDirectionLabel('width')] = `${slideSize}px`;\n      }\n    }\n    if (slides[i]) {\n      slides[i].swiperSlideSize = slideSize;\n    }\n    slidesSizesGrid.push(slideSize);\n    if (params.centeredSlides) {\n      slidePosition = slidePosition + slideSize / 2 + prevSlideSize / 2 + spaceBetween;\n      if (prevSlideSize === 0 && i !== 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (i === 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if (index % params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n    } else {\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if ((index - Math.min(swiper.params.slidesPerGroupSkip, index)) % swiper.params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n      slidePosition = slidePosition + slideSize + spaceBetween;\n    }\n    swiper.virtualSize += slideSize + spaceBetween;\n    prevSlideSize = slideSize;\n    index += 1;\n  }\n  swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n  if (rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n    wrapperEl.style.width = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (params.setWrapperSize) {\n    wrapperEl.style[swiper.getDirectionLabel('width')] = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (gridEnabled) {\n    swiper.grid.updateWrapperSize(slideSize, snapGrid);\n  }\n\n  // Remove last grid elements depending on width\n  if (!params.centeredSlides) {\n    const newSlidesGrid = [];\n    for (let i = 0; i < snapGrid.length; i += 1) {\n      let slidesGridItem = snapGrid[i];\n      if (params.roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n      if (snapGrid[i] <= swiper.virtualSize - swiperSize) {\n        newSlidesGrid.push(slidesGridItem);\n      }\n    }\n    snapGrid = newSlidesGrid;\n    if (Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) > 1) {\n      snapGrid.push(swiper.virtualSize - swiperSize);\n    }\n  }\n  if (isVirtual && params.loop) {\n    const size = slidesSizesGrid[0] + spaceBetween;\n    if (params.slidesPerGroup > 1) {\n      const groups = Math.ceil((swiper.virtual.slidesBefore + swiper.virtual.slidesAfter) / params.slidesPerGroup);\n      const groupSize = size * params.slidesPerGroup;\n      for (let i = 0; i < groups; i += 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + groupSize);\n      }\n    }\n    for (let i = 0; i < swiper.virtual.slidesBefore + swiper.virtual.slidesAfter; i += 1) {\n      if (params.slidesPerGroup === 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + size);\n      }\n      slidesGrid.push(slidesGrid[slidesGrid.length - 1] + size);\n      swiper.virtualSize += size;\n    }\n  }\n  if (snapGrid.length === 0) snapGrid = [0];\n  if (spaceBetween !== 0) {\n    const key = swiper.isHorizontal() && rtl ? 'marginLeft' : swiper.getDirectionLabel('marginRight');\n    slides.filter((_, slideIndex) => {\n      if (!params.cssMode || params.loop) return true;\n      if (slideIndex === slides.length - 1) {\n        return false;\n      }\n      return true;\n    }).forEach(slideEl => {\n      slideEl.style[key] = `${spaceBetween}px`;\n    });\n  }\n  if (params.centeredSlides && params.centeredSlidesBounds) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const maxSnap = allSlidesSize > swiperSize ? allSlidesSize - swiperSize : 0;\n    snapGrid = snapGrid.map(snap => {\n      if (snap <= 0) return -offsetBefore;\n      if (snap > maxSnap) return maxSnap + offsetAfter;\n      return snap;\n    });\n  }\n  if (params.centerInsufficientSlides) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const offsetSize = (params.slidesOffsetBefore || 0) + (params.slidesOffsetAfter || 0);\n    if (allSlidesSize + offsetSize < swiperSize) {\n      const allSlidesOffset = (swiperSize - allSlidesSize - offsetSize) / 2;\n      snapGrid.forEach((snap, snapIndex) => {\n        snapGrid[snapIndex] = snap - allSlidesOffset;\n      });\n      slidesGrid.forEach((snap, snapIndex) => {\n        slidesGrid[snapIndex] = snap + allSlidesOffset;\n      });\n    }\n  }\n  Object.assign(swiper, {\n    slides,\n    snapGrid,\n    slidesGrid,\n    slidesSizesGrid\n  });\n  if (params.centeredSlides && params.cssMode && !params.centeredSlidesBounds) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', `${-snapGrid[0]}px`);\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', `${swiper.size / 2 - slidesSizesGrid[slidesSizesGrid.length - 1] / 2}px`);\n    const addToSnapGrid = -swiper.snapGrid[0];\n    const addToSlidesGrid = -swiper.slidesGrid[0];\n    swiper.snapGrid = swiper.snapGrid.map(v => v + addToSnapGrid);\n    swiper.slidesGrid = swiper.slidesGrid.map(v => v + addToSlidesGrid);\n  }\n  if (slidesLength !== previousSlidesLength) {\n    swiper.emit('slidesLengthChange');\n  }\n  if (snapGrid.length !== previousSnapGridLength) {\n    if (swiper.params.watchOverflow) swiper.checkOverflow();\n    swiper.emit('snapGridLengthChange');\n  }\n  if (slidesGrid.length !== previousSlidesGridLength) {\n    swiper.emit('slidesGridLengthChange');\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  swiper.emit('slidesUpdated');\n  if (!isVirtual && !params.cssMode && (params.effect === 'slide' || params.effect === 'fade')) {\n    const backFaceHiddenClass = `${params.containerModifierClass}backface-hidden`;\n    const hasClassBackfaceClassAdded = swiper.el.classList.contains(backFaceHiddenClass);\n    if (slidesLength <= params.maxBackfaceHiddenSlides) {\n      if (!hasClassBackfaceClassAdded) swiper.el.classList.add(backFaceHiddenClass);\n    } else if (hasClassBackfaceClassAdded) {\n      swiper.el.classList.remove(backFaceHiddenClass);\n    }\n  }\n}\n\nfunction updateAutoHeight(speed) {\n  const swiper = this;\n  const activeSlides = [];\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  let newHeight = 0;\n  let i;\n  if (typeof speed === 'number') {\n    swiper.setTransition(speed);\n  } else if (speed === true) {\n    swiper.setTransition(swiper.params.speed);\n  }\n  const getSlideByIndex = index => {\n    if (isVirtual) {\n      return swiper.slides[swiper.getSlideIndexByData(index)];\n    }\n    return swiper.slides[index];\n  };\n  // Find slides currently in view\n  if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n    if (swiper.params.centeredSlides) {\n      (swiper.visibleSlides || []).forEach(slide => {\n        activeSlides.push(slide);\n      });\n    } else {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        const index = swiper.activeIndex + i;\n        if (index > swiper.slides.length && !isVirtual) break;\n        activeSlides.push(getSlideByIndex(index));\n      }\n    }\n  } else {\n    activeSlides.push(getSlideByIndex(swiper.activeIndex));\n  }\n\n  // Find new height from highest slide in view\n  for (i = 0; i < activeSlides.length; i += 1) {\n    if (typeof activeSlides[i] !== 'undefined') {\n      const height = activeSlides[i].offsetHeight;\n      newHeight = height > newHeight ? height : newHeight;\n    }\n  }\n\n  // Update Height\n  if (newHeight || newHeight === 0) swiper.wrapperEl.style.height = `${newHeight}px`;\n}\n\nfunction updateSlidesOffset() {\n  const swiper = this;\n  const slides = swiper.slides;\n  // eslint-disable-next-line\n  const minusOffset = swiper.isElement ? swiper.isHorizontal() ? swiper.wrapperEl.offsetLeft : swiper.wrapperEl.offsetTop : 0;\n  for (let i = 0; i < slides.length; i += 1) {\n    slides[i].swiperSlideOffset = (swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop) - minusOffset - swiper.cssOverflowAdjustment();\n  }\n}\n\nconst toggleSlideClasses$1 = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesProgress(translate) {\n  if (translate === void 0) {\n    translate = this && this.translate || 0;\n  }\n  const swiper = this;\n  const params = swiper.params;\n  const {\n    slides,\n    rtlTranslate: rtl,\n    snapGrid\n  } = swiper;\n  if (slides.length === 0) return;\n  if (typeof slides[0].swiperSlideOffset === 'undefined') swiper.updateSlidesOffset();\n  let offsetCenter = -translate;\n  if (rtl) offsetCenter = translate;\n  swiper.visibleSlidesIndexes = [];\n  swiper.visibleSlides = [];\n  let spaceBetween = params.spaceBetween;\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiper.size;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  for (let i = 0; i < slides.length; i += 1) {\n    const slide = slides[i];\n    let slideOffset = slide.swiperSlideOffset;\n    if (params.cssMode && params.centeredSlides) {\n      slideOffset -= slides[0].swiperSlideOffset;\n    }\n    const slideProgress = (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const originalSlideProgress = (offsetCenter - snapGrid[0] + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const slideBefore = -(offsetCenter - slideOffset);\n    const slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n    const isFullyVisible = slideBefore >= 0 && slideBefore <= swiper.size - swiper.slidesSizesGrid[i];\n    const isVisible = slideBefore >= 0 && slideBefore < swiper.size - 1 || slideAfter > 1 && slideAfter <= swiper.size || slideBefore <= 0 && slideAfter >= swiper.size;\n    if (isVisible) {\n      swiper.visibleSlides.push(slide);\n      swiper.visibleSlidesIndexes.push(i);\n    }\n    toggleSlideClasses$1(slide, isVisible, params.slideVisibleClass);\n    toggleSlideClasses$1(slide, isFullyVisible, params.slideFullyVisibleClass);\n    slide.progress = rtl ? -slideProgress : slideProgress;\n    slide.originalProgress = rtl ? -originalSlideProgress : originalSlideProgress;\n  }\n}\n\nfunction updateProgress(translate) {\n  const swiper = this;\n  if (typeof translate === 'undefined') {\n    const multiplier = swiper.rtlTranslate ? -1 : 1;\n    // eslint-disable-next-line\n    translate = swiper && swiper.translate && swiper.translate * multiplier || 0;\n  }\n  const params = swiper.params;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  let {\n    progress,\n    isBeginning,\n    isEnd,\n    progressLoop\n  } = swiper;\n  const wasBeginning = isBeginning;\n  const wasEnd = isEnd;\n  if (translatesDiff === 0) {\n    progress = 0;\n    isBeginning = true;\n    isEnd = true;\n  } else {\n    progress = (translate - swiper.minTranslate()) / translatesDiff;\n    const isBeginningRounded = Math.abs(translate - swiper.minTranslate()) < 1;\n    const isEndRounded = Math.abs(translate - swiper.maxTranslate()) < 1;\n    isBeginning = isBeginningRounded || progress <= 0;\n    isEnd = isEndRounded || progress >= 1;\n    if (isBeginningRounded) progress = 0;\n    if (isEndRounded) progress = 1;\n  }\n  if (params.loop) {\n    const firstSlideIndex = swiper.getSlideIndexByData(0);\n    const lastSlideIndex = swiper.getSlideIndexByData(swiper.slides.length - 1);\n    const firstSlideTranslate = swiper.slidesGrid[firstSlideIndex];\n    const lastSlideTranslate = swiper.slidesGrid[lastSlideIndex];\n    const translateMax = swiper.slidesGrid[swiper.slidesGrid.length - 1];\n    const translateAbs = Math.abs(translate);\n    if (translateAbs >= firstSlideTranslate) {\n      progressLoop = (translateAbs - firstSlideTranslate) / translateMax;\n    } else {\n      progressLoop = (translateAbs + translateMax - lastSlideTranslate) / translateMax;\n    }\n    if (progressLoop > 1) progressLoop -= 1;\n  }\n  Object.assign(swiper, {\n    progress,\n    progressLoop,\n    isBeginning,\n    isEnd\n  });\n  if (params.watchSlidesProgress || params.centeredSlides && params.autoHeight) swiper.updateSlidesProgress(translate);\n  if (isBeginning && !wasBeginning) {\n    swiper.emit('reachBeginning toEdge');\n  }\n  if (isEnd && !wasEnd) {\n    swiper.emit('reachEnd toEdge');\n  }\n  if (wasBeginning && !isBeginning || wasEnd && !isEnd) {\n    swiper.emit('fromEdge');\n  }\n  swiper.emit('progress', progress);\n}\n\nconst toggleSlideClasses = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesClasses() {\n  const swiper = this;\n  const {\n    slides,\n    params,\n    slidesEl,\n    activeIndex\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  const getFilteredSlide = selector => {\n    return elementChildren(slidesEl, `.${params.slideClass}${selector}, swiper-slide${selector}`)[0];\n  };\n  let activeSlide;\n  let prevSlide;\n  let nextSlide;\n  if (isVirtual) {\n    if (params.loop) {\n      let slideIndex = activeIndex - swiper.virtual.slidesBefore;\n      if (slideIndex < 0) slideIndex = swiper.virtual.slides.length + slideIndex;\n      if (slideIndex >= swiper.virtual.slides.length) slideIndex -= swiper.virtual.slides.length;\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${slideIndex}\"]`);\n    } else {\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${activeIndex}\"]`);\n    }\n  } else {\n    if (gridEnabled) {\n      activeSlide = slides.find(slideEl => slideEl.column === activeIndex);\n      nextSlide = slides.find(slideEl => slideEl.column === activeIndex + 1);\n      prevSlide = slides.find(slideEl => slideEl.column === activeIndex - 1);\n    } else {\n      activeSlide = slides[activeIndex];\n    }\n  }\n  if (activeSlide) {\n    if (!gridEnabled) {\n      // Next Slide\n      nextSlide = elementNextAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n      if (params.loop && !nextSlide) {\n        nextSlide = slides[0];\n      }\n\n      // Prev Slide\n      prevSlide = elementPrevAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n      if (params.loop && !prevSlide === 0) {\n        prevSlide = slides[slides.length - 1];\n      }\n    }\n  }\n  slides.forEach(slideEl => {\n    toggleSlideClasses(slideEl, slideEl === activeSlide, params.slideActiveClass);\n    toggleSlideClasses(slideEl, slideEl === nextSlide, params.slideNextClass);\n    toggleSlideClasses(slideEl, slideEl === prevSlide, params.slidePrevClass);\n  });\n  swiper.emitSlidesClasses();\n}\n\nconst processLazyPreloader = (swiper, imageEl) => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  const slideSelector = () => swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`;\n  const slideEl = imageEl.closest(slideSelector());\n  if (slideEl) {\n    let lazyEl = slideEl.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n    if (!lazyEl && swiper.isElement) {\n      if (slideEl.shadowRoot) {\n        lazyEl = slideEl.shadowRoot.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n      } else {\n        // init later\n        requestAnimationFrame(() => {\n          if (slideEl.shadowRoot) {\n            lazyEl = slideEl.shadowRoot.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n            if (lazyEl) lazyEl.remove();\n          }\n        });\n      }\n    }\n    if (lazyEl) lazyEl.remove();\n  }\n};\nconst unlazy = (swiper, index) => {\n  if (!swiper.slides[index]) return;\n  const imageEl = swiper.slides[index].querySelector('[loading=\"lazy\"]');\n  if (imageEl) imageEl.removeAttribute('loading');\n};\nconst preload = swiper => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  let amount = swiper.params.lazyPreloadPrevNext;\n  const len = swiper.slides.length;\n  if (!len || !amount || amount < 0) return;\n  amount = Math.min(amount, len);\n  const slidesPerView = swiper.params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(swiper.params.slidesPerView);\n  const activeIndex = swiper.activeIndex;\n  if (swiper.params.grid && swiper.params.grid.rows > 1) {\n    const activeColumn = activeIndex;\n    const preloadColumns = [activeColumn - amount];\n    preloadColumns.push(...Array.from({\n      length: amount\n    }).map((_, i) => {\n      return activeColumn + slidesPerView + i;\n    }));\n    swiper.slides.forEach((slideEl, i) => {\n      if (preloadColumns.includes(slideEl.column)) unlazy(swiper, i);\n    });\n    return;\n  }\n  const slideIndexLastInView = activeIndex + slidesPerView - 1;\n  if (swiper.params.rewind || swiper.params.loop) {\n    for (let i = activeIndex - amount; i <= slideIndexLastInView + amount; i += 1) {\n      const realIndex = (i % len + len) % len;\n      if (realIndex < activeIndex || realIndex > slideIndexLastInView) unlazy(swiper, realIndex);\n    }\n  } else {\n    for (let i = Math.max(activeIndex - amount, 0); i <= Math.min(slideIndexLastInView + amount, len - 1); i += 1) {\n      if (i !== activeIndex && (i > slideIndexLastInView || i < activeIndex)) {\n        unlazy(swiper, i);\n      }\n    }\n  }\n};\n\nfunction getActiveIndexByTranslate(swiper) {\n  const {\n    slidesGrid,\n    params\n  } = swiper;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  let activeIndex;\n  for (let i = 0; i < slidesGrid.length; i += 1) {\n    if (typeof slidesGrid[i + 1] !== 'undefined') {\n      if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1] - (slidesGrid[i + 1] - slidesGrid[i]) / 2) {\n        activeIndex = i;\n      } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n        activeIndex = i + 1;\n      }\n    } else if (translate >= slidesGrid[i]) {\n      activeIndex = i;\n    }\n  }\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    if (activeIndex < 0 || typeof activeIndex === 'undefined') activeIndex = 0;\n  }\n  return activeIndex;\n}\nfunction updateActiveIndex(newActiveIndex) {\n  const swiper = this;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  const {\n    snapGrid,\n    params,\n    activeIndex: previousIndex,\n    realIndex: previousRealIndex,\n    snapIndex: previousSnapIndex\n  } = swiper;\n  let activeIndex = newActiveIndex;\n  let snapIndex;\n  const getVirtualRealIndex = aIndex => {\n    let realIndex = aIndex - swiper.virtual.slidesBefore;\n    if (realIndex < 0) {\n      realIndex = swiper.virtual.slides.length + realIndex;\n    }\n    if (realIndex >= swiper.virtual.slides.length) {\n      realIndex -= swiper.virtual.slides.length;\n    }\n    return realIndex;\n  };\n  if (typeof activeIndex === 'undefined') {\n    activeIndex = getActiveIndexByTranslate(swiper);\n  }\n  if (snapGrid.indexOf(translate) >= 0) {\n    snapIndex = snapGrid.indexOf(translate);\n  } else {\n    const skip = Math.min(params.slidesPerGroupSkip, activeIndex);\n    snapIndex = skip + Math.floor((activeIndex - skip) / params.slidesPerGroup);\n  }\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  if (activeIndex === previousIndex && !swiper.params.loop) {\n    if (snapIndex !== previousSnapIndex) {\n      swiper.snapIndex = snapIndex;\n      swiper.emit('snapIndexChange');\n    }\n    return;\n  }\n  if (activeIndex === previousIndex && swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n    swiper.realIndex = getVirtualRealIndex(activeIndex);\n    return;\n  }\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n\n  // Get real index\n  let realIndex;\n  if (swiper.virtual && params.virtual.enabled && params.loop) {\n    realIndex = getVirtualRealIndex(activeIndex);\n  } else if (gridEnabled) {\n    const firstSlideInColumn = swiper.slides.find(slideEl => slideEl.column === activeIndex);\n    let activeSlideIndex = parseInt(firstSlideInColumn.getAttribute('data-swiper-slide-index'), 10);\n    if (Number.isNaN(activeSlideIndex)) {\n      activeSlideIndex = Math.max(swiper.slides.indexOf(firstSlideInColumn), 0);\n    }\n    realIndex = Math.floor(activeSlideIndex / params.grid.rows);\n  } else if (swiper.slides[activeIndex]) {\n    const slideIndex = swiper.slides[activeIndex].getAttribute('data-swiper-slide-index');\n    if (slideIndex) {\n      realIndex = parseInt(slideIndex, 10);\n    } else {\n      realIndex = activeIndex;\n    }\n  } else {\n    realIndex = activeIndex;\n  }\n  Object.assign(swiper, {\n    previousSnapIndex,\n    snapIndex,\n    previousRealIndex,\n    realIndex,\n    previousIndex,\n    activeIndex\n  });\n  if (swiper.initialized) {\n    preload(swiper);\n  }\n  swiper.emit('activeIndexChange');\n  swiper.emit('snapIndexChange');\n  if (swiper.initialized || swiper.params.runCallbacksOnInit) {\n    if (previousRealIndex !== realIndex) {\n      swiper.emit('realIndexChange');\n    }\n    swiper.emit('slideChange');\n  }\n}\n\nfunction updateClickedSlide(el, path) {\n  const swiper = this;\n  const params = swiper.params;\n  let slide = el.closest(`.${params.slideClass}, swiper-slide`);\n  if (!slide && swiper.isElement && path && path.length > 1 && path.includes(el)) {\n    [...path.slice(path.indexOf(el) + 1, path.length)].forEach(pathEl => {\n      if (!slide && pathEl.matches && pathEl.matches(`.${params.slideClass}, swiper-slide`)) {\n        slide = pathEl;\n      }\n    });\n  }\n  let slideFound = false;\n  let slideIndex;\n  if (slide) {\n    for (let i = 0; i < swiper.slides.length; i += 1) {\n      if (swiper.slides[i] === slide) {\n        slideFound = true;\n        slideIndex = i;\n        break;\n      }\n    }\n  }\n  if (slide && slideFound) {\n    swiper.clickedSlide = slide;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.clickedIndex = parseInt(slide.getAttribute('data-swiper-slide-index'), 10);\n    } else {\n      swiper.clickedIndex = slideIndex;\n    }\n  } else {\n    swiper.clickedSlide = undefined;\n    swiper.clickedIndex = undefined;\n    return;\n  }\n  if (params.slideToClickedSlide && swiper.clickedIndex !== undefined && swiper.clickedIndex !== swiper.activeIndex) {\n    swiper.slideToClickedSlide();\n  }\n}\n\nvar update = {\n  updateSize,\n  updateSlides,\n  updateAutoHeight,\n  updateSlidesOffset,\n  updateSlidesProgress,\n  updateProgress,\n  updateSlidesClasses,\n  updateActiveIndex,\n  updateClickedSlide\n};\n\nfunction getSwiperTranslate(axis) {\n  if (axis === void 0) {\n    axis = this.isHorizontal() ? 'x' : 'y';\n  }\n  const swiper = this;\n  const {\n    params,\n    rtlTranslate: rtl,\n    translate,\n    wrapperEl\n  } = swiper;\n  if (params.virtualTranslate) {\n    return rtl ? -translate : translate;\n  }\n  if (params.cssMode) {\n    return translate;\n  }\n  let currentTranslate = getTranslate(wrapperEl, axis);\n  currentTranslate += swiper.cssOverflowAdjustment();\n  if (rtl) currentTranslate = -currentTranslate;\n  return currentTranslate || 0;\n}\n\nfunction setTranslate(translate, byController) {\n  const swiper = this;\n  const {\n    rtlTranslate: rtl,\n    params,\n    wrapperEl,\n    progress\n  } = swiper;\n  let x = 0;\n  let y = 0;\n  const z = 0;\n  if (swiper.isHorizontal()) {\n    x = rtl ? -translate : translate;\n  } else {\n    y = translate;\n  }\n  if (params.roundLengths) {\n    x = Math.floor(x);\n    y = Math.floor(y);\n  }\n  swiper.previousTranslate = swiper.translate;\n  swiper.translate = swiper.isHorizontal() ? x : y;\n  if (params.cssMode) {\n    wrapperEl[swiper.isHorizontal() ? 'scrollLeft' : 'scrollTop'] = swiper.isHorizontal() ? -x : -y;\n  } else if (!params.virtualTranslate) {\n    if (swiper.isHorizontal()) {\n      x -= swiper.cssOverflowAdjustment();\n    } else {\n      y -= swiper.cssOverflowAdjustment();\n    }\n    wrapperEl.style.transform = `translate3d(${x}px, ${y}px, ${z}px)`;\n  }\n\n  // Check if we need to update progress\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== progress) {\n    swiper.updateProgress(translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, byController);\n}\n\nfunction minTranslate() {\n  return -this.snapGrid[0];\n}\n\nfunction maxTranslate() {\n  return -this.snapGrid[this.snapGrid.length - 1];\n}\n\nfunction translateTo(translate, speed, runCallbacks, translateBounds, internal) {\n  if (translate === void 0) {\n    translate = 0;\n  }\n  if (speed === void 0) {\n    speed = this.params.speed;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (translateBounds === void 0) {\n    translateBounds = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    wrapperEl\n  } = swiper;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  const minTranslate = swiper.minTranslate();\n  const maxTranslate = swiper.maxTranslate();\n  let newTranslate;\n  if (translateBounds && translate > minTranslate) newTranslate = minTranslate;else if (translateBounds && translate < maxTranslate) newTranslate = maxTranslate;else newTranslate = translate;\n\n  // Update progress\n  swiper.updateProgress(newTranslate);\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    if (speed === 0) {\n      wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = -newTranslate;\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: -newTranslate,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: -newTranslate,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  if (speed === 0) {\n    swiper.setTransition(0);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionEnd');\n    }\n  } else {\n    swiper.setTransition(speed);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionStart');\n    }\n    if (!swiper.animating) {\n      swiper.animating = true;\n      if (!swiper.onTranslateToWrapperTransitionEnd) {\n        swiper.onTranslateToWrapperTransitionEnd = function transitionEnd(e) {\n          if (!swiper || swiper.destroyed) return;\n          if (e.target !== this) return;\n          swiper.wrapperEl.removeEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n          swiper.onTranslateToWrapperTransitionEnd = null;\n          delete swiper.onTranslateToWrapperTransitionEnd;\n          swiper.animating = false;\n          if (runCallbacks) {\n            swiper.emit('transitionEnd');\n          }\n        };\n      }\n      swiper.wrapperEl.addEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n    }\n  }\n  return true;\n}\n\nvar translate = {\n  getTranslate: getSwiperTranslate,\n  setTranslate,\n  minTranslate,\n  maxTranslate,\n  translateTo\n};\n\nfunction setTransition(duration, byController) {\n  const swiper = this;\n  if (!swiper.params.cssMode) {\n    swiper.wrapperEl.style.transitionDuration = `${duration}ms`;\n    swiper.wrapperEl.style.transitionDelay = duration === 0 ? `0ms` : '';\n  }\n  swiper.emit('setTransition', duration, byController);\n}\n\nfunction transitionEmit(_ref) {\n  let {\n    swiper,\n    runCallbacks,\n    direction,\n    step\n  } = _ref;\n  const {\n    activeIndex,\n    previousIndex\n  } = swiper;\n  let dir = direction;\n  if (!dir) {\n    if (activeIndex > previousIndex) dir = 'next';else if (activeIndex < previousIndex) dir = 'prev';else dir = 'reset';\n  }\n  swiper.emit(`transition${step}`);\n  if (runCallbacks && dir === 'reset') {\n    swiper.emit(`slideResetTransition${step}`);\n  } else if (runCallbacks && activeIndex !== previousIndex) {\n    swiper.emit(`slideChangeTransition${step}`);\n    if (dir === 'next') {\n      swiper.emit(`slideNextTransition${step}`);\n    } else {\n      swiper.emit(`slidePrevTransition${step}`);\n    }\n  }\n}\n\nfunction transitionStart(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  if (params.cssMode) return;\n  if (params.autoHeight) {\n    swiper.updateAutoHeight();\n  }\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'Start'\n  });\n}\n\nfunction transitionEnd(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.animating = false;\n  if (params.cssMode) return;\n  swiper.setTransition(0);\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'End'\n  });\n}\n\nvar transition = {\n  setTransition,\n  transitionStart,\n  transitionEnd\n};\n\nfunction slideTo(index, speed, runCallbacks, internal, initial) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    index = parseInt(index, 10);\n  }\n  const swiper = this;\n  let slideIndex = index;\n  if (slideIndex < 0) slideIndex = 0;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    previousIndex,\n    activeIndex,\n    rtlTranslate: rtl,\n    wrapperEl,\n    enabled\n  } = swiper;\n  if (!enabled && !internal && !initial || swiper.destroyed || swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, slideIndex);\n  let snapIndex = skip + Math.floor((slideIndex - skip) / swiper.params.slidesPerGroup);\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  const translate = -snapGrid[snapIndex];\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    for (let i = 0; i < slidesGrid.length; i += 1) {\n      const normalizedTranslate = -Math.floor(translate * 100);\n      const normalizedGrid = Math.floor(slidesGrid[i] * 100);\n      const normalizedGridNext = Math.floor(slidesGrid[i + 1] * 100);\n      if (typeof slidesGrid[i + 1] !== 'undefined') {\n        if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext - (normalizedGridNext - normalizedGrid) / 2) {\n          slideIndex = i;\n        } else if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext) {\n          slideIndex = i + 1;\n        }\n      } else if (normalizedTranslate >= normalizedGrid) {\n        slideIndex = i;\n      }\n    }\n  }\n  // Directions locks\n  if (swiper.initialized && slideIndex !== activeIndex) {\n    if (!swiper.allowSlideNext && (rtl ? translate > swiper.translate && translate > swiper.minTranslate() : translate < swiper.translate && translate < swiper.minTranslate())) {\n      return false;\n    }\n    if (!swiper.allowSlidePrev && translate > swiper.translate && translate > swiper.maxTranslate()) {\n      if ((activeIndex || 0) !== slideIndex) {\n        return false;\n      }\n    }\n  }\n  if (slideIndex !== (previousIndex || 0) && runCallbacks) {\n    swiper.emit('beforeSlideChangeStart');\n  }\n\n  // Update progress\n  swiper.updateProgress(translate);\n  let direction;\n  if (slideIndex > activeIndex) direction = 'next';else if (slideIndex < activeIndex) direction = 'prev';else direction = 'reset';\n\n  // initial virtual\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  const isInitialVirtual = isVirtual && initial;\n  // Update Index\n  if (!isInitialVirtual && (rtl && -translate === swiper.translate || !rtl && translate === swiper.translate)) {\n    swiper.updateActiveIndex(slideIndex);\n    // Update Height\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n    swiper.updateSlidesClasses();\n    if (params.effect !== 'slide') {\n      swiper.setTranslate(translate);\n    }\n    if (direction !== 'reset') {\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    }\n    return false;\n  }\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    const t = rtl ? translate : -translate;\n    if (speed === 0) {\n      if (isVirtual) {\n        swiper.wrapperEl.style.scrollSnapType = 'none';\n        swiper._immediateVirtual = true;\n      }\n      if (isVirtual && !swiper._cssModeVirtualInitialSet && swiper.params.initialSlide > 0) {\n        swiper._cssModeVirtualInitialSet = true;\n        requestAnimationFrame(() => {\n          wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n        });\n      } else {\n        wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n      }\n      if (isVirtual) {\n        requestAnimationFrame(() => {\n          swiper.wrapperEl.style.scrollSnapType = '';\n          swiper._immediateVirtual = false;\n        });\n      }\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: t,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: t,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  const browser = getBrowser();\n  const isSafari = browser.isSafari;\n  if (isVirtual && !initial && isSafari && swiper.isElement) {\n    swiper.virtual.update(false, false, slideIndex);\n  }\n  swiper.setTransition(speed);\n  swiper.setTranslate(translate);\n  swiper.updateActiveIndex(slideIndex);\n  swiper.updateSlidesClasses();\n  swiper.emit('beforeTransitionStart', speed, internal);\n  swiper.transitionStart(runCallbacks, direction);\n  if (speed === 0) {\n    swiper.transitionEnd(runCallbacks, direction);\n  } else if (!swiper.animating) {\n    swiper.animating = true;\n    if (!swiper.onSlideToWrapperTransitionEnd) {\n      swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n        if (!swiper || swiper.destroyed) return;\n        if (e.target !== this) return;\n        swiper.wrapperEl.removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n        swiper.onSlideToWrapperTransitionEnd = null;\n        delete swiper.onSlideToWrapperTransitionEnd;\n        swiper.transitionEnd(runCallbacks, direction);\n      };\n    }\n    swiper.wrapperEl.addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n  }\n  return true;\n}\n\nfunction slideToLoop(index, speed, runCallbacks, internal) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    const indexAsNumber = parseInt(index, 10);\n    index = indexAsNumber;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const gridEnabled = swiper.grid && swiper.params.grid && swiper.params.grid.rows > 1;\n  let newIndex = index;\n  if (swiper.params.loop) {\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      // eslint-disable-next-line\n      newIndex = newIndex + swiper.virtual.slidesBefore;\n    } else {\n      let targetSlideIndex;\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        targetSlideIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        targetSlideIndex = swiper.getSlideIndexByData(newIndex);\n      }\n      const cols = gridEnabled ? Math.ceil(swiper.slides.length / swiper.params.grid.rows) : swiper.slides.length;\n      const {\n        centeredSlides\n      } = swiper.params;\n      let slidesPerView = swiper.params.slidesPerView;\n      if (slidesPerView === 'auto') {\n        slidesPerView = swiper.slidesPerViewDynamic();\n      } else {\n        slidesPerView = Math.ceil(parseFloat(swiper.params.slidesPerView, 10));\n        if (centeredSlides && slidesPerView % 2 === 0) {\n          slidesPerView = slidesPerView + 1;\n        }\n      }\n      let needLoopFix = cols - targetSlideIndex < slidesPerView;\n      if (centeredSlides) {\n        needLoopFix = needLoopFix || targetSlideIndex < Math.ceil(slidesPerView / 2);\n      }\n      if (internal && centeredSlides && swiper.params.slidesPerView !== 'auto' && !gridEnabled) {\n        needLoopFix = false;\n      }\n      if (needLoopFix) {\n        const direction = centeredSlides ? targetSlideIndex < swiper.activeIndex ? 'prev' : 'next' : targetSlideIndex - swiper.activeIndex - 1 < swiper.params.slidesPerView ? 'next' : 'prev';\n        swiper.loopFix({\n          direction,\n          slideTo: true,\n          activeSlideIndex: direction === 'next' ? targetSlideIndex + 1 : targetSlideIndex - cols + 1,\n          slideRealIndex: direction === 'next' ? swiper.realIndex : undefined\n        });\n      }\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        newIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        newIndex = swiper.getSlideIndexByData(newIndex);\n      }\n    }\n  }\n  requestAnimationFrame(() => {\n    swiper.slideTo(newIndex, speed, runCallbacks, internal);\n  });\n  return swiper;\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideNext(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    enabled,\n    params,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let perGroup = params.slidesPerGroup;\n  if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n    perGroup = Math.max(swiper.slidesPerViewDynamic('current', true), 1);\n  }\n  const increment = swiper.activeIndex < params.slidesPerGroupSkip ? 1 : perGroup;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'next'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n    if (swiper.activeIndex === swiper.slides.length - 1 && params.cssMode) {\n      requestAnimationFrame(() => {\n        swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n      });\n      return true;\n    }\n  }\n  if (params.rewind && swiper.isEnd) {\n    return swiper.slideTo(0, speed, runCallbacks, internal);\n  }\n  return swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slidePrev(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    rtlTranslate,\n    enabled,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'prev'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n  }\n  const translate = rtlTranslate ? swiper.translate : -swiper.translate;\n  function normalize(val) {\n    if (val < 0) return -Math.floor(Math.abs(val));\n    return Math.floor(val);\n  }\n  const normalizedTranslate = normalize(translate);\n  const normalizedSnapGrid = snapGrid.map(val => normalize(val));\n  const isFreeMode = params.freeMode && params.freeMode.enabled;\n  let prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n  if (typeof prevSnap === 'undefined' && (params.cssMode || isFreeMode)) {\n    let prevSnapIndex;\n    snapGrid.forEach((snap, snapIndex) => {\n      if (normalizedTranslate >= snap) {\n        // prevSnap = snap;\n        prevSnapIndex = snapIndex;\n      }\n    });\n    if (typeof prevSnapIndex !== 'undefined') {\n      prevSnap = isFreeMode ? snapGrid[prevSnapIndex] : snapGrid[prevSnapIndex > 0 ? prevSnapIndex - 1 : prevSnapIndex];\n    }\n  }\n  let prevIndex = 0;\n  if (typeof prevSnap !== 'undefined') {\n    prevIndex = slidesGrid.indexOf(prevSnap);\n    if (prevIndex < 0) prevIndex = swiper.activeIndex - 1;\n    if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n      prevIndex = prevIndex - swiper.slidesPerViewDynamic('previous', true) + 1;\n      prevIndex = Math.max(prevIndex, 0);\n    }\n  }\n  if (params.rewind && swiper.isBeginning) {\n    const lastIndex = swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    return swiper.slideTo(lastIndex, speed, runCallbacks, internal);\n  } else if (params.loop && swiper.activeIndex === 0 && params.cssMode) {\n    requestAnimationFrame(() => {\n      swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n    });\n    return true;\n  }\n  return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideReset(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideToClosest(speed, runCallbacks, internal, threshold) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (threshold === void 0) {\n    threshold = 0.5;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let index = swiper.activeIndex;\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, index);\n  const snapIndex = skip + Math.floor((index - skip) / swiper.params.slidesPerGroup);\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  if (translate >= swiper.snapGrid[snapIndex]) {\n    // The current translate is on or after the current snap index, so the choice\n    // is between the current index and the one after it.\n    const currentSnap = swiper.snapGrid[snapIndex];\n    const nextSnap = swiper.snapGrid[snapIndex + 1];\n    if (translate - currentSnap > (nextSnap - currentSnap) * threshold) {\n      index += swiper.params.slidesPerGroup;\n    }\n  } else {\n    // The current translate is before the current snap index, so the choice\n    // is between the current index and the one before it.\n    const prevSnap = swiper.snapGrid[snapIndex - 1];\n    const currentSnap = swiper.snapGrid[snapIndex];\n    if (translate - prevSnap <= (currentSnap - prevSnap) * threshold) {\n      index -= swiper.params.slidesPerGroup;\n    }\n  }\n  index = Math.max(index, 0);\n  index = Math.min(index, swiper.slidesGrid.length - 1);\n  return swiper.slideTo(index, speed, runCallbacks, internal);\n}\n\nfunction slideToClickedSlide() {\n  const swiper = this;\n  if (swiper.destroyed) return;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  const slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n  let slideToIndex = swiper.getSlideIndexWhenGrid(swiper.clickedIndex);\n  let realIndex;\n  const slideSelector = swiper.isElement ? `swiper-slide` : `.${params.slideClass}`;\n  const isGrid = swiper.grid && swiper.params.grid && swiper.params.grid.rows > 1;\n  if (params.loop) {\n    if (swiper.animating) return;\n    realIndex = parseInt(swiper.clickedSlide.getAttribute('data-swiper-slide-index'), 10);\n    if (params.centeredSlides) {\n      swiper.slideToLoop(realIndex);\n    } else if (slideToIndex > (isGrid ? (swiper.slides.length - slidesPerView) / 2 - (swiper.params.grid.rows - 1) : swiper.slides.length - slidesPerView)) {\n      swiper.loopFix();\n      slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0]);\n      nextTick(() => {\n        swiper.slideTo(slideToIndex);\n      });\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  } else {\n    swiper.slideTo(slideToIndex);\n  }\n}\n\nvar slide = {\n  slideTo,\n  slideToLoop,\n  slideNext,\n  slidePrev,\n  slideReset,\n  slideToClosest,\n  slideToClickedSlide\n};\n\nfunction loopCreate(slideRealIndex, initial) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || swiper.virtual && swiper.params.virtual.enabled) return;\n  const initSlides = () => {\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    slides.forEach((el, index) => {\n      el.setAttribute('data-swiper-slide-index', index);\n    });\n  };\n  const clearBlankSlides = () => {\n    const slides = elementChildren(slidesEl, `.${params.slideBlankClass}`);\n    slides.forEach(el => {\n      el.remove();\n    });\n    if (slides.length > 0) {\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    }\n  };\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  if (params.loopAddBlankSlides && (params.slidesPerGroup > 1 || gridEnabled)) {\n    clearBlankSlides();\n  }\n  const slidesPerGroup = params.slidesPerGroup * (gridEnabled ? params.grid.rows : 1);\n  const shouldFillGroup = swiper.slides.length % slidesPerGroup !== 0;\n  const shouldFillGrid = gridEnabled && swiper.slides.length % params.grid.rows !== 0;\n  const addBlankSlides = amountOfSlides => {\n    for (let i = 0; i < amountOfSlides; i += 1) {\n      const slideEl = swiper.isElement ? createElement('swiper-slide', [params.slideBlankClass]) : createElement('div', [params.slideClass, params.slideBlankClass]);\n      swiper.slidesEl.append(slideEl);\n    }\n  };\n  if (shouldFillGroup) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = slidesPerGroup - swiper.slides.length % slidesPerGroup;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else if (shouldFillGrid) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = params.grid.rows - swiper.slides.length % params.grid.rows;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else {\n    initSlides();\n  }\n  swiper.loopFix({\n    slideRealIndex,\n    direction: params.centeredSlides ? undefined : 'next',\n    initial\n  });\n}\n\nfunction loopFix(_temp) {\n  let {\n    slideRealIndex,\n    slideTo = true,\n    direction,\n    setTranslate,\n    activeSlideIndex,\n    initial,\n    byController,\n    byMousewheel\n  } = _temp === void 0 ? {} : _temp;\n  const swiper = this;\n  if (!swiper.params.loop) return;\n  swiper.emit('beforeLoopFix');\n  const {\n    slides,\n    allowSlidePrev,\n    allowSlideNext,\n    slidesEl,\n    params\n  } = swiper;\n  const {\n    centeredSlides,\n    initialSlide\n  } = params;\n  swiper.allowSlidePrev = true;\n  swiper.allowSlideNext = true;\n  if (swiper.virtual && params.virtual.enabled) {\n    if (slideTo) {\n      if (!params.centeredSlides && swiper.snapIndex === 0) {\n        swiper.slideTo(swiper.virtual.slides.length, 0, false, true);\n      } else if (params.centeredSlides && swiper.snapIndex < params.slidesPerView) {\n        swiper.slideTo(swiper.virtual.slides.length + swiper.snapIndex, 0, false, true);\n      } else if (swiper.snapIndex === swiper.snapGrid.length - 1) {\n        swiper.slideTo(swiper.virtual.slidesBefore, 0, false, true);\n      }\n    }\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n    swiper.emit('loopFix');\n    return;\n  }\n  let slidesPerView = params.slidesPerView;\n  if (slidesPerView === 'auto') {\n    slidesPerView = swiper.slidesPerViewDynamic();\n  } else {\n    slidesPerView = Math.ceil(parseFloat(params.slidesPerView, 10));\n    if (centeredSlides && slidesPerView % 2 === 0) {\n      slidesPerView = slidesPerView + 1;\n    }\n  }\n  const slidesPerGroup = params.slidesPerGroupAuto ? slidesPerView : params.slidesPerGroup;\n  let loopedSlides = centeredSlides ? Math.max(slidesPerGroup, Math.ceil(slidesPerView / 2)) : slidesPerGroup;\n  if (loopedSlides % slidesPerGroup !== 0) {\n    loopedSlides += slidesPerGroup - loopedSlides % slidesPerGroup;\n  }\n  loopedSlides += params.loopAdditionalSlides;\n  swiper.loopedSlides = loopedSlides;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  if (slides.length < slidesPerView + loopedSlides || swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    showWarning('Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters');\n  } else if (gridEnabled && params.grid.fill === 'row') {\n    showWarning('Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`');\n  }\n  const prependSlidesIndexes = [];\n  const appendSlidesIndexes = [];\n  const cols = gridEnabled ? Math.ceil(slides.length / params.grid.rows) : slides.length;\n  const isInitialOverflow = initial && cols - initialSlide < slidesPerView && !centeredSlides;\n  let activeIndex = isInitialOverflow ? initialSlide : swiper.activeIndex;\n  if (typeof activeSlideIndex === 'undefined') {\n    activeSlideIndex = swiper.getSlideIndex(slides.find(el => el.classList.contains(params.slideActiveClass)));\n  } else {\n    activeIndex = activeSlideIndex;\n  }\n  const isNext = direction === 'next' || !direction;\n  const isPrev = direction === 'prev' || !direction;\n  let slidesPrepended = 0;\n  let slidesAppended = 0;\n  const activeColIndex = gridEnabled ? slides[activeSlideIndex].column : activeSlideIndex;\n  const activeColIndexWithShift = activeColIndex + (centeredSlides && typeof setTranslate === 'undefined' ? -slidesPerView / 2 + 0.5 : 0);\n  // prepend last slides before start\n  if (activeColIndexWithShift < loopedSlides) {\n    slidesPrepended = Math.max(loopedSlides - activeColIndexWithShift, slidesPerGroup);\n    for (let i = 0; i < loopedSlides - activeColIndexWithShift; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        const colIndexToPrepend = cols - index - 1;\n        for (let i = slides.length - 1; i >= 0; i -= 1) {\n          if (slides[i].column === colIndexToPrepend) prependSlidesIndexes.push(i);\n        }\n        // slides.forEach((slide, slideIndex) => {\n        //   if (slide.column === colIndexToPrepend) prependSlidesIndexes.push(slideIndex);\n        // });\n      } else {\n        prependSlidesIndexes.push(cols - index - 1);\n      }\n    }\n  } else if (activeColIndexWithShift + slidesPerView > cols - loopedSlides) {\n    slidesAppended = Math.max(activeColIndexWithShift - (cols - loopedSlides * 2), slidesPerGroup);\n    if (isInitialOverflow) {\n      slidesAppended = Math.max(slidesAppended, slidesPerView - cols + initialSlide + 1);\n    }\n    for (let i = 0; i < slidesAppended; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        slides.forEach((slide, slideIndex) => {\n          if (slide.column === index) appendSlidesIndexes.push(slideIndex);\n        });\n      } else {\n        appendSlidesIndexes.push(index);\n      }\n    }\n  }\n  swiper.__preventObserver__ = true;\n  requestAnimationFrame(() => {\n    swiper.__preventObserver__ = false;\n  });\n  if (swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    if (appendSlidesIndexes.includes(activeSlideIndex)) {\n      appendSlidesIndexes.splice(appendSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n    if (prependSlidesIndexes.includes(activeSlideIndex)) {\n      prependSlidesIndexes.splice(prependSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n  }\n  if (isPrev) {\n    prependSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.prepend(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  if (isNext) {\n    appendSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.append(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  swiper.recalcSlides();\n  if (params.slidesPerView === 'auto') {\n    swiper.updateSlides();\n  } else if (gridEnabled && (prependSlidesIndexes.length > 0 && isPrev || appendSlidesIndexes.length > 0 && isNext)) {\n    swiper.slides.forEach((slide, slideIndex) => {\n      swiper.grid.updateSlide(slideIndex, slide, swiper.slides);\n    });\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  if (slideTo) {\n    if (prependSlidesIndexes.length > 0 && isPrev) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex + slidesPrepended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex + Math.ceil(slidesPrepended), 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        if (setTranslate) {\n          const shift = gridEnabled ? prependSlidesIndexes.length / params.grid.rows : prependSlidesIndexes.length;\n          swiper.slideTo(swiper.activeIndex + shift, 0, false, true);\n          swiper.touchEventsData.currentTranslate = swiper.translate;\n        }\n      }\n    } else if (appendSlidesIndexes.length > 0 && isNext) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex - slidesAppended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex - slidesAppended, 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        const shift = gridEnabled ? appendSlidesIndexes.length / params.grid.rows : appendSlidesIndexes.length;\n        swiper.slideTo(swiper.activeIndex - shift, 0, false, true);\n      }\n    }\n  }\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.controller && swiper.controller.control && !byController) {\n    const loopParams = {\n      slideRealIndex,\n      direction,\n      setTranslate,\n      activeSlideIndex,\n      byController: true\n    };\n    if (Array.isArray(swiper.controller.control)) {\n      swiper.controller.control.forEach(c => {\n        if (!c.destroyed && c.params.loop) c.loopFix({\n          ...loopParams,\n          slideTo: c.params.slidesPerView === params.slidesPerView ? slideTo : false\n        });\n      });\n    } else if (swiper.controller.control instanceof swiper.constructor && swiper.controller.control.params.loop) {\n      swiper.controller.control.loopFix({\n        ...loopParams,\n        slideTo: swiper.controller.control.params.slidesPerView === params.slidesPerView ? slideTo : false\n      });\n    }\n  }\n  swiper.emit('loopFix');\n}\n\nfunction loopDestroy() {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || !slidesEl || swiper.virtual && swiper.params.virtual.enabled) return;\n  swiper.recalcSlides();\n  const newSlidesOrder = [];\n  swiper.slides.forEach(slideEl => {\n    const index = typeof slideEl.swiperSlideIndex === 'undefined' ? slideEl.getAttribute('data-swiper-slide-index') * 1 : slideEl.swiperSlideIndex;\n    newSlidesOrder[index] = slideEl;\n  });\n  swiper.slides.forEach(slideEl => {\n    slideEl.removeAttribute('data-swiper-slide-index');\n  });\n  newSlidesOrder.forEach(slideEl => {\n    slidesEl.append(slideEl);\n  });\n  swiper.recalcSlides();\n  swiper.slideTo(swiper.realIndex, 0);\n}\n\nvar loop = {\n  loopCreate,\n  loopFix,\n  loopDestroy\n};\n\nfunction setGrabCursor(moving) {\n  const swiper = this;\n  if (!swiper.params.simulateTouch || swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) return;\n  const el = swiper.params.touchEventsTarget === 'container' ? swiper.el : swiper.wrapperEl;\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  el.style.cursor = 'move';\n  el.style.cursor = moving ? 'grabbing' : 'grab';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\n\nfunction unsetGrabCursor() {\n  const swiper = this;\n  if (swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) {\n    return;\n  }\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  swiper[swiper.params.touchEventsTarget === 'container' ? 'el' : 'wrapperEl'].style.cursor = '';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\n\nvar grabCursor = {\n  setGrabCursor,\n  unsetGrabCursor\n};\n\n// Modified from https://stackoverflow.com/questions/54520554/custom-element-getrootnode-closest-function-crossing-multiple-parent-shadowd\nfunction closestElement(selector, base) {\n  if (base === void 0) {\n    base = this;\n  }\n  function __closestFrom(el) {\n    if (!el || el === getDocument() || el === getWindow()) return null;\n    if (el.assignedSlot) el = el.assignedSlot;\n    const found = el.closest(selector);\n    if (!found && !el.getRootNode) {\n      return null;\n    }\n    return found || __closestFrom(el.getRootNode().host);\n  }\n  return __closestFrom(base);\n}\nfunction preventEdgeSwipe(swiper, event, startX) {\n  const window = getWindow();\n  const {\n    params\n  } = swiper;\n  const edgeSwipeDetection = params.edgeSwipeDetection;\n  const edgeSwipeThreshold = params.edgeSwipeThreshold;\n  if (edgeSwipeDetection && (startX <= edgeSwipeThreshold || startX >= window.innerWidth - edgeSwipeThreshold)) {\n    if (edgeSwipeDetection === 'prevent') {\n      event.preventDefault();\n      return true;\n    }\n    return false;\n  }\n  return true;\n}\nfunction onTouchStart(event) {\n  const swiper = this;\n  const document = getDocument();\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  const data = swiper.touchEventsData;\n  if (e.type === 'pointerdown') {\n    if (data.pointerId !== null && data.pointerId !== e.pointerId) {\n      return;\n    }\n    data.pointerId = e.pointerId;\n  } else if (e.type === 'touchstart' && e.targetTouches.length === 1) {\n    data.touchId = e.targetTouches[0].identifier;\n  }\n  if (e.type === 'touchstart') {\n    // don't proceed touch event\n    preventEdgeSwipe(swiper, e, e.targetTouches[0].pageX);\n    return;\n  }\n  const {\n    params,\n    touches,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return;\n  }\n  if (!swiper.animating && params.cssMode && params.loop) {\n    swiper.loopFix();\n  }\n  let targetEl = e.target;\n  if (params.touchEventsTarget === 'wrapper') {\n    if (!elementIsChildOf(targetEl, swiper.wrapperEl)) return;\n  }\n  if ('which' in e && e.which === 3) return;\n  if ('button' in e && e.button > 0) return;\n  if (data.isTouched && data.isMoved) return;\n\n  // change target el for shadow root component\n  const swipingClassHasValue = !!params.noSwipingClass && params.noSwipingClass !== '';\n  // eslint-disable-next-line\n  const eventPath = e.composedPath ? e.composedPath() : e.path;\n  if (swipingClassHasValue && e.target && e.target.shadowRoot && eventPath) {\n    targetEl = eventPath[0];\n  }\n  const noSwipingSelector = params.noSwipingSelector ? params.noSwipingSelector : `.${params.noSwipingClass}`;\n  const isTargetShadow = !!(e.target && e.target.shadowRoot);\n\n  // use closestElement for shadow root element to get the actual closest for nested shadow root element\n  if (params.noSwiping && (isTargetShadow ? closestElement(noSwipingSelector, targetEl) : targetEl.closest(noSwipingSelector))) {\n    swiper.allowClick = true;\n    return;\n  }\n  if (params.swipeHandler) {\n    if (!targetEl.closest(params.swipeHandler)) return;\n  }\n  touches.currentX = e.pageX;\n  touches.currentY = e.pageY;\n  const startX = touches.currentX;\n  const startY = touches.currentY;\n\n  // Do NOT start if iOS edge swipe is detected. Otherwise iOS app cannot swipe-to-go-back anymore\n\n  if (!preventEdgeSwipe(swiper, e, startX)) {\n    return;\n  }\n  Object.assign(data, {\n    isTouched: true,\n    isMoved: false,\n    allowTouchCallbacks: true,\n    isScrolling: undefined,\n    startMoving: undefined\n  });\n  touches.startX = startX;\n  touches.startY = startY;\n  data.touchStartTime = now();\n  swiper.allowClick = true;\n  swiper.updateSize();\n  swiper.swipeDirection = undefined;\n  if (params.threshold > 0) data.allowThresholdMove = false;\n  let preventDefault = true;\n  if (targetEl.matches(data.focusableElements)) {\n    preventDefault = false;\n    if (targetEl.nodeName === 'SELECT') {\n      data.isTouched = false;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== targetEl && (e.pointerType === 'mouse' || e.pointerType !== 'mouse' && !targetEl.matches(data.focusableElements))) {\n    document.activeElement.blur();\n  }\n  const shouldPreventDefault = preventDefault && swiper.allowTouchMove && params.touchStartPreventDefault;\n  if ((params.touchStartForcePreventDefault || shouldPreventDefault) && !targetEl.isContentEditable) {\n    e.preventDefault();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode && swiper.animating && !params.cssMode) {\n    swiper.freeMode.onTouchStart();\n  }\n  swiper.emit('touchStart', e);\n}\n\nfunction onTouchMove(event) {\n  const document = getDocument();\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (e.type === 'pointermove') {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    const id = e.pointerId;\n    if (id !== data.pointerId) return;\n  }\n  let targetTouch;\n  if (e.type === 'touchmove') {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  } else {\n    targetTouch = e;\n  }\n  if (!data.isTouched) {\n    if (data.startMoving && data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n    return;\n  }\n  const pageX = targetTouch.pageX;\n  const pageY = targetTouch.pageY;\n  if (e.preventedByNestedSwiper) {\n    touches.startX = pageX;\n    touches.startY = pageY;\n    return;\n  }\n  if (!swiper.allowTouchMove) {\n    if (!e.target.matches(data.focusableElements)) {\n      swiper.allowClick = false;\n    }\n    if (data.isTouched) {\n      Object.assign(touches, {\n        startX: pageX,\n        startY: pageY,\n        currentX: pageX,\n        currentY: pageY\n      });\n      data.touchStartTime = now();\n    }\n    return;\n  }\n  if (params.touchReleaseOnEdges && !params.loop) {\n    if (swiper.isVertical()) {\n      // Vertical\n      if (pageY < touches.startY && swiper.translate <= swiper.maxTranslate() || pageY > touches.startY && swiper.translate >= swiper.minTranslate()) {\n        data.isTouched = false;\n        data.isMoved = false;\n        return;\n      }\n    } else if (rtl && (pageX > touches.startX && -swiper.translate <= swiper.maxTranslate() || pageX < touches.startX && -swiper.translate >= swiper.minTranslate())) {\n      return;\n    } else if (!rtl && (pageX < touches.startX && swiper.translate <= swiper.maxTranslate() || pageX > touches.startX && swiper.translate >= swiper.minTranslate())) {\n      return;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== e.target && e.pointerType !== 'mouse') {\n    document.activeElement.blur();\n  }\n  if (document.activeElement) {\n    if (e.target === document.activeElement && e.target.matches(data.focusableElements)) {\n      data.isMoved = true;\n      swiper.allowClick = false;\n      return;\n    }\n  }\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchMove', e);\n  }\n  touches.previousX = touches.currentX;\n  touches.previousY = touches.currentY;\n  touches.currentX = pageX;\n  touches.currentY = pageY;\n  const diffX = touches.currentX - touches.startX;\n  const diffY = touches.currentY - touches.startY;\n  if (swiper.params.threshold && Math.sqrt(diffX ** 2 + diffY ** 2) < swiper.params.threshold) return;\n  if (typeof data.isScrolling === 'undefined') {\n    let touchAngle;\n    if (swiper.isHorizontal() && touches.currentY === touches.startY || swiper.isVertical() && touches.currentX === touches.startX) {\n      data.isScrolling = false;\n    } else {\n      // eslint-disable-next-line\n      if (diffX * diffX + diffY * diffY >= 25) {\n        touchAngle = Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180 / Math.PI;\n        data.isScrolling = swiper.isHorizontal() ? touchAngle > params.touchAngle : 90 - touchAngle > params.touchAngle;\n      }\n    }\n  }\n  if (data.isScrolling) {\n    swiper.emit('touchMoveOpposite', e);\n  }\n  if (typeof data.startMoving === 'undefined') {\n    if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n      data.startMoving = true;\n    }\n  }\n  if (data.isScrolling || e.type === 'touchmove' && data.preventTouchMoveFromPointerMove) {\n    data.isTouched = false;\n    return;\n  }\n  if (!data.startMoving) {\n    return;\n  }\n  swiper.allowClick = false;\n  if (!params.cssMode && e.cancelable) {\n    e.preventDefault();\n  }\n  if (params.touchMoveStopPropagation && !params.nested) {\n    e.stopPropagation();\n  }\n  let diff = swiper.isHorizontal() ? diffX : diffY;\n  let touchesDiff = swiper.isHorizontal() ? touches.currentX - touches.previousX : touches.currentY - touches.previousY;\n  if (params.oneWayMovement) {\n    diff = Math.abs(diff) * (rtl ? 1 : -1);\n    touchesDiff = Math.abs(touchesDiff) * (rtl ? 1 : -1);\n  }\n  touches.diff = diff;\n  diff *= params.touchRatio;\n  if (rtl) {\n    diff = -diff;\n    touchesDiff = -touchesDiff;\n  }\n  const prevTouchesDirection = swiper.touchesDirection;\n  swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n  swiper.touchesDirection = touchesDiff > 0 ? 'prev' : 'next';\n  const isLoop = swiper.params.loop && !params.cssMode;\n  const allowLoopFix = swiper.touchesDirection === 'next' && swiper.allowSlideNext || swiper.touchesDirection === 'prev' && swiper.allowSlidePrev;\n  if (!data.isMoved) {\n    if (isLoop && allowLoopFix) {\n      swiper.loopFix({\n        direction: swiper.swipeDirection\n      });\n    }\n    data.startTranslate = swiper.getTranslate();\n    swiper.setTransition(0);\n    if (swiper.animating) {\n      const evt = new window.CustomEvent('transitionend', {\n        bubbles: true,\n        cancelable: true,\n        detail: {\n          bySwiperTouchMove: true\n        }\n      });\n      swiper.wrapperEl.dispatchEvent(evt);\n    }\n    data.allowMomentumBounce = false;\n    // Grab Cursor\n    if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(true);\n    }\n    swiper.emit('sliderFirstMove', e);\n  }\n  let loopFixed;\n  new Date().getTime();\n  if (params._loopSwapReset !== false && data.isMoved && data.allowThresholdMove && prevTouchesDirection !== swiper.touchesDirection && isLoop && allowLoopFix && Math.abs(diff) >= 1) {\n    Object.assign(touches, {\n      startX: pageX,\n      startY: pageY,\n      currentX: pageX,\n      currentY: pageY,\n      startTranslate: data.currentTranslate\n    });\n    data.loopSwapReset = true;\n    data.startTranslate = data.currentTranslate;\n    return;\n  }\n  swiper.emit('sliderMove', e);\n  data.isMoved = true;\n  data.currentTranslate = diff + data.startTranslate;\n  let disableParentSwiper = true;\n  let resistanceRatio = params.resistanceRatio;\n  if (params.touchReleaseOnEdges) {\n    resistanceRatio = 0;\n  }\n  if (diff > 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate > (params.centeredSlides ? swiper.minTranslate() - swiper.slidesSizesGrid[swiper.activeIndex + 1] - (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.activeIndex + 1] + swiper.params.spaceBetween : 0) - swiper.params.spaceBetween : swiper.minTranslate())) {\n      swiper.loopFix({\n        direction: 'prev',\n        setTranslate: true,\n        activeSlideIndex: 0\n      });\n    }\n    if (data.currentTranslate > swiper.minTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.minTranslate() - 1 + (-swiper.minTranslate() + data.startTranslate + diff) ** resistanceRatio;\n      }\n    }\n  } else if (diff < 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate < (params.centeredSlides ? swiper.maxTranslate() + swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween + (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween : 0) : swiper.maxTranslate())) {\n      swiper.loopFix({\n        direction: 'next',\n        setTranslate: true,\n        activeSlideIndex: swiper.slides.length - (params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(parseFloat(params.slidesPerView, 10)))\n      });\n    }\n    if (data.currentTranslate < swiper.maxTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.maxTranslate() + 1 - (swiper.maxTranslate() - data.startTranslate - diff) ** resistanceRatio;\n      }\n    }\n  }\n  if (disableParentSwiper) {\n    e.preventedByNestedSwiper = true;\n  }\n\n  // Directions locks\n  if (!swiper.allowSlideNext && swiper.swipeDirection === 'next' && data.currentTranslate < data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && swiper.swipeDirection === 'prev' && data.currentTranslate > data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && !swiper.allowSlideNext) {\n    data.currentTranslate = data.startTranslate;\n  }\n\n  // Threshold\n  if (params.threshold > 0) {\n    if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n      if (!data.allowThresholdMove) {\n        data.allowThresholdMove = true;\n        touches.startX = touches.currentX;\n        touches.startY = touches.currentY;\n        data.currentTranslate = data.startTranslate;\n        touches.diff = swiper.isHorizontal() ? touches.currentX - touches.startX : touches.currentY - touches.startY;\n        return;\n      }\n    } else {\n      data.currentTranslate = data.startTranslate;\n      return;\n    }\n  }\n  if (!params.followFinger || params.cssMode) return;\n\n  // Update active index in free mode\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode || params.watchSlidesProgress) {\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode) {\n    swiper.freeMode.onTouchMove();\n  }\n  // Update progress\n  swiper.updateProgress(data.currentTranslate);\n  // Update translate\n  swiper.setTranslate(data.currentTranslate);\n}\n\nfunction onTouchEnd(event) {\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  let targetTouch;\n  const isTouchEvent = e.type === 'touchend' || e.type === 'touchcancel';\n  if (!isTouchEvent) {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    if (e.pointerId !== data.pointerId) return;\n    targetTouch = e;\n  } else {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  }\n  if (['pointercancel', 'pointerout', 'pointerleave', 'contextmenu'].includes(e.type)) {\n    const proceed = ['pointercancel', 'contextmenu'].includes(e.type) && (swiper.browser.isSafari || swiper.browser.isWebView);\n    if (!proceed) {\n      return;\n    }\n  }\n  data.pointerId = null;\n  data.touchId = null;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    slidesGrid,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchEnd', e);\n  }\n  data.allowTouchCallbacks = false;\n  if (!data.isTouched) {\n    if (data.isMoved && params.grabCursor) {\n      swiper.setGrabCursor(false);\n    }\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n\n  // Return Grab Cursor\n  if (params.grabCursor && data.isMoved && data.isTouched && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n    swiper.setGrabCursor(false);\n  }\n\n  // Time diff\n  const touchEndTime = now();\n  const timeDiff = touchEndTime - data.touchStartTime;\n\n  // Tap, doubleTap, Click\n  if (swiper.allowClick) {\n    const pathTree = e.path || e.composedPath && e.composedPath();\n    swiper.updateClickedSlide(pathTree && pathTree[0] || e.target, pathTree);\n    swiper.emit('tap click', e);\n    if (timeDiff < 300 && touchEndTime - data.lastClickTime < 300) {\n      swiper.emit('doubleTap doubleClick', e);\n    }\n  }\n  data.lastClickTime = now();\n  nextTick(() => {\n    if (!swiper.destroyed) swiper.allowClick = true;\n  });\n  if (!data.isTouched || !data.isMoved || !swiper.swipeDirection || touches.diff === 0 && !data.loopSwapReset || data.currentTranslate === data.startTranslate && !data.loopSwapReset) {\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  data.isTouched = false;\n  data.isMoved = false;\n  data.startMoving = false;\n  let currentPos;\n  if (params.followFinger) {\n    currentPos = rtl ? swiper.translate : -swiper.translate;\n  } else {\n    currentPos = -data.currentTranslate;\n  }\n  if (params.cssMode) {\n    return;\n  }\n  if (params.freeMode && params.freeMode.enabled) {\n    swiper.freeMode.onTouchEnd({\n      currentPos\n    });\n    return;\n  }\n\n  // Find current slide\n  const swipeToLast = currentPos >= -swiper.maxTranslate() && !swiper.params.loop;\n  let stopIndex = 0;\n  let groupSize = swiper.slidesSizesGrid[0];\n  for (let i = 0; i < slidesGrid.length; i += i < params.slidesPerGroupSkip ? 1 : params.slidesPerGroup) {\n    const increment = i < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n    if (typeof slidesGrid[i + increment] !== 'undefined') {\n      if (swipeToLast || currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + increment]) {\n        stopIndex = i;\n        groupSize = slidesGrid[i + increment] - slidesGrid[i];\n      }\n    } else if (swipeToLast || currentPos >= slidesGrid[i]) {\n      stopIndex = i;\n      groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n    }\n  }\n  let rewindFirstIndex = null;\n  let rewindLastIndex = null;\n  if (params.rewind) {\n    if (swiper.isBeginning) {\n      rewindLastIndex = params.virtual && params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    } else if (swiper.isEnd) {\n      rewindFirstIndex = 0;\n    }\n  }\n  // Find current slide size\n  const ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n  const increment = stopIndex < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n  if (timeDiff > params.longSwipesMs) {\n    // Long touches\n    if (!params.longSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (swiper.swipeDirection === 'next') {\n      if (ratio >= params.longSwipesRatio) swiper.slideTo(params.rewind && swiper.isEnd ? rewindFirstIndex : stopIndex + increment);else swiper.slideTo(stopIndex);\n    }\n    if (swiper.swipeDirection === 'prev') {\n      if (ratio > 1 - params.longSwipesRatio) {\n        swiper.slideTo(stopIndex + increment);\n      } else if (rewindLastIndex !== null && ratio < 0 && Math.abs(ratio) > params.longSwipesRatio) {\n        swiper.slideTo(rewindLastIndex);\n      } else {\n        swiper.slideTo(stopIndex);\n      }\n    }\n  } else {\n    // Short swipes\n    if (!params.shortSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    const isNavButtonTarget = swiper.navigation && (e.target === swiper.navigation.nextEl || e.target === swiper.navigation.prevEl);\n    if (!isNavButtonTarget) {\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(rewindFirstIndex !== null ? rewindFirstIndex : stopIndex + increment);\n      }\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(rewindLastIndex !== null ? rewindLastIndex : stopIndex);\n      }\n    } else if (e.target === swiper.navigation.nextEl) {\n      swiper.slideTo(stopIndex + increment);\n    } else {\n      swiper.slideTo(stopIndex);\n    }\n  }\n}\n\nfunction onResize() {\n  const swiper = this;\n  const {\n    params,\n    el\n  } = swiper;\n  if (el && el.offsetWidth === 0) return;\n\n  // Breakpoints\n  if (params.breakpoints) {\n    swiper.setBreakpoint();\n  }\n\n  // Save locks\n  const {\n    allowSlideNext,\n    allowSlidePrev,\n    snapGrid\n  } = swiper;\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n  // Disable locks on resize\n  swiper.allowSlideNext = true;\n  swiper.allowSlidePrev = true;\n  swiper.updateSize();\n  swiper.updateSlides();\n  swiper.updateSlidesClasses();\n  const isVirtualLoop = isVirtual && params.loop;\n  if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !swiper.isBeginning && !swiper.params.centeredSlides && !isVirtualLoop) {\n    swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n  } else {\n    if (swiper.params.loop && !isVirtual) {\n      swiper.slideToLoop(swiper.realIndex, 0, false, true);\n    } else {\n      swiper.slideTo(swiper.activeIndex, 0, false, true);\n    }\n  }\n  if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n    clearTimeout(swiper.autoplay.resizeTimeout);\n    swiper.autoplay.resizeTimeout = setTimeout(() => {\n      if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n        swiper.autoplay.resume();\n      }\n    }, 500);\n  }\n  // Return locks after resize\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n    swiper.checkOverflow();\n  }\n}\n\nfunction onClick(e) {\n  const swiper = this;\n  if (!swiper.enabled) return;\n  if (!swiper.allowClick) {\n    if (swiper.params.preventClicks) e.preventDefault();\n    if (swiper.params.preventClicksPropagation && swiper.animating) {\n      e.stopPropagation();\n      e.stopImmediatePropagation();\n    }\n  }\n}\n\nfunction onScroll() {\n  const swiper = this;\n  const {\n    wrapperEl,\n    rtlTranslate,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  swiper.previousTranslate = swiper.translate;\n  if (swiper.isHorizontal()) {\n    swiper.translate = -wrapperEl.scrollLeft;\n  } else {\n    swiper.translate = -wrapperEl.scrollTop;\n  }\n  // eslint-disable-next-line\n  if (swiper.translate === 0) swiper.translate = 0;\n  swiper.updateActiveIndex();\n  swiper.updateSlidesClasses();\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (swiper.translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== swiper.progress) {\n    swiper.updateProgress(rtlTranslate ? -swiper.translate : swiper.translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, false);\n}\n\nfunction onLoad(e) {\n  const swiper = this;\n  processLazyPreloader(swiper, e.target);\n  if (swiper.params.cssMode || swiper.params.slidesPerView !== 'auto' && !swiper.params.autoHeight) {\n    return;\n  }\n  swiper.update();\n}\n\nfunction onDocumentTouchStart() {\n  const swiper = this;\n  if (swiper.documentTouchHandlerProceeded) return;\n  swiper.documentTouchHandlerProceeded = true;\n  if (swiper.params.touchReleaseOnEdges) {\n    swiper.el.style.touchAction = 'auto';\n  }\n}\n\nconst events = (swiper, method) => {\n  const document = getDocument();\n  const {\n    params,\n    el,\n    wrapperEl,\n    device\n  } = swiper;\n  const capture = !!params.nested;\n  const domMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n  const swiperMethod = method;\n  if (!el || typeof el === 'string') return;\n\n  // Touch Events\n  document[domMethod]('touchstart', swiper.onDocumentTouchStart, {\n    passive: false,\n    capture\n  });\n  el[domMethod]('touchstart', swiper.onTouchStart, {\n    passive: false\n  });\n  el[domMethod]('pointerdown', swiper.onTouchStart, {\n    passive: false\n  });\n  document[domMethod]('touchmove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('pointermove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('touchend', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerup', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointercancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('touchcancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerout', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerleave', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('contextmenu', swiper.onTouchEnd, {\n    passive: true\n  });\n\n  // Prevent Links Clicks\n  if (params.preventClicks || params.preventClicksPropagation) {\n    el[domMethod]('click', swiper.onClick, true);\n  }\n  if (params.cssMode) {\n    wrapperEl[domMethod]('scroll', swiper.onScroll);\n  }\n\n  // Resize handler\n  if (params.updateOnWindowResize) {\n    swiper[swiperMethod](device.ios || device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate', onResize, true);\n  } else {\n    swiper[swiperMethod]('observerUpdate', onResize, true);\n  }\n\n  // Images loader\n  el[domMethod]('load', swiper.onLoad, {\n    capture: true\n  });\n};\nfunction attachEvents() {\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.onTouchStart = onTouchStart.bind(swiper);\n  swiper.onTouchMove = onTouchMove.bind(swiper);\n  swiper.onTouchEnd = onTouchEnd.bind(swiper);\n  swiper.onDocumentTouchStart = onDocumentTouchStart.bind(swiper);\n  if (params.cssMode) {\n    swiper.onScroll = onScroll.bind(swiper);\n  }\n  swiper.onClick = onClick.bind(swiper);\n  swiper.onLoad = onLoad.bind(swiper);\n  events(swiper, 'on');\n}\nfunction detachEvents() {\n  const swiper = this;\n  events(swiper, 'off');\n}\nvar events$1 = {\n  attachEvents,\n  detachEvents\n};\n\nconst isGridEnabled = (swiper, params) => {\n  return swiper.grid && params.grid && params.grid.rows > 1;\n};\nfunction setBreakpoint() {\n  const swiper = this;\n  const {\n    realIndex,\n    initialized,\n    params,\n    el\n  } = swiper;\n  const breakpoints = params.breakpoints;\n  if (!breakpoints || breakpoints && Object.keys(breakpoints).length === 0) return;\n  const document = getDocument();\n\n  // Get breakpoint for window/container width and update parameters\n  const breakpointsBase = params.breakpointsBase === 'window' || !params.breakpointsBase ? params.breakpointsBase : 'container';\n  const breakpointContainer = ['window', 'container'].includes(params.breakpointsBase) || !params.breakpointsBase ? swiper.el : document.querySelector(params.breakpointsBase);\n  const breakpoint = swiper.getBreakpoint(breakpoints, breakpointsBase, breakpointContainer);\n  if (!breakpoint || swiper.currentBreakpoint === breakpoint) return;\n  const breakpointOnlyParams = breakpoint in breakpoints ? breakpoints[breakpoint] : undefined;\n  const breakpointParams = breakpointOnlyParams || swiper.originalParams;\n  const wasMultiRow = isGridEnabled(swiper, params);\n  const isMultiRow = isGridEnabled(swiper, breakpointParams);\n  const wasGrabCursor = swiper.params.grabCursor;\n  const isGrabCursor = breakpointParams.grabCursor;\n  const wasEnabled = params.enabled;\n  if (wasMultiRow && !isMultiRow) {\n    el.classList.remove(`${params.containerModifierClass}grid`, `${params.containerModifierClass}grid-column`);\n    swiper.emitContainerClasses();\n  } else if (!wasMultiRow && isMultiRow) {\n    el.classList.add(`${params.containerModifierClass}grid`);\n    if (breakpointParams.grid.fill && breakpointParams.grid.fill === 'column' || !breakpointParams.grid.fill && params.grid.fill === 'column') {\n      el.classList.add(`${params.containerModifierClass}grid-column`);\n    }\n    swiper.emitContainerClasses();\n  }\n  if (wasGrabCursor && !isGrabCursor) {\n    swiper.unsetGrabCursor();\n  } else if (!wasGrabCursor && isGrabCursor) {\n    swiper.setGrabCursor();\n  }\n\n  // Toggle navigation, pagination, scrollbar\n  ['navigation', 'pagination', 'scrollbar'].forEach(prop => {\n    if (typeof breakpointParams[prop] === 'undefined') return;\n    const wasModuleEnabled = params[prop] && params[prop].enabled;\n    const isModuleEnabled = breakpointParams[prop] && breakpointParams[prop].enabled;\n    if (wasModuleEnabled && !isModuleEnabled) {\n      swiper[prop].disable();\n    }\n    if (!wasModuleEnabled && isModuleEnabled) {\n      swiper[prop].enable();\n    }\n  });\n  const directionChanged = breakpointParams.direction && breakpointParams.direction !== params.direction;\n  const needsReLoop = params.loop && (breakpointParams.slidesPerView !== params.slidesPerView || directionChanged);\n  const wasLoop = params.loop;\n  if (directionChanged && initialized) {\n    swiper.changeDirection();\n  }\n  extend(swiper.params, breakpointParams);\n  const isEnabled = swiper.params.enabled;\n  const hasLoop = swiper.params.loop;\n  Object.assign(swiper, {\n    allowTouchMove: swiper.params.allowTouchMove,\n    allowSlideNext: swiper.params.allowSlideNext,\n    allowSlidePrev: swiper.params.allowSlidePrev\n  });\n  if (wasEnabled && !isEnabled) {\n    swiper.disable();\n  } else if (!wasEnabled && isEnabled) {\n    swiper.enable();\n  }\n  swiper.currentBreakpoint = breakpoint;\n  swiper.emit('_beforeBreakpoint', breakpointParams);\n  if (initialized) {\n    if (needsReLoop) {\n      swiper.loopDestroy();\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (!wasLoop && hasLoop) {\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (wasLoop && !hasLoop) {\n      swiper.loopDestroy();\n    }\n  }\n  swiper.emit('breakpoint', breakpointParams);\n}\n\nfunction getBreakpoint(breakpoints, base, containerEl) {\n  if (base === void 0) {\n    base = 'window';\n  }\n  if (!breakpoints || base === 'container' && !containerEl) return undefined;\n  let breakpoint = false;\n  const window = getWindow();\n  const currentHeight = base === 'window' ? window.innerHeight : containerEl.clientHeight;\n  const points = Object.keys(breakpoints).map(point => {\n    if (typeof point === 'string' && point.indexOf('@') === 0) {\n      const minRatio = parseFloat(point.substr(1));\n      const value = currentHeight * minRatio;\n      return {\n        value,\n        point\n      };\n    }\n    return {\n      value: point,\n      point\n    };\n  });\n  points.sort((a, b) => parseInt(a.value, 10) - parseInt(b.value, 10));\n  for (let i = 0; i < points.length; i += 1) {\n    const {\n      point,\n      value\n    } = points[i];\n    if (base === 'window') {\n      if (window.matchMedia(`(min-width: ${value}px)`).matches) {\n        breakpoint = point;\n      }\n    } else if (value <= containerEl.clientWidth) {\n      breakpoint = point;\n    }\n  }\n  return breakpoint || 'max';\n}\n\nvar breakpoints = {\n  setBreakpoint,\n  getBreakpoint\n};\n\nfunction prepareClasses(entries, prefix) {\n  const resultClasses = [];\n  entries.forEach(item => {\n    if (typeof item === 'object') {\n      Object.keys(item).forEach(classNames => {\n        if (item[classNames]) {\n          resultClasses.push(prefix + classNames);\n        }\n      });\n    } else if (typeof item === 'string') {\n      resultClasses.push(prefix + item);\n    }\n  });\n  return resultClasses;\n}\nfunction addClasses() {\n  const swiper = this;\n  const {\n    classNames,\n    params,\n    rtl,\n    el,\n    device\n  } = swiper;\n  // prettier-ignore\n  const suffixes = prepareClasses(['initialized', params.direction, {\n    'free-mode': swiper.params.freeMode && params.freeMode.enabled\n  }, {\n    'autoheight': params.autoHeight\n  }, {\n    'rtl': rtl\n  }, {\n    'grid': params.grid && params.grid.rows > 1\n  }, {\n    'grid-column': params.grid && params.grid.rows > 1 && params.grid.fill === 'column'\n  }, {\n    'android': device.android\n  }, {\n    'ios': device.ios\n  }, {\n    'css-mode': params.cssMode\n  }, {\n    'centered': params.cssMode && params.centeredSlides\n  }, {\n    'watch-progress': params.watchSlidesProgress\n  }], params.containerModifierClass);\n  classNames.push(...suffixes);\n  el.classList.add(...classNames);\n  swiper.emitContainerClasses();\n}\n\nfunction removeClasses() {\n  const swiper = this;\n  const {\n    el,\n    classNames\n  } = swiper;\n  if (!el || typeof el === 'string') return;\n  el.classList.remove(...classNames);\n  swiper.emitContainerClasses();\n}\n\nvar classes = {\n  addClasses,\n  removeClasses\n};\n\nfunction checkOverflow() {\n  const swiper = this;\n  const {\n    isLocked: wasLocked,\n    params\n  } = swiper;\n  const {\n    slidesOffsetBefore\n  } = params;\n  if (slidesOffsetBefore) {\n    const lastSlideIndex = swiper.slides.length - 1;\n    const lastSlideRightEdge = swiper.slidesGrid[lastSlideIndex] + swiper.slidesSizesGrid[lastSlideIndex] + slidesOffsetBefore * 2;\n    swiper.isLocked = swiper.size > lastSlideRightEdge;\n  } else {\n    swiper.isLocked = swiper.snapGrid.length === 1;\n  }\n  if (params.allowSlideNext === true) {\n    swiper.allowSlideNext = !swiper.isLocked;\n  }\n  if (params.allowSlidePrev === true) {\n    swiper.allowSlidePrev = !swiper.isLocked;\n  }\n  if (wasLocked && wasLocked !== swiper.isLocked) {\n    swiper.isEnd = false;\n  }\n  if (wasLocked !== swiper.isLocked) {\n    swiper.emit(swiper.isLocked ? 'lock' : 'unlock');\n  }\n}\nvar checkOverflow$1 = {\n  checkOverflow\n};\n\nvar defaults = {\n  init: true,\n  direction: 'horizontal',\n  oneWayMovement: false,\n  swiperElementNodeName: 'SWIPER-CONTAINER',\n  touchEventsTarget: 'wrapper',\n  initialSlide: 0,\n  speed: 300,\n  cssMode: false,\n  updateOnWindowResize: true,\n  resizeObserver: true,\n  nested: false,\n  createElements: false,\n  eventsPrefix: 'swiper',\n  enabled: true,\n  focusableElements: 'input, select, option, textarea, button, video, label',\n  // Overrides\n  width: null,\n  height: null,\n  //\n  preventInteractionOnTransition: false,\n  // ssr\n  userAgent: null,\n  url: null,\n  // To support iOS's swipe-to-go-back gesture (when being used in-app).\n  edgeSwipeDetection: false,\n  edgeSwipeThreshold: 20,\n  // Autoheight\n  autoHeight: false,\n  // Set wrapper width\n  setWrapperSize: false,\n  // Virtual Translate\n  virtualTranslate: false,\n  // Effects\n  effect: 'slide',\n  // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n\n  // Breakpoints\n  breakpoints: undefined,\n  breakpointsBase: 'window',\n  // Slides grid\n  spaceBetween: 0,\n  slidesPerView: 1,\n  slidesPerGroup: 1,\n  slidesPerGroupSkip: 0,\n  slidesPerGroupAuto: false,\n  centeredSlides: false,\n  centeredSlidesBounds: false,\n  slidesOffsetBefore: 0,\n  // in px\n  slidesOffsetAfter: 0,\n  // in px\n  normalizeSlideIndex: true,\n  centerInsufficientSlides: false,\n  // Disable swiper and hide navigation when container not overflow\n  watchOverflow: true,\n  // Round length\n  roundLengths: false,\n  // Touches\n  touchRatio: 1,\n  touchAngle: 45,\n  simulateTouch: true,\n  shortSwipes: true,\n  longSwipes: true,\n  longSwipesRatio: 0.5,\n  longSwipesMs: 300,\n  followFinger: true,\n  allowTouchMove: true,\n  threshold: 5,\n  touchMoveStopPropagation: false,\n  touchStartPreventDefault: true,\n  touchStartForcePreventDefault: false,\n  touchReleaseOnEdges: false,\n  // Unique Navigation Elements\n  uniqueNavElements: true,\n  // Resistance\n  resistance: true,\n  resistanceRatio: 0.85,\n  // Progress\n  watchSlidesProgress: false,\n  // Cursor\n  grabCursor: false,\n  // Clicks\n  preventClicks: true,\n  preventClicksPropagation: true,\n  slideToClickedSlide: false,\n  // loop\n  loop: false,\n  loopAddBlankSlides: true,\n  loopAdditionalSlides: 0,\n  loopPreventsSliding: true,\n  // rewind\n  rewind: false,\n  // Swiping/no swiping\n  allowSlidePrev: true,\n  allowSlideNext: true,\n  swipeHandler: null,\n  // '.swipe-handler',\n  noSwiping: true,\n  noSwipingClass: 'swiper-no-swiping',\n  noSwipingSelector: null,\n  // Passive Listeners\n  passiveListeners: true,\n  maxBackfaceHiddenSlides: 10,\n  // NS\n  containerModifierClass: 'swiper-',\n  // NEW\n  slideClass: 'swiper-slide',\n  slideBlankClass: 'swiper-slide-blank',\n  slideActiveClass: 'swiper-slide-active',\n  slideVisibleClass: 'swiper-slide-visible',\n  slideFullyVisibleClass: 'swiper-slide-fully-visible',\n  slideNextClass: 'swiper-slide-next',\n  slidePrevClass: 'swiper-slide-prev',\n  wrapperClass: 'swiper-wrapper',\n  lazyPreloaderClass: 'swiper-lazy-preloader',\n  lazyPreloadPrevNext: 0,\n  // Callbacks\n  runCallbacksOnInit: true,\n  // Internals\n  _emitClasses: false\n};\n\nfunction moduleExtendParams(params, allModulesParams) {\n  return function extendParams(obj) {\n    if (obj === void 0) {\n      obj = {};\n    }\n    const moduleParamName = Object.keys(obj)[0];\n    const moduleParams = obj[moduleParamName];\n    if (typeof moduleParams !== 'object' || moduleParams === null) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (params[moduleParamName] === true) {\n      params[moduleParamName] = {\n        enabled: true\n      };\n    }\n    if (moduleParamName === 'navigation' && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].prevEl && !params[moduleParamName].nextEl) {\n      params[moduleParamName].auto = true;\n    }\n    if (['pagination', 'scrollbar'].indexOf(moduleParamName) >= 0 && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].el) {\n      params[moduleParamName].auto = true;\n    }\n    if (!(moduleParamName in params && 'enabled' in moduleParams)) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (typeof params[moduleParamName] === 'object' && !('enabled' in params[moduleParamName])) {\n      params[moduleParamName].enabled = true;\n    }\n    if (!params[moduleParamName]) params[moduleParamName] = {\n      enabled: false\n    };\n    extend(allModulesParams, obj);\n  };\n}\n\n/* eslint no-param-reassign: \"off\" */\nconst prototypes = {\n  eventsEmitter,\n  update,\n  translate,\n  transition,\n  slide,\n  loop,\n  grabCursor,\n  events: events$1,\n  breakpoints,\n  checkOverflow: checkOverflow$1,\n  classes\n};\nconst extendedDefaults = {};\nclass Swiper {\n  constructor() {\n    let el;\n    let params;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (args.length === 1 && args[0].constructor && Object.prototype.toString.call(args[0]).slice(8, -1) === 'Object') {\n      params = args[0];\n    } else {\n      [el, params] = args;\n    }\n    if (!params) params = {};\n    params = extend({}, params);\n    if (el && !params.el) params.el = el;\n    const document = getDocument();\n    if (params.el && typeof params.el === 'string' && document.querySelectorAll(params.el).length > 1) {\n      const swipers = [];\n      document.querySelectorAll(params.el).forEach(containerEl => {\n        const newParams = extend({}, params, {\n          el: containerEl\n        });\n        swipers.push(new Swiper(newParams));\n      });\n      // eslint-disable-next-line no-constructor-return\n      return swipers;\n    }\n\n    // Swiper Instance\n    const swiper = this;\n    swiper.__swiper__ = true;\n    swiper.support = getSupport();\n    swiper.device = getDevice({\n      userAgent: params.userAgent\n    });\n    swiper.browser = getBrowser();\n    swiper.eventsListeners = {};\n    swiper.eventsAnyListeners = [];\n    swiper.modules = [...swiper.__modules__];\n    if (params.modules && Array.isArray(params.modules)) {\n      swiper.modules.push(...params.modules);\n    }\n    const allModulesParams = {};\n    swiper.modules.forEach(mod => {\n      mod({\n        params,\n        swiper,\n        extendParams: moduleExtendParams(params, allModulesParams),\n        on: swiper.on.bind(swiper),\n        once: swiper.once.bind(swiper),\n        off: swiper.off.bind(swiper),\n        emit: swiper.emit.bind(swiper)\n      });\n    });\n\n    // Extend defaults with modules params\n    const swiperParams = extend({}, defaults, allModulesParams);\n\n    // Extend defaults with passed params\n    swiper.params = extend({}, swiperParams, extendedDefaults, params);\n    swiper.originalParams = extend({}, swiper.params);\n    swiper.passedParams = extend({}, params);\n\n    // add event listeners\n    if (swiper.params && swiper.params.on) {\n      Object.keys(swiper.params.on).forEach(eventName => {\n        swiper.on(eventName, swiper.params.on[eventName]);\n      });\n    }\n    if (swiper.params && swiper.params.onAny) {\n      swiper.onAny(swiper.params.onAny);\n    }\n\n    // Extend Swiper\n    Object.assign(swiper, {\n      enabled: swiper.params.enabled,\n      el,\n      // Classes\n      classNames: [],\n      // Slides\n      slides: [],\n      slidesGrid: [],\n      snapGrid: [],\n      slidesSizesGrid: [],\n      // isDirection\n      isHorizontal() {\n        return swiper.params.direction === 'horizontal';\n      },\n      isVertical() {\n        return swiper.params.direction === 'vertical';\n      },\n      // Indexes\n      activeIndex: 0,\n      realIndex: 0,\n      //\n      isBeginning: true,\n      isEnd: false,\n      // Props\n      translate: 0,\n      previousTranslate: 0,\n      progress: 0,\n      velocity: 0,\n      animating: false,\n      cssOverflowAdjustment() {\n        // Returns 0 unless `translate` is > 2**23\n        // Should be subtracted from css values to prevent overflow\n        return Math.trunc(this.translate / 2 ** 23) * 2 ** 23;\n      },\n      // Locks\n      allowSlideNext: swiper.params.allowSlideNext,\n      allowSlidePrev: swiper.params.allowSlidePrev,\n      // Touch Events\n      touchEventsData: {\n        isTouched: undefined,\n        isMoved: undefined,\n        allowTouchCallbacks: undefined,\n        touchStartTime: undefined,\n        isScrolling: undefined,\n        currentTranslate: undefined,\n        startTranslate: undefined,\n        allowThresholdMove: undefined,\n        // Form elements to match\n        focusableElements: swiper.params.focusableElements,\n        // Last click time\n        lastClickTime: 0,\n        clickTimeout: undefined,\n        // Velocities\n        velocities: [],\n        allowMomentumBounce: undefined,\n        startMoving: undefined,\n        pointerId: null,\n        touchId: null\n      },\n      // Clicks\n      allowClick: true,\n      // Touches\n      allowTouchMove: swiper.params.allowTouchMove,\n      touches: {\n        startX: 0,\n        startY: 0,\n        currentX: 0,\n        currentY: 0,\n        diff: 0\n      },\n      // Images\n      imagesToLoad: [],\n      imagesLoaded: 0\n    });\n    swiper.emit('_swiper');\n\n    // Init\n    if (swiper.params.init) {\n      swiper.init();\n    }\n\n    // Return app instance\n    // eslint-disable-next-line no-constructor-return\n    return swiper;\n  }\n  getDirectionLabel(property) {\n    if (this.isHorizontal()) {\n      return property;\n    }\n    // prettier-ignore\n    return {\n      'width': 'height',\n      'margin-top': 'margin-left',\n      'margin-bottom ': 'margin-right',\n      'margin-left': 'margin-top',\n      'margin-right': 'margin-bottom',\n      'padding-left': 'padding-top',\n      'padding-right': 'padding-bottom',\n      'marginRight': 'marginBottom'\n    }[property];\n  }\n  getSlideIndex(slideEl) {\n    const {\n      slidesEl,\n      params\n    } = this;\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    const firstSlideIndex = elementIndex(slides[0]);\n    return elementIndex(slideEl) - firstSlideIndex;\n  }\n  getSlideIndexByData(index) {\n    return this.getSlideIndex(this.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === index));\n  }\n  getSlideIndexWhenGrid(index) {\n    if (this.grid && this.params.grid && this.params.grid.rows > 1) {\n      if (this.params.grid.fill === 'column') {\n        index = Math.floor(index / this.params.grid.rows);\n      } else if (this.params.grid.fill === 'row') {\n        index = index % Math.ceil(this.slides.length / this.params.grid.rows);\n      }\n    }\n    return index;\n  }\n  recalcSlides() {\n    const swiper = this;\n    const {\n      slidesEl,\n      params\n    } = swiper;\n    swiper.slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n  }\n  enable() {\n    const swiper = this;\n    if (swiper.enabled) return;\n    swiper.enabled = true;\n    if (swiper.params.grabCursor) {\n      swiper.setGrabCursor();\n    }\n    swiper.emit('enable');\n  }\n  disable() {\n    const swiper = this;\n    if (!swiper.enabled) return;\n    swiper.enabled = false;\n    if (swiper.params.grabCursor) {\n      swiper.unsetGrabCursor();\n    }\n    swiper.emit('disable');\n  }\n  setProgress(progress, speed) {\n    const swiper = this;\n    progress = Math.min(Math.max(progress, 0), 1);\n    const min = swiper.minTranslate();\n    const max = swiper.maxTranslate();\n    const current = (max - min) * progress + min;\n    swiper.translateTo(current, typeof speed === 'undefined' ? 0 : speed);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  emitContainerClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const cls = swiper.el.className.split(' ').filter(className => {\n      return className.indexOf('swiper') === 0 || className.indexOf(swiper.params.containerModifierClass) === 0;\n    });\n    swiper.emit('_containerClasses', cls.join(' '));\n  }\n  getSlideClasses(slideEl) {\n    const swiper = this;\n    if (swiper.destroyed) return '';\n    return slideEl.className.split(' ').filter(className => {\n      return className.indexOf('swiper-slide') === 0 || className.indexOf(swiper.params.slideClass) === 0;\n    }).join(' ');\n  }\n  emitSlidesClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const updates = [];\n    swiper.slides.forEach(slideEl => {\n      const classNames = swiper.getSlideClasses(slideEl);\n      updates.push({\n        slideEl,\n        classNames\n      });\n      swiper.emit('_slideClass', slideEl, classNames);\n    });\n    swiper.emit('_slideClasses', updates);\n  }\n  slidesPerViewDynamic(view, exact) {\n    if (view === void 0) {\n      view = 'current';\n    }\n    if (exact === void 0) {\n      exact = false;\n    }\n    const swiper = this;\n    const {\n      params,\n      slides,\n      slidesGrid,\n      slidesSizesGrid,\n      size: swiperSize,\n      activeIndex\n    } = swiper;\n    let spv = 1;\n    if (typeof params.slidesPerView === 'number') return params.slidesPerView;\n    if (params.centeredSlides) {\n      let slideSize = slides[activeIndex] ? Math.ceil(slides[activeIndex].swiperSlideSize) : 0;\n      let breakLoop;\n      for (let i = activeIndex + 1; i < slides.length; i += 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += Math.ceil(slides[i].swiperSlideSize);\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n      for (let i = activeIndex - 1; i >= 0; i -= 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n    } else {\n      // eslint-disable-next-line\n      if (view === 'current') {\n        for (let i = activeIndex + 1; i < slides.length; i += 1) {\n          const slideInView = exact ? slidesGrid[i] + slidesSizesGrid[i] - slidesGrid[activeIndex] < swiperSize : slidesGrid[i] - slidesGrid[activeIndex] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      } else {\n        // previous\n        for (let i = activeIndex - 1; i >= 0; i -= 1) {\n          const slideInView = slidesGrid[activeIndex] - slidesGrid[i] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      }\n    }\n    return spv;\n  }\n  update() {\n    const swiper = this;\n    if (!swiper || swiper.destroyed) return;\n    const {\n      snapGrid,\n      params\n    } = swiper;\n    // Breakpoints\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n    [...swiper.el.querySelectorAll('[loading=\"lazy\"]')].forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      }\n    });\n    swiper.updateSize();\n    swiper.updateSlides();\n    swiper.updateProgress();\n    swiper.updateSlidesClasses();\n    function setTranslate() {\n      const translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n      const newTranslate = Math.min(Math.max(translateValue, swiper.maxTranslate()), swiper.minTranslate());\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    let translated;\n    if (params.freeMode && params.freeMode.enabled && !params.cssMode) {\n      setTranslate();\n      if (params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !params.centeredSlides) {\n        const slides = swiper.virtual && params.virtual.enabled ? swiper.virtual.slides : swiper.slides;\n        translated = swiper.slideTo(slides.length - 1, 0, false, true);\n      } else {\n        translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n      if (!translated) {\n        setTranslate();\n      }\n    }\n    if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n    swiper.emit('update');\n  }\n  changeDirection(newDirection, needUpdate) {\n    if (needUpdate === void 0) {\n      needUpdate = true;\n    }\n    const swiper = this;\n    const currentDirection = swiper.params.direction;\n    if (!newDirection) {\n      // eslint-disable-next-line\n      newDirection = currentDirection === 'horizontal' ? 'vertical' : 'horizontal';\n    }\n    if (newDirection === currentDirection || newDirection !== 'horizontal' && newDirection !== 'vertical') {\n      return swiper;\n    }\n    swiper.el.classList.remove(`${swiper.params.containerModifierClass}${currentDirection}`);\n    swiper.el.classList.add(`${swiper.params.containerModifierClass}${newDirection}`);\n    swiper.emitContainerClasses();\n    swiper.params.direction = newDirection;\n    swiper.slides.forEach(slideEl => {\n      if (newDirection === 'vertical') {\n        slideEl.style.width = '';\n      } else {\n        slideEl.style.height = '';\n      }\n    });\n    swiper.emit('changeDirection');\n    if (needUpdate) swiper.update();\n    return swiper;\n  }\n  changeLanguageDirection(direction) {\n    const swiper = this;\n    if (swiper.rtl && direction === 'rtl' || !swiper.rtl && direction === 'ltr') return;\n    swiper.rtl = direction === 'rtl';\n    swiper.rtlTranslate = swiper.params.direction === 'horizontal' && swiper.rtl;\n    if (swiper.rtl) {\n      swiper.el.classList.add(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'rtl';\n    } else {\n      swiper.el.classList.remove(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'ltr';\n    }\n    swiper.update();\n  }\n  mount(element) {\n    const swiper = this;\n    if (swiper.mounted) return true;\n\n    // Find el\n    let el = element || swiper.params.el;\n    if (typeof el === 'string') {\n      el = document.querySelector(el);\n    }\n    if (!el) {\n      return false;\n    }\n    el.swiper = swiper;\n    if (el.parentNode && el.parentNode.host && el.parentNode.host.nodeName === swiper.params.swiperElementNodeName.toUpperCase()) {\n      swiper.isElement = true;\n    }\n    const getWrapperSelector = () => {\n      return `.${(swiper.params.wrapperClass || '').trim().split(' ').join('.')}`;\n    };\n    const getWrapper = () => {\n      if (el && el.shadowRoot && el.shadowRoot.querySelector) {\n        const res = el.shadowRoot.querySelector(getWrapperSelector());\n        // Children needs to return slot items\n        return res;\n      }\n      return elementChildren(el, getWrapperSelector())[0];\n    };\n    // Find Wrapper\n    let wrapperEl = getWrapper();\n    if (!wrapperEl && swiper.params.createElements) {\n      wrapperEl = createElement('div', swiper.params.wrapperClass);\n      el.append(wrapperEl);\n      elementChildren(el, `.${swiper.params.slideClass}`).forEach(slideEl => {\n        wrapperEl.append(slideEl);\n      });\n    }\n    Object.assign(swiper, {\n      el,\n      wrapperEl,\n      slidesEl: swiper.isElement && !el.parentNode.host.slideSlots ? el.parentNode.host : wrapperEl,\n      hostEl: swiper.isElement ? el.parentNode.host : el,\n      mounted: true,\n      // RTL\n      rtl: el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl',\n      rtlTranslate: swiper.params.direction === 'horizontal' && (el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl'),\n      wrongRTL: elementStyle(wrapperEl, 'display') === '-webkit-box'\n    });\n    return true;\n  }\n  init(el) {\n    const swiper = this;\n    if (swiper.initialized) return swiper;\n    const mounted = swiper.mount(el);\n    if (mounted === false) return swiper;\n    swiper.emit('beforeInit');\n\n    // Set breakpoint\n    if (swiper.params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    // Add Classes\n    swiper.addClasses();\n\n    // Update size\n    swiper.updateSize();\n\n    // Update slides\n    swiper.updateSlides();\n    if (swiper.params.watchOverflow) {\n      swiper.checkOverflow();\n    }\n\n    // Set Grab Cursor\n    if (swiper.params.grabCursor && swiper.enabled) {\n      swiper.setGrabCursor();\n    }\n\n    // Slide To Initial Slide\n    if (swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.slideTo(swiper.params.initialSlide + swiper.virtual.slidesBefore, 0, swiper.params.runCallbacksOnInit, false, true);\n    } else {\n      swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit, false, true);\n    }\n\n    // Create loop\n    if (swiper.params.loop) {\n      swiper.loopCreate(undefined, true);\n    }\n\n    // Attach events\n    swiper.attachEvents();\n    const lazyElements = [...swiper.el.querySelectorAll('[loading=\"lazy\"]')];\n    if (swiper.isElement) {\n      lazyElements.push(...swiper.hostEl.querySelectorAll('[loading=\"lazy\"]'));\n    }\n    lazyElements.forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      } else {\n        imageEl.addEventListener('load', e => {\n          processLazyPreloader(swiper, e.target);\n        });\n      }\n    });\n    preload(swiper);\n\n    // Init Flag\n    swiper.initialized = true;\n    preload(swiper);\n\n    // Emit\n    swiper.emit('init');\n    swiper.emit('afterInit');\n    return swiper;\n  }\n  destroy(deleteInstance, cleanStyles) {\n    if (deleteInstance === void 0) {\n      deleteInstance = true;\n    }\n    if (cleanStyles === void 0) {\n      cleanStyles = true;\n    }\n    const swiper = this;\n    const {\n      params,\n      el,\n      wrapperEl,\n      slides\n    } = swiper;\n    if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n      return null;\n    }\n    swiper.emit('beforeDestroy');\n\n    // Init Flag\n    swiper.initialized = false;\n\n    // Detach events\n    swiper.detachEvents();\n\n    // Destroy loop\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n\n    // Cleanup styles\n    if (cleanStyles) {\n      swiper.removeClasses();\n      if (el && typeof el !== 'string') {\n        el.removeAttribute('style');\n      }\n      if (wrapperEl) {\n        wrapperEl.removeAttribute('style');\n      }\n      if (slides && slides.length) {\n        slides.forEach(slideEl => {\n          slideEl.classList.remove(params.slideVisibleClass, params.slideFullyVisibleClass, params.slideActiveClass, params.slideNextClass, params.slidePrevClass);\n          slideEl.removeAttribute('style');\n          slideEl.removeAttribute('data-swiper-slide-index');\n        });\n      }\n    }\n    swiper.emit('destroy');\n\n    // Detach emitter events\n    Object.keys(swiper.eventsListeners).forEach(eventName => {\n      swiper.off(eventName);\n    });\n    if (deleteInstance !== false) {\n      if (swiper.el && typeof swiper.el !== 'string') {\n        swiper.el.swiper = null;\n      }\n      deleteProps(swiper);\n    }\n    swiper.destroyed = true;\n    return null;\n  }\n  static extendDefaults(newDefaults) {\n    extend(extendedDefaults, newDefaults);\n  }\n  static get extendedDefaults() {\n    return extendedDefaults;\n  }\n  static get defaults() {\n    return defaults;\n  }\n  static installModule(mod) {\n    if (!Swiper.prototype.__modules__) Swiper.prototype.__modules__ = [];\n    const modules = Swiper.prototype.__modules__;\n    if (typeof mod === 'function' && modules.indexOf(mod) < 0) {\n      modules.push(mod);\n    }\n  }\n  static use(module) {\n    if (Array.isArray(module)) {\n      module.forEach(m => Swiper.installModule(m));\n      return Swiper;\n    }\n    Swiper.installModule(module);\n    return Swiper;\n  }\n}\nObject.keys(prototypes).forEach(prototypeGroup => {\n  Object.keys(prototypes[prototypeGroup]).forEach(protoMethod => {\n    Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n  });\n});\nSwiper.use([Resize, Observer]);\n\nexport { Swiper as S, defaults as d };\n", "import { e as elementChildren, c as createElement } from './utils.mjs';\n\nfunction createElementIfNotDefined(swiper, originalParams, params, checkProps) {\n  if (swiper.params.createElements) {\n    Object.keys(checkProps).forEach(key => {\n      if (!params[key] && params.auto === true) {\n        let element = elementChildren(swiper.el, `.${checkProps[key]}`)[0];\n        if (!element) {\n          element = createElement('div', checkProps[key]);\n          element.className = checkProps[key];\n          swiper.el.append(element);\n        }\n        params[key] = element;\n        originalParams[key] = element;\n      }\n    });\n  }\n  return params;\n}\n\nexport { createElementIfNotDefined as c };\n", "import { c as createElementIfNotDefined } from '../shared/create-element-if-not-defined.mjs';\nimport { m as makeElementsArray } from '../shared/utils.mjs';\n\nfunction Navigation(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  extendParams({\n    navigation: {\n      nextEl: null,\n      prevEl: null,\n      hideOnClick: false,\n      disabledClass: 'swiper-button-disabled',\n      hiddenClass: 'swiper-button-hidden',\n      lockClass: 'swiper-button-lock',\n      navigationDisabledClass: 'swiper-navigation-disabled'\n    }\n  });\n  swiper.navigation = {\n    nextEl: null,\n    prevEl: null\n  };\n  function getEl(el) {\n    let res;\n    if (el && typeof el === 'string' && swiper.isElement) {\n      res = swiper.el.querySelector(el) || swiper.hostEl.querySelector(el);\n      if (res) return res;\n    }\n    if (el) {\n      if (typeof el === 'string') res = [...document.querySelectorAll(el)];\n      if (swiper.params.uniqueNavElements && typeof el === 'string' && res && res.length > 1 && swiper.el.querySelectorAll(el).length === 1) {\n        res = swiper.el.querySelector(el);\n      } else if (res && res.length === 1) {\n        res = res[0];\n      }\n    }\n    if (el && !res) return el;\n    // if (Array.isArray(res) && res.length === 1) res = res[0];\n    return res;\n  }\n  function toggleEl(el, disabled) {\n    const params = swiper.params.navigation;\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      if (subEl) {\n        subEl.classList[disabled ? 'add' : 'remove'](...params.disabledClass.split(' '));\n        if (subEl.tagName === 'BUTTON') subEl.disabled = disabled;\n        if (swiper.params.watchOverflow && swiper.enabled) {\n          subEl.classList[swiper.isLocked ? 'add' : 'remove'](params.lockClass);\n        }\n      }\n    });\n  }\n  function update() {\n    // Update Navigation Buttons\n    const {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    if (swiper.params.loop) {\n      toggleEl(prevEl, false);\n      toggleEl(nextEl, false);\n      return;\n    }\n    toggleEl(prevEl, swiper.isBeginning && !swiper.params.rewind);\n    toggleEl(nextEl, swiper.isEnd && !swiper.params.rewind);\n  }\n  function onPrevClick(e) {\n    e.preventDefault();\n    if (swiper.isBeginning && !swiper.params.loop && !swiper.params.rewind) return;\n    swiper.slidePrev();\n    emit('navigationPrev');\n  }\n  function onNextClick(e) {\n    e.preventDefault();\n    if (swiper.isEnd && !swiper.params.loop && !swiper.params.rewind) return;\n    swiper.slideNext();\n    emit('navigationNext');\n  }\n  function init() {\n    const params = swiper.params.navigation;\n    swiper.params.navigation = createElementIfNotDefined(swiper, swiper.originalParams.navigation, swiper.params.navigation, {\n      nextEl: 'swiper-button-next',\n      prevEl: 'swiper-button-prev'\n    });\n    if (!(params.nextEl || params.prevEl)) return;\n    let nextEl = getEl(params.nextEl);\n    let prevEl = getEl(params.prevEl);\n    Object.assign(swiper.navigation, {\n      nextEl,\n      prevEl\n    });\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    const initButton = (el, dir) => {\n      if (el) {\n        el.addEventListener('click', dir === 'next' ? onNextClick : onPrevClick);\n      }\n      if (!swiper.enabled && el) {\n        el.classList.add(...params.lockClass.split(' '));\n      }\n    };\n    nextEl.forEach(el => initButton(el, 'next'));\n    prevEl.forEach(el => initButton(el, 'prev'));\n  }\n  function destroy() {\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    const destroyButton = (el, dir) => {\n      el.removeEventListener('click', dir === 'next' ? onNextClick : onPrevClick);\n      el.classList.remove(...swiper.params.navigation.disabledClass.split(' '));\n    };\n    nextEl.forEach(el => destroyButton(el, 'next'));\n    prevEl.forEach(el => destroyButton(el, 'prev'));\n  }\n  on('init', () => {\n    if (swiper.params.navigation.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      update();\n    }\n  });\n  on('toEdge fromEdge lock unlock', () => {\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    if (swiper.enabled) {\n      update();\n      return;\n    }\n    [...nextEl, ...prevEl].filter(el => !!el).forEach(el => el.classList.add(swiper.params.navigation.lockClass));\n  });\n  on('click', (_s, e) => {\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    const targetEl = e.target;\n    let targetIsButton = prevEl.includes(targetEl) || nextEl.includes(targetEl);\n    if (swiper.isElement && !targetIsButton) {\n      const path = e.path || e.composedPath && e.composedPath();\n      if (path) {\n        targetIsButton = path.find(pathEl => nextEl.includes(pathEl) || prevEl.includes(pathEl));\n      }\n    }\n    if (swiper.params.navigation.hideOnClick && !targetIsButton) {\n      if (swiper.pagination && swiper.params.pagination && swiper.params.pagination.clickable && (swiper.pagination.el === targetEl || swiper.pagination.el.contains(targetEl))) return;\n      let isHidden;\n      if (nextEl.length) {\n        isHidden = nextEl[0].classList.contains(swiper.params.navigation.hiddenClass);\n      } else if (prevEl.length) {\n        isHidden = prevEl[0].classList.contains(swiper.params.navigation.hiddenClass);\n      }\n      if (isHidden === true) {\n        emit('navigationShow');\n      } else {\n        emit('navigationHide');\n      }\n      [...nextEl, ...prevEl].filter(el => !!el).forEach(el => el.classList.toggle(swiper.params.navigation.hiddenClass));\n    }\n  });\n  const enable = () => {\n    swiper.el.classList.remove(...swiper.params.navigation.navigationDisabledClass.split(' '));\n    init();\n    update();\n  };\n  const disable = () => {\n    swiper.el.classList.add(...swiper.params.navigation.navigationDisabledClass.split(' '));\n    destroy();\n  };\n  Object.assign(swiper.navigation, {\n    enable,\n    disable,\n    update,\n    init,\n    destroy\n  });\n}\n\nexport { Navigation as default };\n", "function classesToSelector(classes) {\n  if (classes === void 0) {\n    classes = '';\n  }\n  return `.${classes.trim().replace(/([\\.:!+\\/()[\\]])/g, '\\\\$1') // eslint-disable-line\n  .replace(/ /g, '.')}`;\n}\n\nexport { classesToSelector as c };\n", "import { c as classesToSelector } from '../shared/classes-to-selector.mjs';\nimport { c as createElementIfNotDefined } from '../shared/create-element-if-not-defined.mjs';\nimport { m as makeElementsArray, h as elementOuterSize, i as elementIndex, s as setInnerHTML, b as elementParents } from '../shared/utils.mjs';\n\nfunction Pagination(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const pfx = 'swiper-pagination';\n  extendParams({\n    pagination: {\n      el: null,\n      bulletElement: 'span',\n      clickable: false,\n      hideOnClick: false,\n      renderBullet: null,\n      renderProgressbar: null,\n      renderFraction: null,\n      renderCustom: null,\n      progressbarOpposite: false,\n      type: 'bullets',\n      // 'bullets' or 'progressbar' or 'fraction' or 'custom'\n      dynamicBullets: false,\n      dynamicMainBullets: 1,\n      formatFractionCurrent: number => number,\n      formatFractionTotal: number => number,\n      bulletClass: `${pfx}-bullet`,\n      bulletActiveClass: `${pfx}-bullet-active`,\n      modifierClass: `${pfx}-`,\n      currentClass: `${pfx}-current`,\n      totalClass: `${pfx}-total`,\n      hiddenClass: `${pfx}-hidden`,\n      progressbarFillClass: `${pfx}-progressbar-fill`,\n      progressbarOppositeClass: `${pfx}-progressbar-opposite`,\n      clickableClass: `${pfx}-clickable`,\n      lockClass: `${pfx}-lock`,\n      horizontalClass: `${pfx}-horizontal`,\n      verticalClass: `${pfx}-vertical`,\n      paginationDisabledClass: `${pfx}-disabled`\n    }\n  });\n  swiper.pagination = {\n    el: null,\n    bullets: []\n  };\n  let bulletSize;\n  let dynamicBulletIndex = 0;\n  function isPaginationDisabled() {\n    return !swiper.params.pagination.el || !swiper.pagination.el || Array.isArray(swiper.pagination.el) && swiper.pagination.el.length === 0;\n  }\n  function setSideBullets(bulletEl, position) {\n    const {\n      bulletActiveClass\n    } = swiper.params.pagination;\n    if (!bulletEl) return;\n    bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n    if (bulletEl) {\n      bulletEl.classList.add(`${bulletActiveClass}-${position}`);\n      bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n      if (bulletEl) {\n        bulletEl.classList.add(`${bulletActiveClass}-${position}-${position}`);\n      }\n    }\n  }\n  function getMoveDirection(prevIndex, nextIndex, length) {\n    prevIndex = prevIndex % length;\n    nextIndex = nextIndex % length;\n    if (nextIndex === prevIndex + 1) {\n      return 'next';\n    } else if (nextIndex === prevIndex - 1) {\n      return 'previous';\n    }\n    return;\n  }\n  function onBulletClick(e) {\n    const bulletEl = e.target.closest(classesToSelector(swiper.params.pagination.bulletClass));\n    if (!bulletEl) {\n      return;\n    }\n    e.preventDefault();\n    const index = elementIndex(bulletEl) * swiper.params.slidesPerGroup;\n    if (swiper.params.loop) {\n      if (swiper.realIndex === index) return;\n      const moveDirection = getMoveDirection(swiper.realIndex, index, swiper.slides.length);\n      if (moveDirection === 'next') {\n        swiper.slideNext();\n      } else if (moveDirection === 'previous') {\n        swiper.slidePrev();\n      } else {\n        swiper.slideToLoop(index);\n      }\n    } else {\n      swiper.slideTo(index);\n    }\n  }\n  function update() {\n    // Render || Update Pagination bullets/items\n    const rtl = swiper.rtl;\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    // Current/Total\n    let current;\n    let previousIndex;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n    const total = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n    if (swiper.params.loop) {\n      previousIndex = swiper.previousRealIndex || 0;\n      current = swiper.params.slidesPerGroup > 1 ? Math.floor(swiper.realIndex / swiper.params.slidesPerGroup) : swiper.realIndex;\n    } else if (typeof swiper.snapIndex !== 'undefined') {\n      current = swiper.snapIndex;\n      previousIndex = swiper.previousSnapIndex;\n    } else {\n      previousIndex = swiper.previousIndex || 0;\n      current = swiper.activeIndex || 0;\n    }\n    // Types\n    if (params.type === 'bullets' && swiper.pagination.bullets && swiper.pagination.bullets.length > 0) {\n      const bullets = swiper.pagination.bullets;\n      let firstIndex;\n      let lastIndex;\n      let midIndex;\n      if (params.dynamicBullets) {\n        bulletSize = elementOuterSize(bullets[0], swiper.isHorizontal() ? 'width' : 'height', true);\n        el.forEach(subEl => {\n          subEl.style[swiper.isHorizontal() ? 'width' : 'height'] = `${bulletSize * (params.dynamicMainBullets + 4)}px`;\n        });\n        if (params.dynamicMainBullets > 1 && previousIndex !== undefined) {\n          dynamicBulletIndex += current - (previousIndex || 0);\n          if (dynamicBulletIndex > params.dynamicMainBullets - 1) {\n            dynamicBulletIndex = params.dynamicMainBullets - 1;\n          } else if (dynamicBulletIndex < 0) {\n            dynamicBulletIndex = 0;\n          }\n        }\n        firstIndex = Math.max(current - dynamicBulletIndex, 0);\n        lastIndex = firstIndex + (Math.min(bullets.length, params.dynamicMainBullets) - 1);\n        midIndex = (lastIndex + firstIndex) / 2;\n      }\n      bullets.forEach(bulletEl => {\n        const classesToRemove = [...['', '-next', '-next-next', '-prev', '-prev-prev', '-main'].map(suffix => `${params.bulletActiveClass}${suffix}`)].map(s => typeof s === 'string' && s.includes(' ') ? s.split(' ') : s).flat();\n        bulletEl.classList.remove(...classesToRemove);\n      });\n      if (el.length > 1) {\n        bullets.forEach(bullet => {\n          const bulletIndex = elementIndex(bullet);\n          if (bulletIndex === current) {\n            bullet.classList.add(...params.bulletActiveClass.split(' '));\n          } else if (swiper.isElement) {\n            bullet.setAttribute('part', 'bullet');\n          }\n          if (params.dynamicBullets) {\n            if (bulletIndex >= firstIndex && bulletIndex <= lastIndex) {\n              bullet.classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n            if (bulletIndex === firstIndex) {\n              setSideBullets(bullet, 'prev');\n            }\n            if (bulletIndex === lastIndex) {\n              setSideBullets(bullet, 'next');\n            }\n          }\n        });\n      } else {\n        const bullet = bullets[current];\n        if (bullet) {\n          bullet.classList.add(...params.bulletActiveClass.split(' '));\n        }\n        if (swiper.isElement) {\n          bullets.forEach((bulletEl, bulletIndex) => {\n            bulletEl.setAttribute('part', bulletIndex === current ? 'bullet-active' : 'bullet');\n          });\n        }\n        if (params.dynamicBullets) {\n          const firstDisplayedBullet = bullets[firstIndex];\n          const lastDisplayedBullet = bullets[lastIndex];\n          for (let i = firstIndex; i <= lastIndex; i += 1) {\n            if (bullets[i]) {\n              bullets[i].classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n          }\n          setSideBullets(firstDisplayedBullet, 'prev');\n          setSideBullets(lastDisplayedBullet, 'next');\n        }\n      }\n      if (params.dynamicBullets) {\n        const dynamicBulletsLength = Math.min(bullets.length, params.dynamicMainBullets + 4);\n        const bulletsOffset = (bulletSize * dynamicBulletsLength - bulletSize) / 2 - midIndex * bulletSize;\n        const offsetProp = rtl ? 'right' : 'left';\n        bullets.forEach(bullet => {\n          bullet.style[swiper.isHorizontal() ? offsetProp : 'top'] = `${bulletsOffset}px`;\n        });\n      }\n    }\n    el.forEach((subEl, subElIndex) => {\n      if (params.type === 'fraction') {\n        subEl.querySelectorAll(classesToSelector(params.currentClass)).forEach(fractionEl => {\n          fractionEl.textContent = params.formatFractionCurrent(current + 1);\n        });\n        subEl.querySelectorAll(classesToSelector(params.totalClass)).forEach(totalEl => {\n          totalEl.textContent = params.formatFractionTotal(total);\n        });\n      }\n      if (params.type === 'progressbar') {\n        let progressbarDirection;\n        if (params.progressbarOpposite) {\n          progressbarDirection = swiper.isHorizontal() ? 'vertical' : 'horizontal';\n        } else {\n          progressbarDirection = swiper.isHorizontal() ? 'horizontal' : 'vertical';\n        }\n        const scale = (current + 1) / total;\n        let scaleX = 1;\n        let scaleY = 1;\n        if (progressbarDirection === 'horizontal') {\n          scaleX = scale;\n        } else {\n          scaleY = scale;\n        }\n        subEl.querySelectorAll(classesToSelector(params.progressbarFillClass)).forEach(progressEl => {\n          progressEl.style.transform = `translate3d(0,0,0) scaleX(${scaleX}) scaleY(${scaleY})`;\n          progressEl.style.transitionDuration = `${swiper.params.speed}ms`;\n        });\n      }\n      if (params.type === 'custom' && params.renderCustom) {\n        setInnerHTML(subEl, params.renderCustom(swiper, current + 1, total));\n        if (subElIndex === 0) emit('paginationRender', subEl);\n      } else {\n        if (subElIndex === 0) emit('paginationRender', subEl);\n        emit('paginationUpdate', subEl);\n      }\n      if (swiper.params.watchOverflow && swiper.enabled) {\n        subEl.classList[swiper.isLocked ? 'add' : 'remove'](params.lockClass);\n      }\n    });\n  }\n  function render() {\n    // Render Container\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.grid && swiper.params.grid.rows > 1 ? swiper.slides.length / Math.ceil(swiper.params.grid.rows) : swiper.slides.length;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    let paginationHTML = '';\n    if (params.type === 'bullets') {\n      let numberOfBullets = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n      if (swiper.params.freeMode && swiper.params.freeMode.enabled && numberOfBullets > slidesLength) {\n        numberOfBullets = slidesLength;\n      }\n      for (let i = 0; i < numberOfBullets; i += 1) {\n        if (params.renderBullet) {\n          paginationHTML += params.renderBullet.call(swiper, i, params.bulletClass);\n        } else {\n          // prettier-ignore\n          paginationHTML += `<${params.bulletElement} ${swiper.isElement ? 'part=\"bullet\"' : ''} class=\"${params.bulletClass}\"></${params.bulletElement}>`;\n        }\n      }\n    }\n    if (params.type === 'fraction') {\n      if (params.renderFraction) {\n        paginationHTML = params.renderFraction.call(swiper, params.currentClass, params.totalClass);\n      } else {\n        paginationHTML = `<span class=\"${params.currentClass}\"></span>` + ' / ' + `<span class=\"${params.totalClass}\"></span>`;\n      }\n    }\n    if (params.type === 'progressbar') {\n      if (params.renderProgressbar) {\n        paginationHTML = params.renderProgressbar.call(swiper, params.progressbarFillClass);\n      } else {\n        paginationHTML = `<span class=\"${params.progressbarFillClass}\"></span>`;\n      }\n    }\n    swiper.pagination.bullets = [];\n    el.forEach(subEl => {\n      if (params.type !== 'custom') {\n        setInnerHTML(subEl, paginationHTML || '');\n      }\n      if (params.type === 'bullets') {\n        swiper.pagination.bullets.push(...subEl.querySelectorAll(classesToSelector(params.bulletClass)));\n      }\n    });\n    if (params.type !== 'custom') {\n      emit('paginationRender', el[0]);\n    }\n  }\n  function init() {\n    swiper.params.pagination = createElementIfNotDefined(swiper, swiper.originalParams.pagination, swiper.params.pagination, {\n      el: 'swiper-pagination'\n    });\n    const params = swiper.params.pagination;\n    if (!params.el) return;\n    let el;\n    if (typeof params.el === 'string' && swiper.isElement) {\n      el = swiper.el.querySelector(params.el);\n    }\n    if (!el && typeof params.el === 'string') {\n      el = [...document.querySelectorAll(params.el)];\n    }\n    if (!el) {\n      el = params.el;\n    }\n    if (!el || el.length === 0) return;\n    if (swiper.params.uniqueNavElements && typeof params.el === 'string' && Array.isArray(el) && el.length > 1) {\n      el = [...swiper.el.querySelectorAll(params.el)];\n      // check if it belongs to another nested Swiper\n      if (el.length > 1) {\n        el = el.find(subEl => {\n          if (elementParents(subEl, '.swiper')[0] !== swiper.el) return false;\n          return true;\n        });\n      }\n    }\n    if (Array.isArray(el) && el.length === 1) el = el[0];\n    Object.assign(swiper.pagination, {\n      el\n    });\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      if (params.type === 'bullets' && params.clickable) {\n        subEl.classList.add(...(params.clickableClass || '').split(' '));\n      }\n      subEl.classList.add(params.modifierClass + params.type);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n      if (params.type === 'bullets' && params.dynamicBullets) {\n        subEl.classList.add(`${params.modifierClass}${params.type}-dynamic`);\n        dynamicBulletIndex = 0;\n        if (params.dynamicMainBullets < 1) {\n          params.dynamicMainBullets = 1;\n        }\n      }\n      if (params.type === 'progressbar' && params.progressbarOpposite) {\n        subEl.classList.add(params.progressbarOppositeClass);\n      }\n      if (params.clickable) {\n        subEl.addEventListener('click', onBulletClick);\n      }\n      if (!swiper.enabled) {\n        subEl.classList.add(params.lockClass);\n      }\n    });\n  }\n  function destroy() {\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => {\n        subEl.classList.remove(params.hiddenClass);\n        subEl.classList.remove(params.modifierClass + params.type);\n        subEl.classList.remove(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n        if (params.clickable) {\n          subEl.classList.remove(...(params.clickableClass || '').split(' '));\n          subEl.removeEventListener('click', onBulletClick);\n        }\n      });\n    }\n    if (swiper.pagination.bullets) swiper.pagination.bullets.forEach(subEl => subEl.classList.remove(...params.bulletActiveClass.split(' ')));\n  }\n  on('changeDirection', () => {\n    if (!swiper.pagination || !swiper.pagination.el) return;\n    const params = swiper.params.pagination;\n    let {\n      el\n    } = swiper.pagination;\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.classList.remove(params.horizontalClass, params.verticalClass);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    });\n  });\n  on('init', () => {\n    if (swiper.params.pagination.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      render();\n      update();\n    }\n  });\n  on('activeIndexChange', () => {\n    if (typeof swiper.snapIndex === 'undefined') {\n      update();\n    }\n  });\n  on('snapIndexChange', () => {\n    update();\n  });\n  on('snapGridLengthChange', () => {\n    render();\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.pagination.lockClass));\n    }\n  });\n  on('lock unlock', () => {\n    update();\n  });\n  on('click', (_s, e) => {\n    const targetEl = e.target;\n    const el = makeElementsArray(swiper.pagination.el);\n    if (swiper.params.pagination.el && swiper.params.pagination.hideOnClick && el && el.length > 0 && !targetEl.classList.contains(swiper.params.pagination.bulletClass)) {\n      if (swiper.navigation && (swiper.navigation.nextEl && targetEl === swiper.navigation.nextEl || swiper.navigation.prevEl && targetEl === swiper.navigation.prevEl)) return;\n      const isHidden = el[0].classList.contains(swiper.params.pagination.hiddenClass);\n      if (isHidden === true) {\n        emit('paginationShow');\n      } else {\n        emit('paginationHide');\n      }\n      el.forEach(subEl => subEl.classList.toggle(swiper.params.pagination.hiddenClass));\n    }\n  });\n  const enable = () => {\n    swiper.el.classList.remove(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.remove(swiper.params.pagination.paginationDisabledClass));\n    }\n    init();\n    render();\n    update();\n  };\n  const disable = () => {\n    swiper.el.classList.add(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.add(swiper.params.pagination.paginationDisabledClass));\n    }\n    destroy();\n  };\n  Object.assign(swiper.pagination, {\n    enable,\n    disable,\n    render,\n    update,\n    init,\n    destroy\n  });\n}\n\nexport { Pagination as default };\n", "import { g as getDocument } from '../shared/ssr-window.esm.mjs';\n\n/* eslint no-underscore-dangle: \"off\" */\n/* eslint no-use-before-define: \"off\" */\nfunction Autoplay(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit,\n    params\n  } = _ref;\n  swiper.autoplay = {\n    running: false,\n    paused: false,\n    timeLeft: 0\n  };\n  extendParams({\n    autoplay: {\n      enabled: false,\n      delay: 3000,\n      waitForTransition: true,\n      disableOnInteraction: false,\n      stopOnLastSlide: false,\n      reverseDirection: false,\n      pauseOnMouseEnter: false\n    }\n  });\n  let timeout;\n  let raf;\n  let autoplayDelayTotal = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayDelayCurrent = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayTimeLeft;\n  let autoplayStartTime = new Date().getTime();\n  let wasPaused;\n  let isTouched;\n  let pausedByTouch;\n  let touchStartTimeout;\n  let slideChanged;\n  let pausedByInteraction;\n  let pausedByPointerEnter;\n  function onTransitionEnd(e) {\n    if (!swiper || swiper.destroyed || !swiper.wrapperEl) return;\n    if (e.target !== swiper.wrapperEl) return;\n    swiper.wrapperEl.removeEventListener('transitionend', onTransitionEnd);\n    if (pausedByPointerEnter || e.detail && e.detail.bySwiperTouchMove) {\n      return;\n    }\n    resume();\n  }\n  const calcTimeLeft = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.autoplay.paused) {\n      wasPaused = true;\n    } else if (wasPaused) {\n      autoplayDelayCurrent = autoplayTimeLeft;\n      wasPaused = false;\n    }\n    const timeLeft = swiper.autoplay.paused ? autoplayTimeLeft : autoplayStartTime + autoplayDelayCurrent - new Date().getTime();\n    swiper.autoplay.timeLeft = timeLeft;\n    emit('autoplayTimeLeft', timeLeft, timeLeft / autoplayDelayTotal);\n    raf = requestAnimationFrame(() => {\n      calcTimeLeft();\n    });\n  };\n  const getSlideDelay = () => {\n    let activeSlideEl;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      activeSlideEl = swiper.slides.find(slideEl => slideEl.classList.contains('swiper-slide-active'));\n    } else {\n      activeSlideEl = swiper.slides[swiper.activeIndex];\n    }\n    if (!activeSlideEl) return undefined;\n    const currentSlideDelay = parseInt(activeSlideEl.getAttribute('data-swiper-autoplay'), 10);\n    return currentSlideDelay;\n  };\n  const run = delayForce => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    cancelAnimationFrame(raf);\n    calcTimeLeft();\n    let delay = typeof delayForce === 'undefined' ? swiper.params.autoplay.delay : delayForce;\n    autoplayDelayTotal = swiper.params.autoplay.delay;\n    autoplayDelayCurrent = swiper.params.autoplay.delay;\n    const currentSlideDelay = getSlideDelay();\n    if (!Number.isNaN(currentSlideDelay) && currentSlideDelay > 0 && typeof delayForce === 'undefined') {\n      delay = currentSlideDelay;\n      autoplayDelayTotal = currentSlideDelay;\n      autoplayDelayCurrent = currentSlideDelay;\n    }\n    autoplayTimeLeft = delay;\n    const speed = swiper.params.speed;\n    const proceed = () => {\n      if (!swiper || swiper.destroyed) return;\n      if (swiper.params.autoplay.reverseDirection) {\n        if (!swiper.isBeginning || swiper.params.loop || swiper.params.rewind) {\n          swiper.slidePrev(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(swiper.slides.length - 1, speed, true, true);\n          emit('autoplay');\n        }\n      } else {\n        if (!swiper.isEnd || swiper.params.loop || swiper.params.rewind) {\n          swiper.slideNext(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(0, speed, true, true);\n          emit('autoplay');\n        }\n      }\n      if (swiper.params.cssMode) {\n        autoplayStartTime = new Date().getTime();\n        requestAnimationFrame(() => {\n          run();\n        });\n      }\n    };\n    if (delay > 0) {\n      clearTimeout(timeout);\n      timeout = setTimeout(() => {\n        proceed();\n      }, delay);\n    } else {\n      requestAnimationFrame(() => {\n        proceed();\n      });\n    }\n\n    // eslint-disable-next-line\n    return delay;\n  };\n  const start = () => {\n    autoplayStartTime = new Date().getTime();\n    swiper.autoplay.running = true;\n    run();\n    emit('autoplayStart');\n  };\n  const stop = () => {\n    swiper.autoplay.running = false;\n    clearTimeout(timeout);\n    cancelAnimationFrame(raf);\n    emit('autoplayStop');\n  };\n  const pause = (internal, reset) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    clearTimeout(timeout);\n    if (!internal) {\n      pausedByInteraction = true;\n    }\n    const proceed = () => {\n      emit('autoplayPause');\n      if (swiper.params.autoplay.waitForTransition) {\n        swiper.wrapperEl.addEventListener('transitionend', onTransitionEnd);\n      } else {\n        resume();\n      }\n    };\n    swiper.autoplay.paused = true;\n    if (reset) {\n      if (slideChanged) {\n        autoplayTimeLeft = swiper.params.autoplay.delay;\n      }\n      slideChanged = false;\n      proceed();\n      return;\n    }\n    const delay = autoplayTimeLeft || swiper.params.autoplay.delay;\n    autoplayTimeLeft = delay - (new Date().getTime() - autoplayStartTime);\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop) return;\n    if (autoplayTimeLeft < 0) autoplayTimeLeft = 0;\n    proceed();\n  };\n  const resume = () => {\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop || swiper.destroyed || !swiper.autoplay.running) return;\n    autoplayStartTime = new Date().getTime();\n    if (pausedByInteraction) {\n      pausedByInteraction = false;\n      run(autoplayTimeLeft);\n    } else {\n      run();\n    }\n    swiper.autoplay.paused = false;\n    emit('autoplayResume');\n  };\n  const onVisibilityChange = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    const document = getDocument();\n    if (document.visibilityState === 'hidden') {\n      pausedByInteraction = true;\n      pause(true);\n    }\n    if (document.visibilityState === 'visible') {\n      resume();\n    }\n  };\n  const onPointerEnter = e => {\n    if (e.pointerType !== 'mouse') return;\n    pausedByInteraction = true;\n    pausedByPointerEnter = true;\n    if (swiper.animating || swiper.autoplay.paused) return;\n    pause(true);\n  };\n  const onPointerLeave = e => {\n    if (e.pointerType !== 'mouse') return;\n    pausedByPointerEnter = false;\n    if (swiper.autoplay.paused) {\n      resume();\n    }\n  };\n  const attachMouseEvents = () => {\n    if (swiper.params.autoplay.pauseOnMouseEnter) {\n      swiper.el.addEventListener('pointerenter', onPointerEnter);\n      swiper.el.addEventListener('pointerleave', onPointerLeave);\n    }\n  };\n  const detachMouseEvents = () => {\n    if (swiper.el && typeof swiper.el !== 'string') {\n      swiper.el.removeEventListener('pointerenter', onPointerEnter);\n      swiper.el.removeEventListener('pointerleave', onPointerLeave);\n    }\n  };\n  const attachDocumentEvents = () => {\n    const document = getDocument();\n    document.addEventListener('visibilitychange', onVisibilityChange);\n  };\n  const detachDocumentEvents = () => {\n    const document = getDocument();\n    document.removeEventListener('visibilitychange', onVisibilityChange);\n  };\n  on('init', () => {\n    if (swiper.params.autoplay.enabled) {\n      attachMouseEvents();\n      attachDocumentEvents();\n      start();\n    }\n  });\n  on('destroy', () => {\n    detachMouseEvents();\n    detachDocumentEvents();\n    if (swiper.autoplay.running) {\n      stop();\n    }\n  });\n  on('_freeModeStaticRelease', () => {\n    if (pausedByTouch || pausedByInteraction) {\n      resume();\n    }\n  });\n  on('_freeModeNoMomentumRelease', () => {\n    if (!swiper.params.autoplay.disableOnInteraction) {\n      pause(true, true);\n    } else {\n      stop();\n    }\n  });\n  on('beforeTransitionStart', (_s, speed, internal) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (internal || !swiper.params.autoplay.disableOnInteraction) {\n      pause(true, true);\n    } else {\n      stop();\n    }\n  });\n  on('sliderFirstMove', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.params.autoplay.disableOnInteraction) {\n      stop();\n      return;\n    }\n    isTouched = true;\n    pausedByTouch = false;\n    pausedByInteraction = false;\n    touchStartTimeout = setTimeout(() => {\n      pausedByInteraction = true;\n      pausedByTouch = true;\n      pause(true);\n    }, 200);\n  });\n  on('touchEnd', () => {\n    if (swiper.destroyed || !swiper.autoplay.running || !isTouched) return;\n    clearTimeout(touchStartTimeout);\n    clearTimeout(timeout);\n    if (swiper.params.autoplay.disableOnInteraction) {\n      pausedByTouch = false;\n      isTouched = false;\n      return;\n    }\n    if (pausedByTouch && swiper.params.cssMode) resume();\n    pausedByTouch = false;\n    isTouched = false;\n  });\n  on('slideChange', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    slideChanged = true;\n  });\n  Object.assign(swiper.autoplay, {\n    start,\n    stop,\n    pause,\n    resume\n  });\n}\n\nexport { Autoplay as default };\n", "import type { DOMElementOptions, StylesObject } from '../../common/config/types';\n\n/**\n * DOM utility functions for safe DOM manipulation\n */\n\n/**\n * Safely query a single element\n */\nexport const querySelector = (selector: string): Element | false => {\n    try {\n        return document.querySelector(selector) || false;\n    } catch {\n        return false;\n    }\n};\n\n/**\n * Safely query multiple elements\n */\nexport const querySelectorAll = (selector: string): NodeListOf<Element> => {\n    try {\n        return document.querySelectorAll(selector);\n    } catch {\n        return document.querySelectorAll(''); // Returns empty NodeList\n    }\n};\n\n/**\n * Safely get element by ID\n */\nexport const getElementById = (id: string): HTMLElement | false => {\n    try {\n        return document.getElementById(id) || false;\n    } catch {\n        return false;\n    }\n};\n\n/**\n * Safely create element with options\n */\nexport const createElement = (\n    tagName: string,\n    options: DOMElementOptions = {},\n    innerHTML = ''\n): HTMLElement => {\n    try {\n        const element = document.createElement(tagName);\n\n        // Set attributes\n        for (const [key, value] of Object.entries(options)) {\n            if (key === 'className') {\n                element.className = String(value);\n            } else if (key === 'id') {\n                element.id = String(value);\n            } else {\n                element.setAttribute(key, String(value));\n            }\n        }\n\n        if (innerHTML) {\n            element.innerHTML = innerHTML;\n        }\n\n        return element;\n    } catch {\n        return document.createElement('div'); // Fallback\n    }\n};\n\n/**\n * Safely append child element\n */\nexport const appendChild = (parent: Element, child: Element): void => {\n    try {\n        parent.appendChild(child);\n    } catch {\n        // Silently handle append errors\n    }\n};\n\n/**\n * Safely prepend child element\n */\nexport const prependChild = (parent: Element, child: Element): void => {\n    try {\n        parent.prepend(child);\n    } catch {\n        // Silently handle prepend errors\n    }\n};\n\n/**\n * Safely remove element\n */\nexport const removeElement = (element: Element): void => {\n    try {\n        element.remove();\n    } catch {\n        // Silently handle removal errors\n    }\n};\n\n/**\n * Safely set styles on element\n */\nexport const setStyles = (element: HTMLElement, styles: StylesObject): void => {\n    try {\n        for (const [property, value] of Object.entries(styles)) {\n            element.style.setProperty(property, String(value));\n        }\n    } catch {\n        // Silently handle style errors\n    }\n};\n", "/**\n * Application constants for Header Advertisement extension\n */\n\n// Mobile detection constants\nexport const MO<PERSON>LE_DETECTION = {\n  USER_AGENT_SUBSTR_START: 0,\n  USER_AGENT_SUBSTR_LENGTH: 4,\n} as const;\n\n// Error handling constants\nexport const ERROR_HANDLING = {\n  MAX_ERROR_LOG_ENTRIES: 50,\n  DOM_READY_TIMEOUT: 5000,\n  SLIDE_NUMBER_MIN: 1,\n  SLIDE_NUMBER_MAX: 30,\n  TRANSITION_TIME_MIN: 1000,\n  TRANSITION_TIME_MAX: 30_000,\n  CONFIG_MAX_SLIDES_MIN: 1,\n  CONFIG_MAX_SLIDES_MAX: 50,\n} as const;\n\n// Admin component constants\nexport const ADMIN_CONSTANTS = {\n  SAVE_DEBOUNCE_DELAY: 500,\n  DEFAULT_MAX_SLIDES: 30,\n  EMPTY_SLIDES_COUNT: 0,\n} as const;\n\n// UI styling constants\nexport const UI_STYLES = {\n  HEADER_ICON_HEIGHT: 24,\n  HEADER_ICON_MARGIN_TOP: 8,\n} as const;\n\n// Mobile layout constants\nexport const MOBILE_LAYOUT = {\n  SCREEN_WIDTH_MULTIPLIER: 2,\n  SCREEN_WIDTH_OFFSET: 50,\n  CONTAINER_MARGIN_MULTIPLIER: 0.254,\n} as const;\n\n// Slideshow constants\nexport const SLIDESHOW_CONSTANTS = {\n  SLIDE_INCREMENT: 1,\n  INITIAL_SLIDE_INDEX: 1,\n  VALIDATION_ERRORS_EMPTY: 0,\n} as const;\n\n// Array and index constants\nexport const ARRAY_CONSTANTS = {\n  EMPTY_LENGTH: 0,\n  FIRST_INDEX: 0,\n  NOT_FOUND_INDEX: -1,\n  NEXT_ITEM_OFFSET: 1,\n  LAST_ITEM_OFFSET: -1,\n} as const;\n\n// Timing constants\nexport const TIMING = {\n  CHECK_INTERVAL: 10,\n  DATA_CHECK_INTERVAL: 100,\n  DEFAULT_TRANSITION_TIME: 5000,\n} as const;\n\n// DOM element constants\nexport const DOM_ELEMENTS = {\n  SWIPER_AD_CONTAINER_ID: 'swiperAdContainer',\n  HEADER_ICON_ID: 'wusong8899HeaderAdvIcon',\n} as const;\n\n// CSS class constants\nexport const CSS_CLASSES = {\n  SWIPER: 'swiper',\n  SWIPER_WRAPPER: 'swiper-wrapper',\n  SWIPER_SLIDE: 'swiper-slide',\n  SWIPER_BUTTON_NEXT: 'swiper-button-next',\n  SWIPER_BUTTON_PREV: 'swiper-button-prev',\n  SWIPER_PAGINATION: 'swiper-pagination',\n  AD_SWIPER: 'adSwiper',\n} as const;\n\n// CSS selector constants\nexport const CSS_SELECTORS = {\n  APP_NAVIGATION_BACK_CONTROL: '#app-navigation .App-backControl',\n  CONTENT_CONTAINER: '#content .container',\n  NAV_ITEMS: '.item-nav',\n  SWIPER_PAGINATION_EL: '.swiper-pagination',\n  SWIPER_BUTTON_NEXT_EL: '.swiper-button-next',\n  SWIPER_BUTTON_PREV_EL: '.swiper-button-prev',\n} as const;\n\n\n\n// Extension configuration constants\nexport const EXTENSION_CONFIG = {\n  ID: 'wusong8899-header-advertisement',\n  TRANSLATION_PREFIX: 'wusong8899-header-advertisement',\n  MAX_SLIDES: 30,\n  HEADER_ICON_URL: 'https://ex.cc/assets/files/date/test.png',\n} as const;\n", "import { MOBILE_DETECTION } from '../../common/config/constants';\n\n/**\n * Mobile detection utility functions\n */\n\n/**\n * Check if the current device is mobile\n */\nexport const isMobileDevice = (): boolean => {\n    try {\n        const { userAgent } = navigator;\n        const mobileIndicator = userAgent.substring(\n            MOBILE_DETECTION.USER_AGENT_SUBSTR_START,\n            MOBILE_DETECTION.USER_AGENT_SUBSTR_LENGTH\n        );\n        return mobileIndicator === 'Mobi';\n    } catch {\n        return false;\n    }\n};\n", "import type { RootConfig, Environment } from './types';\nimport {\n  EXTENSION_CONFIG,\n  TIMING,\n  DOM_ELEMENTS,\n  CSS_CLASSES,\n  CSS_SELECTORS\n} from './constants';\n\nexport const defaultConfig: RootConfig = {\n  env: (process.env.NODE_ENV as Environment) || 'production',\n  app: {\n    extensionId: EXTENSION_CONFIG.ID,\n    translationPrefix: EXTENSION_CONFIG.TRANSLATION_PREFIX,\n  },\n  slider: {\n    maxSlides: EXTENSION_CONFIG.MAX_SLIDES,\n    defaultTransitionTime: TIMING.DEFAULT_TRANSITION_TIME,\n    checkTime: TIMING.CHECK_INTERVAL,\n    dataCheckInterval: TIMING.DATA_CHECK_INTERVAL,\n    dom: {\n      containerId: DOM_ELEMENTS.SWIPER_AD_CONTAINER_ID,\n      swiperClass: CSS_CLASSES.AD_SWIPER,\n    },\n    swiper: {\n      spaceBetween: 15,\n      effect: 'slide',\n      centeredSlides: true,\n      slidesPerView: 'auto',\n      pagination: {\n        el: CSS_SELECTORS.SWIPER_PAGINATION_EL,\n        type: 'bullets',\n      },\n      navigation: {\n        nextEl: CSS_SELECTORS.SWIPER_BUTTON_NEXT_EL,\n        prevEl: CSS_SELECTORS.SWIPER_BUTTON_PREV_EL,\n      },\n    },\n  },\n  ui: {\n    headerIconId: DOM_ELEMENTS.HEADER_ICON_ID,\n    headerIconUrl: EXTENSION_CONFIG.HEADER_ICON_URL,\n  },\n};\n", "import Swiper from 'swiper';\nimport { Navigation, Pagination, Autoplay } from 'swiper/modules';\nimport app from 'flarum/forum/app';\nimport * as DOMUtils from '../utils/dom-utils';\nimport { isMobileDevice } from '../utils/mobile-detection';\nimport { defaultConfig } from '../../common/config';\nimport { SLIDESHOW_CONSTANTS } from '../../common/config/constants';\nimport type { FlarumVnode } from '../../common/config/types';\n\n/**\n * Swiper configuration constants\n */\nconst SWIPER_CONFIG_CONSTANTS = {\n    MIN_SLIDES_FOR_DUAL_VIEW: 8,\n    MIN_SLIDES_FOR_SINGLE_VIEW_LOOP: 4,\n} as const;\n\n\n\n/**\n * Slideshow manager for header advertisements\n */\nexport class SlideshowManager {\n    private swiper: Swiper | undefined;\n    private container: HTMLElement | undefined;\n    private readonly maxSlides = defaultConfig.slider.maxSlides;\n    private readonly checkTime = defaultConfig.slider.checkTime;\n\n    /**\n     * Safely read a forum attribute if available\n     */\n    private getForumAttribute(key: string): unknown {\n        try {\n            const forum = app && app.forum;\n            const attrFn = forum && forum.attribute;\n            if (typeof attrFn === 'function') {\n                return attrFn.call(forum, key);\n            }\n            return;\n        } catch {\n            return;\n        }\n    }\n\n    /**\n     * Initialize and attach slideshow to the DOM\n     */\n    attachAdvertiseHeader(_vdom: FlarumVnode): void {\n        try {\n            this.destroy(); // Clean up any existing instance\n\n            const container = this.createContainer();\n            const swiper = this.createSwiperElement(container);\n            const wrapper = this.createSwiperWrapper(swiper);\n\n            const slideCount = this.populateSlides(wrapper);\n            this.createPagination(swiper);\n            this.createNavigation(swiper);\n\n            this.container = container;\n            this.appendToDOM(container);\n\n            // Initialize Swiper after DOM attachment with slide count\n            setTimeout(() => {\n                this.initializeSwiper(this.getTransitionTime(), slideCount);\n            }, this.checkTime);\n        } catch {\n            // Silently handle slideshow creation errors\n        }\n    }\n\n    /**\n     * Remove existing navigation elements\n     */\n    private removeExistingNavigation(): void {\n        const existingContainer = DOMUtils.querySelector(`#${defaultConfig.slider.dom.containerId}`);\n        if (existingContainer) {\n            DOMUtils.removeElement(existingContainer);\n        }\n\n        const navElements = DOMUtils.querySelectorAll(\".item-nav\");\n        for (const element of navElements) {\n            DOMUtils.removeElement(element);\n        }\n    }\n\n    /**\n     * Create main container element\n     * @returns Container element\n     */\n    private createContainer(): HTMLElement {\n        this.removeExistingNavigation();\n\n        const container = DOMUtils.createElement('div', {\n            id: defaultConfig.slider.dom.containerId,\n            className: 'adContainer'\n        });\n\n        // Force essential container styles with !important to ensure visibility\n        container.style.cssText = `\n            display: block !important;\n            min-height: 300px !important;\n            background: transparent !important;\n            border: none !important;\n            padding: 0 !important;\n            max-width: 1200px !important;\n            width: 100% !important;\n            margin: 0 auto 20px auto !important;\n            overflow: hidden !important;\n        `;\n        return container;\n    }\n\n\n\n    /**\n     * Create Swiper element\n     * @param {HTMLElement} container - Parent container\n     * @returns {HTMLElement} Swiper element\n     */\n    private createSwiperElement(container: HTMLElement): HTMLElement {\n        const swiper = DOMUtils.createElement('div', {\n            className: `swiper ${defaultConfig.slider.dom.swiperClass}`\n        });\n\n        // Add inline styles to ensure visibility with !important\n        let height = '300px';\n        if (isMobileDevice()) {\n            height = '200px';\n        }\n\n        // Force styles with !important to override any conflicting CSS\n        swiper.style.cssText = `\n            width: 100% !important;\n            height: ${height} !important;\n            position: relative !important;\n            display: block !important;\n            min-height: ${height} !important;\n            background: transparent !important;\n            border: none !important;\n            border-radius: 12px !important;\n            overflow: hidden !important;\n        `;\n\n        DOMUtils.appendChild(container, swiper);\n        return swiper;\n    }\n\n    /**\n     * Create Swiper wrapper\n     * @param {HTMLElement} swiper - Swiper element\n     * @returns {HTMLElement} Wrapper element\n     */\n    private createSwiperWrapper(swiper: HTMLElement): HTMLElement {\n        const wrapper = DOMUtils.createElement('div', {\n            className: 'swiper-wrapper'\n        });\n\n        // Add inline styles to ensure wrapper visibility\n        DOMUtils.setStyles(wrapper, {\n            'width': '100%',\n            'height': '100%',\n            'display': 'flex'\n        });\n\n        DOMUtils.appendChild(swiper, wrapper);\n        return wrapper;\n    }\n\n    /**\n     * Get transition time from forum settings\n     * @returns Transition time in milliseconds\n     */\n    private getTransitionTime(): number {\n        const transitionTime = this.getForumAttribute('wusong8899-header-advertisement.TransitionTime');\n        if (transitionTime) {\n            return Number.parseInt(String(transitionTime), 10);\n        }\n        return defaultConfig.slider.defaultTransitionTime;\n    }\n\n    /**\n     * Populate slides with data from forum settings\n     * @param {HTMLElement} wrapper - Swiper wrapper element\n     * @returns {number} Number of slides created\n     */\n    private populateSlides(wrapper: HTMLElement): number {\n        let slideCount = 0;\n        for (let slideIndex = SLIDESHOW_CONSTANTS.INITIAL_SLIDE_INDEX; slideIndex <= this.maxSlides; slideIndex += SLIDESHOW_CONSTANTS.SLIDE_INCREMENT) {\n            const imageSrc = this.getForumAttribute(`wusong8899-header-advertisement.Image${slideIndex}`);\n            const imageLink = this.getForumAttribute(`wusong8899-header-advertisement.Link${slideIndex}`);\n\n            if (imageSrc) {\n                const slide = this.createSlide(String(imageSrc), String(imageLink || ''));\n                DOMUtils.appendChild(wrapper, slide);\n                slideCount += SLIDESHOW_CONSTANTS.SLIDE_INCREMENT;\n            }\n        }\n        return slideCount;\n    }\n\n    /**\n     * Create individual slide\n     * @param {string} imageSrc - Image source URL\n     * @param {string} imageLink - Link URL\n     * @returns {HTMLElement} Slide element\n     */\n    private createSlide(imageSrc: string, imageLink: string): HTMLElement {\n        const slide = DOMUtils.createElement('div', {\n            className: 'swiper-slide'\n        });\n\n        // Add inline styles to ensure slide visibility\n        DOMUtils.setStyles(slide, {\n            'width': '100%',\n            'height': '100%',\n            'display': 'flex',\n            'align-items': 'center',\n            'justify-content': 'center'\n        });\n\n        let clickHandler = '';\n        if (imageLink) {\n            clickHandler = `window.location.href=\"${imageLink}\"`;\n        }\n\n        // Create image with proper styling\n        slide.innerHTML = `<img onclick='${clickHandler}' src='${imageSrc}' style='width: 100%; height: 100%; object-fit: cover; display: block;' />`;\n\n        return slide;\n    }\n\n    /**\n     * Create pagination element\n     * @param {HTMLElement} swiper - Swiper element\n     */\n    private createPagination(swiper: HTMLElement): void {\n        const pagination = DOMUtils.createElement('div', {\n            className: 'swiper-pagination'\n        });\n        DOMUtils.appendChild(swiper, pagination);\n    }\n\n    /**\n     * Create navigation elements\n     * @param {HTMLElement} swiper - Swiper element\n     */\n    private createNavigation(swiper: HTMLElement): void {\n        const prevButton = DOMUtils.createElement('div', {\n            className: 'swiper-button-prev'\n        });\n        const nextButton = DOMUtils.createElement('div', {\n            className: 'swiper-button-next'\n        });\n\n        // Style navigation buttons\n        DOMUtils.setStyles(prevButton, {\n            'position': 'absolute',\n            'top': '50%',\n            'left': '15px',\n            'transform': 'translateY(-50%)',\n            'z-index': '100',\n            'width': '40px',\n            'height': '40px',\n            'background': 'rgba(255,255,255,0.9)',\n            'color': '#333',\n            'border': '1px solid rgba(0,0,0,0.1)',\n            'border-radius': '50%',\n            'cursor': 'pointer',\n            'display': 'flex',\n            'align-items': 'center',\n            'justify-content': 'center',\n            'font-size': '18px',\n            'font-weight': 'bold',\n            'box-shadow': '0 2px 8px rgba(0,0,0,0.15)',\n            'transition': 'all 0.3s ease'\n        });\n\n        DOMUtils.setStyles(nextButton, {\n            'position': 'absolute',\n            'top': '50%',\n            'right': '15px',\n            'transform': 'translateY(-50%)',\n            'z-index': '100',\n            'width': '40px',\n            'height': '40px',\n            'background': 'rgba(255,255,255,0.9)',\n            'color': '#333',\n            'border': '1px solid rgba(0,0,0,0.1)',\n            'border-radius': '50%',\n            'cursor': 'pointer',\n            'display': 'flex',\n            'align-items': 'center',\n            'justify-content': 'center',\n            'font-size': '18px',\n            'font-weight': 'bold',\n            'box-shadow': '0 2px 8px rgba(0,0,0,0.15)',\n            'transition': 'all 0.3s ease'\n        });\n\n        // Add hover effects\n        prevButton.addEventListener('mouseenter', () => {\n            DOMUtils.setStyles(prevButton, {\n                'background': 'rgba(255,255,255,1)',\n                'transform': 'translateY(-50%) scale(1.1)'\n            });\n        });\n\n        prevButton.addEventListener('mouseleave', () => {\n            DOMUtils.setStyles(prevButton, {\n                'background': 'rgba(255,255,255,0.9)',\n                'transform': 'translateY(-50%) scale(1)'\n            });\n        });\n\n        nextButton.addEventListener('mouseenter', () => {\n            DOMUtils.setStyles(nextButton, {\n                'background': 'rgba(255,255,255,1)',\n                'transform': 'translateY(-50%) scale(1.1)'\n            });\n        });\n\n        nextButton.addEventListener('mouseleave', () => {\n            DOMUtils.setStyles(nextButton, {\n                'background': 'rgba(255,255,255,0.9)',\n                'transform': 'translateY(-50%) scale(1)'\n            });\n        });\n\n        DOMUtils.appendChild(swiper, prevButton);\n        DOMUtils.appendChild(swiper, nextButton);\n    }\n\n    /**\n     * Append slideshow to DOM\n     * @param {HTMLElement} container - Container element\n     */\n    private appendToDOM(container: HTMLElement): void {\n        const contentContainer = DOMUtils.querySelector(\"#content .container\");\n        if (contentContainer) {\n            DOMUtils.prependChild(contentContainer, container);\n        }\n    }\n\n    /**\n     * Initialize Swiper instance\n     * @param {number} transitionTime - Transition time in milliseconds\n     * @param {number} slideCount - Number of slides available\n     */\n    private initializeSwiper(transitionTime: number, slideCount: number): void {\n        try {\n            // Calculate optimal configuration based on slide count\n            const swiperConfig = this.calculateSwiperConfig(slideCount);\n\n            this.swiper = new Swiper(`.${defaultConfig.slider.dom.swiperClass}`, {\n                loop: swiperConfig.enableLoop,\n                autoplay: {\n                    delay: transitionTime,\n                    disableOnInteraction: false,\n                },\n                spaceBetween: defaultConfig.slider.swiper.spaceBetween,\n                effect: defaultConfig.slider.swiper.effect,\n                centeredSlides: swiperConfig.centeredSlides,\n                slidesPerView: swiperConfig.slidesPerView,\n                pagination: {\n                    el: defaultConfig.slider.swiper.pagination.el,\n                    type: defaultConfig.slider.swiper.pagination.type,\n                    clickable: true,\n                },\n                navigation: {\n                    nextEl: defaultConfig.slider.swiper.navigation.nextEl,\n                    prevEl: defaultConfig.slider.swiper.navigation.prevEl,\n                },\n                initialSlide: 0,\n                modules: [Navigation, Pagination, Autoplay]\n            });\n        } catch {\n            // Silently handle Swiper initialization errors\n        }\n    }\n\n    /**\n     * Calculate optimal Swiper configuration based on slide count\n     * @param {number} slideCount - Number of available slides\n     * @returns {object} Swiper configuration object\n     */\n    private calculateSwiperConfig(slideCount: number): {\n        enableLoop: boolean;\n        slidesPerView: number | 'auto';\n        centeredSlides: boolean;\n    } {\n        // Swiper loop mode requirements:\n        // - For slidesPerView: 'auto', need at least 6-8 slides for smooth loop\n        // - For slidesPerView: 1, need at least 3-4 slides for smooth loop\n\n        if (slideCount >= SWIPER_CONFIG_CONSTANTS.MIN_SLIDES_FOR_DUAL_VIEW) {\n            // Enough slides for auto view with loop - let Swiper calculate optimal display\n            return {\n                enableLoop: true,\n                slidesPerView: 'auto',\n                centeredSlides: true\n            };\n        } else if (slideCount >= SWIPER_CONFIG_CONSTANTS.MIN_SLIDES_FOR_SINGLE_VIEW_LOOP) {\n            // Use single slide view with loop for better control\n            return {\n                enableLoop: true,\n                slidesPerView: 1,\n                centeredSlides: true\n            };\n        } else {\n            // Too few slides for loop mode\n            return {\n                enableLoop: false,\n                slidesPerView: 1,\n                centeredSlides: true\n            };\n        }\n    }\n\n    /**\n     * Destroy slideshow instance\n     */\n    destroy(): void {\n        if (this.swiper) {\n            this.swiper.destroy(true, true);\n            delete this.swiper;\n        }\n\n        if (this.container) {\n            DOMUtils.removeElement(this.container);\n            delete this.container;\n        }\n    }\n}\n", "import { ERROR_HANDLING } from '../../common/config/constants';\nimport type { ErrorLogEntry } from '../../common/config/types';\n\n/**\n * Error handling utility for the Header Advertisement extension\n */\nexport class ErrorHandler {\n    private static instance: ErrorHandler;\n    private errorLog: ErrorLogEntry[] = [];\n    private isInitialized = false;\n\n    private constructor() {\n        // Private constructor for singleton pattern\n    }\n\n    /**\n     * Get singleton instance\n     */\n    public static getInstance(): ErrorHandler {\n        if (!ErrorHandler.instance) {\n            ErrorHandler.instance = new ErrorHandler();\n        }\n        return ErrorHandler.instance;\n    }\n\n    /**\n     * Initialize error handler\n     */\n    public initialize(): boolean {\n        try {\n            if (this.isInitialized) {\n                return true;\n            }\n\n            // Set up global error handling\n            this.setupGlobalErrorHandling();\n            this.isInitialized = true;\n            return true;\n        } catch {\n            return false;\n        }\n    }\n\n    /**\n     * Handle synchronous errors\n     */\n    public handleSync<TResult>(fn: () => TResult, context: string): TResult | false {\n        try {\n            return fn();\n        } catch (error) {\n            this.logError(error as Error, context);\n            return false;\n        }\n    }\n\n    /**\n     * Handle asynchronous errors\n     */\n    public handleAsync<TResult>(fn: () => Promise<TResult>, context: string): Promise<TResult | false> {\n        return fn().catch((error) => {\n            this.logError(error as Error, context);\n            return false;\n        });\n    }\n\n    /**\n     * Log error with context\n     */\n    private logError(error: Error, context: string): void {\n        try {\n            const entry: ErrorLogEntry = {\n                timestamp: new Date(),\n                error,\n                context,\n            };\n\n            this.errorLog.push(entry);\n\n            // Keep log size manageable\n            if (this.errorLog.length > ERROR_HANDLING.MAX_ERROR_LOG_ENTRIES) {\n                this.errorLog.shift();\n            }\n\n            // Log to console in development\n            if (process.env.NODE_ENV === 'development') {\n                // Development logging would go here\n                // console.warn(`[HeaderAdvertisement] Error in ${context}:`, error);\n            }\n        } catch {\n            // Silently handle logging errors\n        }\n    }\n\n    /**\n     * Set up global error handling\n     */\n    private setupGlobalErrorHandling(): void {\n        try {\n            // Handle unhandled promise rejections\n            globalThis.addEventListener('unhandledrejection', (event) => {\n                this.logError(\n                    new Error(String(event.reason)),\n                    'Unhandled Promise Rejection'\n                );\n            });\n        } catch {\n            // Silently handle setup errors\n        }\n    }\n\n    /**\n     * Get error log (for debugging)\n     */\n    public getErrorLog(): ErrorLogEntry[] {\n        return [...this.errorLog];\n    }\n\n    /**\n     * Clear error log\n     */\n    public clearErrorLog(): void {\n        this.errorLog = [];\n    }\n}\n", "import app from 'flarum/forum/app';\nimport { defaultConfig } from '../../common/config';\n\n/**\n * Configuration manager for the Header Advertisement extension\n */\nexport class ConfigManager {\n    private static instance: ConfigManager;\n\n    private constructor() {\n        // Private constructor for singleton pattern\n    }\n\n    /**\n     * Get singleton instance\n     */\n    public static getInstance(): ConfigManager {\n        if (!ConfigManager.instance) {\n            ConfigManager.instance = new ConfigManager();\n        }\n        return ConfigManager.instance;\n    }\n\n    /**\n     * Check if current page is tags page\n     */\n    public isTagsPage(): boolean {\n        try {\n            const currentRoute = app.current.get('routeName');\n            return currentRoute === 'tags';\n        } catch {\n            // Fallback: check URL\n            try {\n                return globalThis.location.pathname.includes('/tags');\n            } catch {\n                return false;\n            }\n        }\n    }\n\n    /**\n     * Get extension configuration\n     */\n    public getConfig(): typeof defaultConfig {\n        return defaultConfig;\n    }\n\n    /**\n     * Check if slideshow is properly configured\n     */\n    public isSlideshowConfigured(): boolean {\n        try {\n            const FIRST_SLIDE_INDEX = 1;\n            const SLIDE_INCREMENT = 1;\n            // Check if at least one slide is configured\n            for (let slideIndex = FIRST_SLIDE_INDEX; slideIndex <= defaultConfig.slider.maxSlides; slideIndex += SLIDE_INCREMENT) {\n                const image = app.forum.attribute(`wusong8899-header-advertisement.Image${slideIndex}`);\n                if (image) {\n                    return true;\n                }\n            }\n            return false;\n        } catch {\n            return false;\n        }\n    }\n}\n", "import { extend } from 'flarum/common/extend';\nimport app from 'flarum/forum/app';\nimport HeaderPrimary from 'flarum/forum/components/HeaderPrimary';\n\nimport { SlideshowManager } from './components/slideshow-manager';\nimport { UIManager } from './components/ui-manager';\nimport { ErrorHandler } from './utils/error-handler';\nimport { ConfigManager } from './utils/config-manager';\nimport { defaultConfig } from '../common/config';\n\n/**\n * Main extension initializer for Header Advertisement\n */\napp.initializers.add(defaultConfig.app.extensionId, () => {\n    const errorHandler = ErrorHandler.getInstance();\n    const configManager = ConfigManager.getInstance();\n\n    // Initialize error handling\n    if (!errorHandler.initialize()) {\n        return;\n    }\n\n    const slideshowManager = new SlideshowManager();\n    const uiManager = new UIManager();\n\n    extend(HeaderPrimary.prototype, 'view', function headerPrimaryViewExtension(vnode: unknown) {\n        errorHandler.handleSync(() => {\n            if (configManager.isTagsPage()) {\n                initializeExtension(vnode, slideshowManager, uiManager);\n            }\n        }, 'HeaderPrimary view extension');\n    });\n});\n\n/**\n * Initialize extension components\n */\nconst initializeExtension = (\n    vnode: unknown,\n    slideshowManager: SlideshowManager,\n    _uiManager: UIManager\n): void => {\n    try {\n        const configManager = ConfigManager.getInstance();\n\n        // Setup slideshow (only if configured)\n        if (configManager.isSlideshowConfigured()) {\n            try {\n                slideshowManager.attachAdvertiseHeader(vnode);\n            } catch {\n                // Slideshow setup failed, but continue with other features\n            }\n        }\n\n        // UI Manager is available for future use if needed\n\n        // Add header icon for non-logged users\n        if (!app.session.user) {\n            addHeaderIcon();\n        }\n\n    } catch {\n        // Silently handle initialization errors\n    }\n}\n\n\n\n/**\n * Add header icon for branding\n */\nconst addHeaderIcon = (): void => {\n    let headerIconContainer = document.getElementById(defaultConfig.ui.headerIconId);\n\n    if (headerIconContainer === null) {\n        // Get header icon URL from settings, fallback to default config\n        const headerIconUrl = app.forum.attribute('wusong8899-header-advertisement.HeaderIconUrl') || defaultConfig.ui.headerIconUrl;\n\n        headerIconContainer = document.createElement(\"div\");\n        headerIconContainer.id = defaultConfig.ui.headerIconId;\n        headerIconContainer.style.display = 'inline-block';\n        headerIconContainer.style.marginTop = '8px';\n        headerIconContainer.innerHTML = `<img src=\"${headerIconUrl}\" style=\"height: 24px;\" />`;\n\n        const backControl = document.querySelector(\"#app-navigation .App-backControl\");\n        if (backControl && backControl.firstChild) {\n            backControl.firstChild.before(headerIconContainer);\n        }\n    }\n}\n"], "names": ["isObject", "obj", "extend", "target", "src", "noExtend", "key", "ssrDocument", "getDocument", "doc", "ssrWindow", "callback", "id", "getWindow", "win", "classesToTokens", "classes", "c", "deleteProps", "object", "nextTick", "delay", "now", "getComputedStyle", "el", "window", "style", "getTranslate", "axis", "matrix", "curTransform", "transformMatrix", "curStyle", "o", "isNode", "node", "to", "i", "nextSource", "keysArray", "nextIndex", "len", "<PERSON><PERSON><PERSON>", "desc", "setCSSProperty", "varName", "varValue", "animateCSSModeScroll", "_ref", "swiper", "targetPosition", "side", "startPosition", "startTime", "time", "duration", "dir", "isOutOfBound", "current", "animate", "progress", "easeProgress", "currentPosition", "elementChildren", "element", "selector", "children", "elementIsChildOfSlot", "slot", "elementsQueue", "elementToCheck", "elementIsChildOf", "parent", "<PERSON><PERSON><PERSON><PERSON>", "showWarning", "text", "createElement", "tag", "elementPrevAll", "prevEls", "prev", "elementNextAll", "nextEls", "next", "elementStyle", "prop", "elementIndex", "child", "elementParents", "parents", "elementOuterSize", "size", "<PERSON><PERSON><PERSON><PERSON>", "makeElementsArray", "setInnerHTML", "html", "s", "support", "calcSupport", "document", "getSupport", "deviceCached", "calcDevice", "_temp", "userAgent", "platform", "ua", "device", "screenWidth", "screenHeight", "android", "ipad", "ipod", "iphone", "windows", "macos", "iPadScreens", "getDevice", "overrides", "browser", "calcB<PERSON>er", "needPerspectiveFix", "<PERSON><PERSON><PERSON><PERSON>", "major", "minor", "num", "isWebView", "isSafariB<PERSON><PERSON>", "need3dFix", "<PERSON><PERSON><PERSON><PERSON>", "Resize", "on", "emit", "observer", "animationFrame", "resize<PERSON><PERSON>ler", "createObserver", "entries", "width", "height", "newWidth", "newHeight", "_ref2", "contentBoxSize", "contentRect", "removeObserver", "orientationChangeHandler", "Observer", "extendParams", "observers", "attach", "options", "ObserverFunc", "mutations", "observerUpdate", "init", "containerParents", "destroy", "eventsEmitter", "events", "handler", "priority", "self", "method", "event", "once<PERSON><PERSON><PERSON>", "_len", "args", "_key", "index", "<PERSON><PERSON><PERSON><PERSON>", "data", "context", "_len2", "_key2", "updateSize", "updateSlides", "getDirectionPropertyValue", "label", "params", "wrapperEl", "slidesEl", "swiperSize", "rtl", "wrongRTL", "isVirtual", "previousSlidesLength", "slides", "<PERSON><PERSON><PERSON><PERSON>", "snapGrid", "slidesGrid", "slidesSizesGrid", "offsetBefore", "offsetAfter", "previousSnapGridLength", "previousSlidesGridLength", "spaceBetween", "slidePosition", "prevSlideSize", "slideEl", "gridEnabled", "slideSize", "shouldResetSlideSize", "slide", "slideStyles", "currentTransform", "currentWebKitTransform", "paddingLeft", "paddingRight", "marginLeft", "marginRight", "boxSizing", "clientWidth", "offsetWidth", "newSlidesGrid", "slidesGridItem", "groups", "groupSize", "_", "slideIndex", "allSlidesSize", "slideSizeValue", "maxSnap", "snap", "offsetSize", "allSlidesOffset", "snapIndex", "addToSnapGrid", "addToSlidesGrid", "v", "backFaceHiddenClass", "hasClassBackfaceClassAdded", "updateAutoHeight", "speed", "activeSlides", "getSlideByIndex", "updateSlidesOffset", "minusOffset", "toggleSlideClasses$1", "condition", "className", "updateSlidesProgress", "translate", "offsetCenter", "slideOffset", "slideProgress", "originalSlideProgress", "slideBefore", "slideAfter", "isFullyVisible", "isVisible", "updateProgress", "multiplier", "translatesDiff", "isBeginning", "isEnd", "progressLoop", "wasBeginning", "wasEnd", "isBeginningRounded", "isEndRounded", "firstSlideIndex", "lastSlideIndex", "firstSlideTranslate", "lastSlideTranslate", "translateMax", "translateAbs", "toggleSlideClasses", "updateSlidesClasses", "activeIndex", "getFilteredSlide", "activeSlide", "prevSlide", "nextSlide", "processLazyPreloader", "imageEl", "slideSelector", "lazyEl", "unlazy", "preload", "amount", "<PERSON><PERSON><PERSON><PERSON>iew", "activeColumn", "preloadColumns", "slideIndexLastInView", "realIndex", "getActiveIndexByTranslate", "updateActiveIndex", "newActiveIndex", "previousIndex", "previousRealIndex", "previousSnapIndex", "getVirtualRealIndex", "aIndex", "skip", "firstSlideInColumn", "activeSlideIndex", "updateClickedSlide", "path", "pathEl", "slideFound", "update", "getSwiperTranslate", "currentTranslate", "setTranslate", "byController", "x", "y", "z", "newProgress", "minTranslate", "maxTranslate", "translateTo", "runCallbacks", "translateBounds", "internal", "newTranslate", "isH", "e", "setTransition", "transitionEmit", "direction", "step", "transitionStart", "transitionEnd", "transition", "slideTo", "initial", "enabled", "normalizedTranslate", "normalizedGrid", "normalizedGridNext", "t", "slideToLoop", "newIndex", "targetSlideIndex", "cols", "centeredSlides", "needLoopFix", "slideNext", "animating", "perGroup", "increment", "slidePrev", "rtlTranslate", "normalize", "val", "normalizedSnapGrid", "isFreeMode", "prevSnap", "prevSnapIndex", "prevIndex", "lastIndex", "slideReset", "slideToClosest", "threshold", "currentSnap", "nextSnap", "slideToClickedSlide", "slideToIndex", "isGrid", "loopCreate", "slideRealIndex", "initSlides", "clearBlankSlides", "slidesPerGroup", "shouldFillGroup", "shouldFillGrid", "addBlankSlides", "amountOfSlides", "slidesToAdd", "loopFix", "byMousewheel", "allowSlidePrev", "allowSlideNext", "initialSlide", "loopedSlides", "prependSlidesIndexes", "appendSlidesIndexes", "isInitialOverflow", "isNext", "isPrev", "slidesPrepended", "slidesAppended", "activeColIndexWithShift", "colIndexToPrepend", "currentSlideTranslate", "diff", "shift", "loopParams", "loop<PERSON><PERSON><PERSON>", "newSlidesOrder", "loop", "setGrabCursor", "moving", "unsetGrabCursor", "grabCursor", "closestElement", "base", "__closestFrom", "found", "preventEdgeSwipe", "startX", "edgeSwipeDetection", "edgeSwipeThreshold", "onTouchStart", "touches", "targetEl", "swipingClassHasValue", "eventPath", "noSwipingSelector", "isTargetShadow", "startY", "preventDefault", "shouldPreventDefault", "onTouchMove", "targetTouch", "pageX", "pageY", "diffX", "diffY", "touchAngle", "touchesDiff", "prevTouchesDirection", "isLoop", "allowLoopFix", "evt", "disableParentSwiper", "resistanceRatio", "onTouchEnd", "touchEndTime", "timeDiff", "pathTree", "currentPos", "swipeToLast", "stopIndex", "rewindFirstIndex", "rewindLastIndex", "ratio", "onResize", "isVirtualLoop", "onClick", "onScroll", "onLoad", "onDocumentTouchStart", "capture", "dom<PERSON>ethod", "swiperMethod", "attachEvents", "detachEvents", "events$1", "isGridEnabled", "setBreakpoint", "initialized", "breakpoints", "breakpointsBase", "breakpoint<PERSON><PERSON><PERSON>", "breakpoint", "breakpointP<PERSON>ms", "wasMultiRow", "isMultiRow", "wasGrabCursor", "isGrabCursor", "wasEnabled", "wasModuleEnabled", "isModuleEnabled", "directionChanged", "needsReLoop", "<PERSON><PERSON><PERSON>", "isEnabled", "<PERSON><PERSON><PERSON>", "getBreakpoint", "containerEl", "currentHeight", "points", "point", "minRatio", "b", "value", "prepareClasses", "prefix", "resultClasses", "item", "classNames", "addClasses", "suffixes", "removeClasses", "checkOverflow", "wasLocked", "slidesOffsetBefore", "lastSlideRightEdge", "checkOverflow$1", "defaults", "moduleExtendParams", "allModulesParams", "moduleParamName", "moduleParams", "prototypes", "extendedDefaults", "Swiper", "swipers", "newParams", "mod", "swiperParams", "eventName", "property", "min", "cls", "updates", "view", "exact", "spv", "breakLoop", "translateValue", "translated", "newDirection", "needUpdate", "currentDirection", "getWrapperSelector", "lazyElements", "deleteInstance", "cleanStyles", "newDefaults", "modules", "module", "m", "prototypeGroup", "protoMethod", "createElementIfNotDefined", "originalParams", "checkProps", "Navigation", "getEl", "res", "toggleEl", "disabled", "subEl", "nextEl", "prevEl", "onPrevClick", "onNextClick", "initButton", "destroyButton", "disable", "_s", "targetIsButton", "isHidden", "enable", "classesToSelector", "Pagination", "pfx", "number", "bulletSize", "dynamicBulletIndex", "isPaginationDisabled", "setSideBullets", "bulletEl", "position", "bulletActiveClass", "getMoveDirection", "length", "onBulletClick", "moveDirection", "total", "bullets", "firstIndex", "midIndex", "classesToRemove", "suffix", "bullet", "bulletIndex", "first<PERSON><PERSON>played<PERSON><PERSON>et", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dynamicBulletsLength", "bulletsOffset", "offsetProp", "subElIndex", "fractionEl", "totalEl", "progressbarDirection", "scale", "scaleX", "scaleY", "progressEl", "render", "paginationHTML", "numberOfBullets", "Autoplay", "timeout", "raf", "autoplayDelayTotal", "autoplayDelayCurrent", "autoplayTimeLeft", "autoplayStartTime", "wasPaused", "isTouched", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touchStartTimeout", "slideChanged", "pausedByInteraction", "pausedByPointerEnter", "onTransitionEnd", "resume", "calcTimeLeft", "timeLeft", "getSlideDelay", "activeSlideEl", "run", "delayForce", "currentSlideDelay", "proceed", "start", "stop", "pause", "reset", "onVisibilityChange", "onPointerEnter", "onPointerLeave", "attachMouseEvents", "detachMouseEvents", "attachDocumentEvents", "detachDocumentEvents", "querySelector", "querySelectorAll", "tagName", "innerHTML", "append<PERSON><PERSON><PERSON>", "prepend<PERSON>hild", "removeElement", "setStyles", "styles", "MOBILE_DETECTION", "ERROR_HANDLING", "SLIDESHOW_CONSTANTS", "TIMING", "DOM_ELEMENTS", "CSS_CLASSES", "CSS_SELECTORS", "EXTENSION_CONFIG", "isMobileDevice", "defaultConfig", "SWIPER_CONFIG_CONSTANTS", "SlideshowManager", "forum", "app", "attrFn", "_vdom", "container", "wrapper", "slideCount", "existingContainer", "DOMUtils.querySelector", "DOMUtils.removeElement", "navElements", "DOMUtils.querySelectorAll", "DOMUtils.createElement", "DOMUtils.appendChild", "DOMUtils.setStyles", "transitionTime", "imageSrc", "imageLink", "clickHandler", "pagination", "prevButton", "nextButton", "contentContainer", "DOMUtils.prependChild", "swiperConfig", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fn", "error", "entry", "ConfigManager", "<PERSON><PERSON><PERSON><PERSON>", "config<PERSON><PERSON><PERSON>", "slideshowManager", "HeaderPrimary", "vnode", "initializeExtension", "_uiManager", "addHeaderIcon", "headerIconContainer", "headerIconUrl", "backControl"], "mappings": "gCAYA,SAASA,GAASC,EAAK,CACrB,OAAOA,IAAQ,MAAQ,OAAOA,GAAQ,UAAY,gBAAiBA,GAAOA,EAAI,cAAgB,MAChG,CACA,SAASC,GAAOC,EAAQC,EAAK,CACvBD,IAAW,SACbA,EAAS,CAAA,GAEPC,IAAQ,SACVA,EAAM,CAAA,GAER,MAAMC,EAAW,CAAC,YAAa,cAAe,WAAW,EACzD,OAAO,KAAKD,CAAG,EAAE,OAAOE,GAAOD,EAAS,QAAQC,CAAG,EAAI,CAAC,EAAE,QAAQA,GAAO,CACnE,OAAOH,EAAOG,CAAG,EAAM,IAAaH,EAAOG,CAAG,EAAIF,EAAIE,CAAG,EAAWN,GAASI,EAAIE,CAAG,CAAC,GAAKN,GAASG,EAAOG,CAAG,CAAC,GAAK,OAAO,KAAKF,EAAIE,CAAG,CAAC,EAAE,OAAS,GACpJJ,GAAOC,EAAOG,CAAG,EAAGF,EAAIE,CAAG,CAAC,CAEhC,CAAC,CACH,CACA,MAAMC,GAAc,CAClB,KAAM,CAAA,EACN,kBAAmB,CAAC,EACpB,qBAAsB,CAAC,EACvB,cAAe,CACb,MAAO,CAAC,EACR,SAAU,EACd,EACE,eAAgB,CACd,OAAO,IACT,EACA,kBAAmB,CACjB,MAAO,CAAA,CACT,EACA,gBAAiB,CACf,OAAO,IACT,EACA,aAAc,CACZ,MAAO,CACL,WAAY,CAAC,CACnB,CACE,EACA,eAAgB,CACd,MAAO,CACL,SAAU,CAAA,EACV,WAAY,CAAA,EACZ,MAAO,CAAA,EACP,cAAe,CAAC,EAChB,sBAAuB,CACrB,MAAO,CAAA,CACT,CACN,CACE,EACA,iBAAkB,CAChB,MAAO,CAAA,CACT,EACA,YAAa,CACX,OAAO,IACT,EACA,SAAU,CACR,KAAM,GACN,KAAM,GACN,SAAU,GACV,KAAM,GACN,OAAQ,GACR,SAAU,GACV,SAAU,GACV,OAAQ,EACZ,CACA,EACA,SAASC,GAAc,CACrB,MAAMC,EAAM,OAAO,SAAa,IAAc,SAAW,CAAA,EACzDP,OAAAA,GAAOO,EAAKF,EAAW,EAChBE,CACT,CACA,MAAMC,GAAY,CAChB,SAAUH,GACV,UAAW,CACT,UAAW,EACf,EACE,SAAU,CACR,KAAM,GACN,KAAM,GACN,SAAU,GACV,KAAM,GACN,OAAQ,GACR,SAAU,GACV,SAAU,GACV,OAAQ,EACZ,EACE,QAAS,CACP,cAAe,CAAC,EAChB,WAAY,CAAC,EACb,IAAK,CAAC,EACN,MAAO,CAAC,CACZ,EACE,YAAa,UAAuB,CAClC,OAAO,IACT,EACA,kBAAmB,CAAC,EACpB,qBAAsB,CAAC,EACvB,kBAAmB,CACjB,MAAO,CACL,kBAAmB,CACjB,MAAO,EACT,CACN,CACE,EACA,OAAQ,CAAC,EACT,MAAO,CAAC,EACR,OAAQ,CAAA,EACR,YAAa,CAAC,EACd,cAAe,CAAC,EAChB,YAAa,CACX,MAAO,CAAA,CACT,EACA,sBAAsBI,EAAU,CAC9B,OAAI,OAAO,WAAe,KACxBA,EAAQ,EACD,MAEF,WAAWA,EAAU,CAAC,CAC/B,EACA,qBAAqBC,EAAI,CACnB,OAAO,WAAe,KAG1B,aAAaA,CAAE,CACjB,CACF,EACA,SAASC,GAAY,CACnB,MAAMC,EAAM,OAAO,OAAW,IAAc,OAAS,CAAA,EACrDZ,OAAAA,GAAOY,EAAKJ,EAAS,EACdI,CACT,CC7IA,SAASC,GAAgBC,EAAS,CAChC,OAAIA,IAAY,SACdA,EAAU,IAELA,EAAQ,OAAO,MAAM,GAAG,EAAE,OAAOC,GAAK,CAAC,CAACA,EAAE,KAAI,CAAE,CACzD,CAEA,SAASC,GAAYjB,EAAK,CACxB,MAAMkB,EAASlB,EACf,OAAO,KAAKkB,CAAM,EAAE,QAAQb,GAAO,CACjC,GAAI,CACFa,EAAOb,CAAG,EAAI,IAChB,MAAY,CAEZ,CACA,GAAI,CACF,OAAOa,EAAOb,CAAG,CACnB,MAAY,CAEZ,CACF,CAAC,CACH,CACA,SAASc,GAAST,EAAUU,EAAO,CACjC,OAAIA,IAAU,SACZA,EAAQ,GAEH,WAAWV,EAAUU,CAAK,CACnC,CACA,SAASC,GAAM,CACb,OAAO,KAAK,IAAG,CACjB,CACA,SAASC,GAAiBC,EAAI,CAC5B,MAAMC,EAASZ,EAAS,EACxB,IAAIa,EACJ,OAAID,EAAO,mBACTC,EAAQD,EAAO,iBAAiBD,EAAI,IAAI,GAEtC,CAACE,GAASF,EAAG,eACfE,EAAQF,EAAG,cAERE,IACHA,EAAQF,EAAG,OAENE,CACT,CACA,SAASC,GAAaH,EAAII,EAAM,CAC1BA,IAAS,SACXA,EAAO,KAET,MAAMH,EAASZ,EAAS,EACxB,IAAIgB,EACAC,EACAC,EACJ,MAAMC,EAAWT,GAAiBC,CAAE,EACpC,OAAIC,EAAO,iBACTK,EAAeE,EAAS,WAAaA,EAAS,gBAC1CF,EAAa,MAAM,GAAG,EAAE,OAAS,IACnCA,EAAeA,EAAa,MAAM,IAAI,EAAE,IAAI,GAAK,EAAE,QAAQ,IAAK,GAAG,CAAC,EAAE,KAAK,IAAI,GAIjFC,EAAkB,IAAIN,EAAO,gBAAgBK,IAAiB,OAAS,GAAKA,CAAY,IAExFC,EAAkBC,EAAS,cAAgBA,EAAS,YAAcA,EAAS,aAAeA,EAAS,aAAeA,EAAS,WAAaA,EAAS,iBAAiB,WAAW,EAAE,QAAQ,aAAc,oBAAoB,EACzNH,EAASE,EAAgB,WAAW,MAAM,GAAG,GAE3CH,IAAS,MAEPH,EAAO,gBAAiBK,EAAeC,EAAgB,IAElDF,EAAO,SAAW,GAAIC,EAAe,WAAWD,EAAO,EAAE,CAAC,EAE9DC,EAAe,WAAWD,EAAO,CAAC,CAAC,GAEtCD,IAAS,MAEPH,EAAO,gBAAiBK,EAAeC,EAAgB,IAElDF,EAAO,SAAW,GAAIC,EAAe,WAAWD,EAAO,EAAE,CAAC,EAE9DC,EAAe,WAAWD,EAAO,CAAC,CAAC,GAEnCC,GAAgB,CACzB,CACA,SAAS9B,GAASiC,EAAG,CACnB,OAAO,OAAOA,GAAM,UAAYA,IAAM,MAAQA,EAAE,aAAe,OAAO,UAAU,SAAS,KAAKA,CAAC,EAAE,MAAM,EAAG,EAAE,IAAM,QACpH,CACA,SAASC,GAAOC,EAAM,CAEpB,OAAI,OAAO,OAAW,KAAe,OAAO,OAAO,YAAgB,IAC1DA,aAAgB,YAElBA,IAASA,EAAK,WAAa,GAAKA,EAAK,WAAa,GAC3D,CACA,SAASjC,GAAS,CAChB,MAAMkC,EAAK,OAAO,UAAU,QAAU,EAAI,OAAY,UAAU,CAAC,CAAC,EAC5D/B,EAAW,CAAC,YAAa,cAAe,WAAW,EACzD,QAASgC,EAAI,EAAGA,EAAI,UAAU,OAAQA,GAAK,EAAG,CAC5C,MAAMC,EAAaD,EAAI,GAAK,UAAU,QAAUA,EAAI,OAAY,UAAUA,CAAC,EAC3E,GAAgCC,GAAe,MAAQ,CAACJ,GAAOI,CAAU,EAAG,CAC1E,MAAMC,EAAY,OAAO,KAAK,OAAOD,CAAU,CAAC,EAAE,OAAOhC,GAAOD,EAAS,QAAQC,CAAG,EAAI,CAAC,EACzF,QAASkC,EAAY,EAAGC,EAAMF,EAAU,OAAQC,EAAYC,EAAKD,GAAa,EAAG,CAC/E,MAAME,EAAUH,EAAUC,CAAS,EAC7BG,EAAO,OAAO,yBAAyBL,EAAYI,CAAO,EAC5DC,IAAS,QAAaA,EAAK,aACzB3C,GAASoC,EAAGM,CAAO,CAAC,GAAK1C,GAASsC,EAAWI,CAAO,CAAC,EACnDJ,EAAWI,CAAO,EAAE,WACtBN,EAAGM,CAAO,EAAIJ,EAAWI,CAAO,EAEhCxC,EAAOkC,EAAGM,CAAO,EAAGJ,EAAWI,CAAO,CAAC,EAEhC,CAAC1C,GAASoC,EAAGM,CAAO,CAAC,GAAK1C,GAASsC,EAAWI,CAAO,CAAC,GAC/DN,EAAGM,CAAO,EAAI,CAAA,EACVJ,EAAWI,CAAO,EAAE,WACtBN,EAAGM,CAAO,EAAIJ,EAAWI,CAAO,EAEhCxC,EAAOkC,EAAGM,CAAO,EAAGJ,EAAWI,CAAO,CAAC,GAGzCN,EAAGM,CAAO,EAAIJ,EAAWI,CAAO,EAGtC,CACF,CACF,CACA,OAAON,CACT,CACA,SAASQ,GAAepB,EAAIqB,EAASC,EAAU,CAC7CtB,EAAG,MAAM,YAAYqB,EAASC,CAAQ,CACxC,CACA,SAASC,GAAqBC,EAAM,CAClC,GAAI,CACF,OAAAC,EACA,eAAAC,EACA,KAAAC,CACJ,EAAMH,EACJ,MAAMvB,EAASZ,EAAS,EAClBuC,EAAgB,CAACH,EAAO,UAC9B,IAAII,EAAY,KACZC,EACJ,MAAMC,EAAWN,EAAO,OAAO,MAC/BA,EAAO,UAAU,MAAM,eAAiB,OACxCxB,EAAO,qBAAqBwB,EAAO,cAAc,EACjD,MAAMO,EAAMN,EAAiBE,EAAgB,OAAS,OAChDK,EAAe,CAACC,EAASvD,IACtBqD,IAAQ,QAAUE,GAAWvD,GAAUqD,IAAQ,QAAUE,GAAWvD,EAEvEwD,EAAU,IAAM,CACpBL,EAAO,IAAI,KAAI,EAAG,QAAO,EACrBD,IAAc,OAChBA,EAAYC,GAEd,MAAMM,EAAW,KAAK,IAAI,KAAK,KAAKN,EAAOD,GAAaE,EAAU,CAAC,EAAG,CAAC,EACjEM,EAAe,GAAM,KAAK,IAAID,EAAW,KAAK,EAAE,EAAI,EAC1D,IAAIE,EAAkBV,EAAgBS,GAAgBX,EAAiBE,GAOvE,GANIK,EAAaK,EAAiBZ,CAAc,IAC9CY,EAAkBZ,GAEpBD,EAAO,UAAU,SAAS,CACxB,CAACE,CAAI,EAAGW,CACd,CAAK,EACGL,EAAaK,EAAiBZ,CAAc,EAAG,CACjDD,EAAO,UAAU,MAAM,SAAW,SAClCA,EAAO,UAAU,MAAM,eAAiB,GACxC,WAAW,IAAM,CACfA,EAAO,UAAU,MAAM,SAAW,GAClCA,EAAO,UAAU,SAAS,CACxB,CAACE,CAAI,EAAGW,CAClB,CAAS,CACH,CAAC,EACDrC,EAAO,qBAAqBwB,EAAO,cAAc,EACjD,MACF,CACAA,EAAO,eAAiBxB,EAAO,sBAAsBkC,CAAO,CAC9D,EACAA,EAAO,CACT,CAIA,SAASI,EAAgBC,EAASC,EAAU,CACtCA,IAAa,SACfA,EAAW,IAEb,MAAMxC,EAASZ,EAAS,EAClBqD,EAAW,CAAC,GAAGF,EAAQ,QAAQ,EAIrC,OAHIvC,EAAO,iBAAmBuC,aAAmB,iBAC/CE,EAAS,KAAK,GAAGF,EAAQ,iBAAgB,CAAE,EAExCC,EAGEC,EAAS,OAAO1C,GAAMA,EAAG,QAAQyC,CAAQ,CAAC,EAFxCC,CAGX,CACA,SAASC,GAAqB3C,EAAI4C,EAAM,CAEtC,MAAMC,EAAgB,CAACD,CAAI,EAC3B,KAAOC,EAAc,OAAS,GAAG,CAC/B,MAAMC,EAAiBD,EAAc,MAAK,EAC1C,GAAI7C,IAAO8C,EACT,MAAO,GAETD,EAAc,KAAK,GAAGC,EAAe,SAAU,GAAIA,EAAe,WAAaA,EAAe,WAAW,SAAW,CAAA,EAAK,GAAIA,EAAe,iBAAmBA,EAAe,iBAAgB,EAAK,CAAA,CAAG,CACxM,CACF,CACA,SAASC,GAAiB/C,EAAIgD,EAAQ,CACpC,MAAM/C,EAASZ,EAAS,EACxB,IAAI4D,EAAUD,EAAO,SAAShD,CAAE,EAChC,MAAI,CAACiD,GAAWhD,EAAO,iBAAmB+C,aAAkB,kBAE1DC,EADiB,CAAC,GAAGD,EAAO,iBAAgB,CAAE,EAC3B,SAAShD,CAAE,EACzBiD,IACHA,EAAUN,GAAqB3C,EAAIgD,CAAM,IAGtCC,CACT,CACA,SAASC,GAAYC,EAAM,CACzB,GAAI,CACF,QAAQ,KAAKA,CAAI,EACjB,MACF,MAAc,CAEd,CACF,CACA,SAASC,GAAcC,EAAK7D,EAAS,CAC/BA,IAAY,SACdA,EAAU,CAAA,GAEZ,MAAMQ,EAAK,SAAS,cAAcqD,CAAG,EACrC,OAAArD,EAAG,UAAU,IAAI,GAAI,MAAM,QAAQR,CAAO,EAAIA,EAAUD,GAAgBC,CAAO,CAAE,EAC1EQ,CACT,CAeA,SAASsD,GAAetD,EAAIyC,EAAU,CACpC,MAAMc,EAAU,CAAA,EAChB,KAAOvD,EAAG,wBAAwB,CAChC,MAAMwD,EAAOxD,EAAG,uBACZyC,EACEe,EAAK,QAAQf,CAAQ,GAAGc,EAAQ,KAAKC,CAAI,EACxCD,EAAQ,KAAKC,CAAI,EACxBxD,EAAKwD,CACP,CACA,OAAOD,CACT,CACA,SAASE,GAAezD,EAAIyC,EAAU,CACpC,MAAMiB,EAAU,CAAA,EAChB,KAAO1D,EAAG,oBAAoB,CAC5B,MAAM2D,EAAO3D,EAAG,mBACZyC,EACEkB,EAAK,QAAQlB,CAAQ,GAAGiB,EAAQ,KAAKC,CAAI,EACxCD,EAAQ,KAAKC,CAAI,EACxB3D,EAAK2D,CACP,CACA,OAAOD,CACT,CACA,SAASE,EAAa5D,EAAI6D,EAAM,CAE9B,OADexE,EAAS,EACV,iBAAiBW,EAAI,IAAI,EAAE,iBAAiB6D,CAAI,CAChE,CACA,SAASC,GAAa9D,EAAI,CACxB,IAAI+D,EAAQ/D,EACRa,EACJ,GAAIkD,EAAO,CAGT,IAFAlD,EAAI,GAEIkD,EAAQA,EAAM,mBAAqB,MACrCA,EAAM,WAAa,IAAGlD,GAAK,GAEjC,OAAOA,CACT,CAEF,CACA,SAASmD,GAAehE,EAAIyC,EAAU,CACpC,MAAMwB,EAAU,CAAA,EAChB,IAAIjB,EAAShD,EAAG,cAChB,KAAOgD,GACDP,EACEO,EAAO,QAAQP,CAAQ,GAAGwB,EAAQ,KAAKjB,CAAM,EAEjDiB,EAAQ,KAAKjB,CAAM,EAErBA,EAASA,EAAO,cAElB,OAAOiB,CACT,CAWA,SAASC,GAAiBlE,EAAImE,EAAMC,EAAgB,CAClD,MAAMnE,EAASZ,EAAS,EAEtB,OAAOW,EAAGmE,IAAS,QAAU,cAAgB,cAAc,EAAI,WAAWlE,EAAO,iBAAiBD,EAAI,IAAI,EAAE,iBAAiBmE,IAAS,QAAU,eAAiB,YAAY,CAAC,EAAI,WAAWlE,EAAO,iBAAiBD,EAAI,IAAI,EAAE,iBAAiBmE,IAAS,QAAU,cAAgB,eAAe,CAAC,CAGvS,CACA,SAASE,EAAkBrE,EAAI,CAC7B,OAAQ,MAAM,QAAQA,CAAE,EAAIA,EAAK,CAACA,CAAE,GAAG,OAAO,GAAK,CAAC,CAAC,CAAC,CACxD,CASA,SAASsE,GAAatE,EAAIuE,EAAM,CAC1BA,IAAS,SACXA,EAAO,IAEL,OAAO,aAAiB,IAC1BvE,EAAG,UAAY,aAAa,aAAa,OAAQ,CAC/C,WAAYwE,GAAKA,CACvB,CAAK,EAAE,WAAWD,CAAI,EAElBvE,EAAG,UAAYuE,CAEnB,CCjVA,IAAIE,GACJ,SAASC,IAAc,CACrB,MAAMzE,EAASZ,EAAS,EAClBsF,EAAW3F,EAAW,EAC5B,MAAO,CACL,aAAc2F,EAAS,iBAAmBA,EAAS,gBAAgB,OAAS,mBAAoBA,EAAS,gBAAgB,MACzH,MAAO,CAAC,EAAE,iBAAkB1E,GAAUA,EAAO,eAAiB0E,aAAoB1E,EAAO,cAC7F,CACA,CACA,SAAS2E,IAAa,CACpB,OAAKH,KACHA,GAAUC,GAAW,GAEhBD,EACT,CAEA,IAAII,GACJ,SAASC,GAAWC,EAAO,CACzB,GAAI,CACF,UAAAC,CACJ,EAAMD,IAAU,OAAS,CAAA,EAAKA,EAC5B,MAAMN,EAAUG,GAAU,EACpB3E,EAASZ,EAAS,EAClB4F,EAAWhF,EAAO,UAAU,SAC5BiF,EAAKF,GAAa/E,EAAO,UAAU,UACnCkF,EAAS,CACb,IAAK,GACL,QAAS,EACb,EACQC,EAAcnF,EAAO,OAAO,MAC5BoF,EAAepF,EAAO,OAAO,OAC7BqF,EAAUJ,EAAG,MAAM,6BAA6B,EACtD,IAAIK,EAAOL,EAAG,MAAM,sBAAsB,EAC1C,MAAMM,EAAON,EAAG,MAAM,yBAAyB,EACzCO,EAAS,CAACF,GAAQL,EAAG,MAAM,4BAA4B,EACvDQ,EAAUT,IAAa,QAC7B,IAAIU,EAAQV,IAAa,WAGzB,MAAMW,EAAc,CAAC,YAAa,YAAa,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAU,EACrK,MAAI,CAACL,GAAQI,GAASlB,EAAQ,OAASmB,EAAY,QAAQ,GAAGR,CAAW,IAAIC,CAAY,EAAE,GAAK,IAC9FE,EAAOL,EAAG,MAAM,qBAAqB,EAChCK,IAAMA,EAAO,CAAC,EAAG,EAAG,QAAQ,GACjCI,EAAQ,IAINL,GAAW,CAACI,IACdP,EAAO,GAAK,UACZA,EAAO,QAAU,KAEfI,GAAQE,GAAUD,KACpBL,EAAO,GAAK,MACZA,EAAO,IAAM,IAIRA,CACT,CACA,SAASU,GAAUC,EAAW,CAC5B,OAAIA,IAAc,SAChBA,EAAY,CAAA,GAETjB,KACHA,GAAeC,GAAWgB,CAAS,GAE9BjB,EACT,CAEA,IAAIkB,GACJ,SAASC,IAAc,CACrB,MAAM/F,EAASZ,EAAS,EAClB8F,EAASU,GAAS,EACxB,IAAII,EAAqB,GACzB,SAASC,GAAW,CAClB,MAAMhB,EAAKjF,EAAO,UAAU,UAAU,YAAW,EACjD,OAAOiF,EAAG,QAAQ,QAAQ,GAAK,GAAKA,EAAG,QAAQ,QAAQ,EAAI,GAAKA,EAAG,QAAQ,SAAS,EAAI,CAC1F,CACA,GAAIgB,EAAQ,EAAI,CACd,MAAMhB,EAAK,OAAOjF,EAAO,UAAU,SAAS,EAC5C,GAAIiF,EAAG,SAAS,UAAU,EAAG,CAC3B,KAAM,CAACiB,EAAOC,CAAK,EAAIlB,EAAG,MAAM,UAAU,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,IAAImB,GAAO,OAAOA,CAAG,CAAC,EAC9FJ,EAAqBE,EAAQ,IAAMA,IAAU,IAAMC,EAAQ,CAC7D,CACF,CACA,MAAME,EAAY,+CAA+C,KAAKrG,EAAO,UAAU,SAAS,EAC1FsG,EAAkBL,EAAQ,EAC1BM,EAAYD,GAAmBD,GAAanB,EAAO,IACzD,MAAO,CACL,SAAUc,GAAsBM,EAChC,mBAAAN,EACA,UAAAO,EACA,UAAAF,CACJ,CACA,CACA,SAASG,IAAa,CACpB,OAAKV,KACHA,GAAUC,GAAW,GAEhBD,EACT,CAEA,SAASW,GAAOlF,EAAM,CACpB,GAAI,CACF,OAAAC,EACA,GAAAkF,EACA,KAAAC,CACJ,EAAMpF,EACJ,MAAMvB,EAASZ,EAAS,EACxB,IAAIwH,EAAW,KACXC,EAAiB,KACrB,MAAMC,EAAgB,IAAM,CACtB,CAACtF,GAAUA,EAAO,WAAa,CAACA,EAAO,cAC3CmF,EAAK,cAAc,EACnBA,EAAK,QAAQ,EACf,EACMI,EAAiB,IAAM,CACvB,CAACvF,GAAUA,EAAO,WAAa,CAACA,EAAO,cAC3CoF,EAAW,IAAI,eAAeI,GAAW,CACvCH,EAAiB7G,EAAO,sBAAsB,IAAM,CAClD,KAAM,CACJ,MAAAiH,EACA,OAAAC,CACV,EAAY1F,EACJ,IAAI2F,EAAWF,EACXG,EAAYF,EAChBF,EAAQ,QAAQK,GAAS,CACvB,GAAI,CACF,eAAAC,EACA,YAAAC,EACA,OAAA7I,CACZ,EAAc2I,EACA3I,GAAUA,IAAW8C,EAAO,KAChC2F,EAAWI,EAAcA,EAAY,OAASD,EAAe,CAAC,GAAKA,GAAgB,WACnFF,EAAYG,EAAcA,EAAY,QAAUD,EAAe,CAAC,GAAKA,GAAgB,UACvF,CAAC,GACGH,IAAaF,GAASG,IAAcF,IACtCJ,EAAa,CAEjB,CAAC,CACH,CAAC,EACDF,EAAS,QAAQpF,EAAO,EAAE,EAC5B,EACMgG,EAAiB,IAAM,CACvBX,GACF7G,EAAO,qBAAqB6G,CAAc,EAExCD,GAAYA,EAAS,WAAapF,EAAO,KAC3CoF,EAAS,UAAUpF,EAAO,EAAE,EAC5BoF,EAAW,KAEf,EACMa,EAA2B,IAAM,CACjC,CAACjG,GAAUA,EAAO,WAAa,CAACA,EAAO,aAC3CmF,EAAK,mBAAmB,CAC1B,EACAD,EAAG,OAAQ,IAAM,CACf,GAAIlF,EAAO,OAAO,gBAAkB,OAAOxB,EAAO,eAAmB,IAAa,CAChF+G,EAAc,EACd,MACF,CACA/G,EAAO,iBAAiB,SAAU8G,CAAa,EAC/C9G,EAAO,iBAAiB,oBAAqByH,CAAwB,CACvE,CAAC,EACDf,EAAG,UAAW,IAAM,CAClBc,EAAc,EACdxH,EAAO,oBAAoB,SAAU8G,CAAa,EAClD9G,EAAO,oBAAoB,oBAAqByH,CAAwB,CAC1E,CAAC,CACH,CAEA,SAASC,GAASnG,EAAM,CACtB,GAAI,CACF,OAAAC,EACA,aAAAmG,EACA,GAAAjB,EACA,KAAAC,CACJ,EAAMpF,EACJ,MAAMqG,EAAY,CAAA,EACZ5H,EAASZ,EAAS,EAClByI,EAAS,SAAUnJ,EAAQoJ,EAAS,CACpCA,IAAY,SACdA,EAAU,CAAA,GAEZ,MAAMC,EAAe/H,EAAO,kBAAoBA,EAAO,uBACjD4G,EAAW,IAAImB,EAAaC,GAAa,CAI7C,GAAIxG,EAAO,oBAAqB,OAChC,GAAIwG,EAAU,SAAW,EAAG,CAC1BrB,EAAK,iBAAkBqB,EAAU,CAAC,CAAC,EACnC,MACF,CACA,MAAMC,EAAiB,UAA0B,CAC/CtB,EAAK,iBAAkBqB,EAAU,CAAC,CAAC,CACrC,EACIhI,EAAO,sBACTA,EAAO,sBAAsBiI,CAAc,EAE3CjI,EAAO,WAAWiI,EAAgB,CAAC,CAEvC,CAAC,EACDrB,EAAS,QAAQlI,EAAQ,CACvB,WAAY,OAAOoJ,EAAQ,WAAe,IAAc,GAAOA,EAAQ,WACvE,UAAWtG,EAAO,YAAc,OAAOsG,EAAQ,UAAc,IAAc,GAAOA,GAAS,UAC3F,cAAe,OAAOA,EAAQ,cAAkB,IAAc,GAAOA,EAAQ,aACnF,CAAK,EACDF,EAAU,KAAKhB,CAAQ,CACzB,EACMsB,EAAO,IAAM,CACjB,GAAK1G,EAAO,OAAO,SACnB,IAAIA,EAAO,OAAO,eAAgB,CAChC,MAAM2G,EAAmBpE,GAAevC,EAAO,MAAM,EACrD,QAASZ,EAAI,EAAGA,EAAIuH,EAAiB,OAAQvH,GAAK,EAChDiH,EAAOM,EAAiBvH,CAAC,CAAC,CAE9B,CAEAiH,EAAOrG,EAAO,OAAQ,CACpB,UAAWA,EAAO,OAAO,oBAC/B,CAAK,EAGDqG,EAAOrG,EAAO,UAAW,CACvB,WAAY,EAClB,CAAK,EACH,EACM4G,EAAU,IAAM,CACpBR,EAAU,QAAQhB,GAAY,CAC5BA,EAAS,WAAU,CACrB,CAAC,EACDgB,EAAU,OAAO,EAAGA,EAAU,MAAM,CACtC,EACAD,EAAa,CACX,SAAU,GACV,eAAgB,GAChB,qBAAsB,EAC1B,CAAG,EACDjB,EAAG,OAAQwB,CAAI,EACfxB,EAAG,UAAW0B,CAAO,CACvB,CAIA,IAAIC,GAAgB,CAClB,GAAGC,EAAQC,EAASC,EAAU,CAC5B,MAAMC,EAAO,KAEb,GADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,OAAOF,GAAY,WAAY,OAAOE,EAC1C,MAAMC,EAASF,EAAW,UAAY,OACtC,OAAAF,EAAO,MAAM,GAAG,EAAE,QAAQK,GAAS,CAC5BF,EAAK,gBAAgBE,CAAK,IAAGF,EAAK,gBAAgBE,CAAK,EAAI,CAAA,GAChEF,EAAK,gBAAgBE,CAAK,EAAED,CAAM,EAAEH,CAAO,CAC7C,CAAC,EACME,CACT,EACA,KAAKH,EAAQC,EAASC,EAAU,CAC9B,MAAMC,EAAO,KAEb,GADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,OAAOF,GAAY,WAAY,OAAOE,EAC1C,SAASG,GAAc,CACrBH,EAAK,IAAIH,EAAQM,CAAW,EACxBA,EAAY,gBACd,OAAOA,EAAY,eAErB,QAASC,EAAO,UAAU,OAAQC,EAAO,IAAI,MAAMD,CAAI,EAAGE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,CAAI,EAAI,UAAUA,CAAI,EAE7BR,EAAQ,MAAME,EAAMK,CAAI,CAC1B,CACA,OAAAF,EAAY,eAAiBL,EACtBE,EAAK,GAAGH,EAAQM,EAAaJ,CAAQ,CAC9C,EACA,MAAMD,EAASC,EAAU,CACvB,MAAMC,EAAO,KAEb,GADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,OAAOF,GAAY,WAAY,OAAOE,EAC1C,MAAMC,EAASF,EAAW,UAAY,OACtC,OAAIC,EAAK,mBAAmB,QAAQF,CAAO,EAAI,GAC7CE,EAAK,mBAAmBC,CAAM,EAAEH,CAAO,EAElCE,CACT,EACA,OAAOF,EAAS,CACd,MAAME,EAAO,KAEb,GADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,CAACA,EAAK,mBAAoB,OAAOA,EACrC,MAAMO,EAAQP,EAAK,mBAAmB,QAAQF,CAAO,EACrD,OAAIS,GAAS,GACXP,EAAK,mBAAmB,OAAOO,EAAO,CAAC,EAElCP,CACT,EACA,IAAIH,EAAQC,EAAS,CACnB,MAAME,EAAO,KAEb,MADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,CAACA,EAAK,iBACVH,EAAO,MAAM,GAAG,EAAE,QAAQK,GAAS,CAC7B,OAAOJ,EAAY,IACrBE,EAAK,gBAAgBE,CAAK,EAAI,CAAA,EACrBF,EAAK,gBAAgBE,CAAK,GACnCF,EAAK,gBAAgBE,CAAK,EAAE,QAAQ,CAACM,EAAcD,IAAU,EACvDC,IAAiBV,GAAWU,EAAa,gBAAkBA,EAAa,iBAAmBV,IAC7FE,EAAK,gBAAgBE,CAAK,EAAE,OAAOK,EAAO,CAAC,CAE/C,CAAC,CAEL,CAAC,EACMP,CACT,EACA,MAAO,CACL,MAAMA,EAAO,KAEb,GADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,CAACA,EAAK,gBAAiB,OAAOA,EAClC,IAAIH,EACAY,EACAC,EACJ,QAASC,EAAQ,UAAU,OAAQN,EAAO,IAAI,MAAMM,CAAK,EAAGC,EAAQ,EAAGA,EAAQD,EAAOC,IACpFP,EAAKO,CAAK,EAAI,UAAUA,CAAK,EAE/B,OAAI,OAAOP,EAAK,CAAC,GAAM,UAAY,MAAM,QAAQA,EAAK,CAAC,CAAC,GACtDR,EAASQ,EAAK,CAAC,EACfI,EAAOJ,EAAK,MAAM,EAAGA,EAAK,MAAM,EAChCK,EAAUV,IAEVH,EAASQ,EAAK,CAAC,EAAE,OACjBI,EAAOJ,EAAK,CAAC,EAAE,KACfK,EAAUL,EAAK,CAAC,EAAE,SAAWL,GAE/BS,EAAK,QAAQC,CAAO,GACA,MAAM,QAAQb,CAAM,EAAIA,EAASA,EAAO,MAAM,GAAG,GACzD,QAAQK,GAAS,CACvBF,EAAK,oBAAsBA,EAAK,mBAAmB,QACrDA,EAAK,mBAAmB,QAAQQ,GAAgB,CAC9CA,EAAa,MAAME,EAAS,CAACR,EAAO,GAAGO,CAAI,CAAC,CAC9C,CAAC,EAECT,EAAK,iBAAmBA,EAAK,gBAAgBE,CAAK,GACpDF,EAAK,gBAAgBE,CAAK,EAAE,QAAQM,GAAgB,CAClDA,EAAa,MAAME,EAASD,CAAI,CAClC,CAAC,CAEL,CAAC,EACMT,CACT,CACF,EAEA,SAASa,IAAa,CACpB,MAAM9H,EAAS,KACf,IAAIyF,EACAC,EACJ,MAAMnH,EAAKyB,EAAO,GACd,OAAOA,EAAO,OAAO,MAAU,KAAeA,EAAO,OAAO,QAAU,KACxEyF,EAAQzF,EAAO,OAAO,MAEtByF,EAAQlH,EAAG,YAET,OAAOyB,EAAO,OAAO,OAAW,KAAeA,EAAO,OAAO,SAAW,KAC1E0F,EAAS1F,EAAO,OAAO,OAEvB0F,EAASnH,EAAG,aAEV,EAAAkH,IAAU,GAAKzF,EAAO,aAAY,GAAM0F,IAAW,GAAK1F,EAAO,gBAKnEyF,EAAQA,EAAQ,SAAStD,EAAa5D,EAAI,cAAc,GAAK,EAAG,EAAE,EAAI,SAAS4D,EAAa5D,EAAI,eAAe,GAAK,EAAG,EAAE,EACzHmH,EAASA,EAAS,SAASvD,EAAa5D,EAAI,aAAa,GAAK,EAAG,EAAE,EAAI,SAAS4D,EAAa5D,EAAI,gBAAgB,GAAK,EAAG,EAAE,EACvH,OAAO,MAAMkH,CAAK,IAAGA,EAAQ,GAC7B,OAAO,MAAMC,CAAM,IAAGA,EAAS,GACnC,OAAO,OAAO1F,EAAQ,CACpB,MAAAyF,EACA,OAAAC,EACA,KAAM1F,EAAO,aAAY,EAAKyF,EAAQC,CAC1C,CAAG,EACH,CAEA,SAASqC,IAAe,CACtB,MAAM/H,EAAS,KACf,SAASgI,EAA0B9I,EAAM+I,EAAO,CAC9C,OAAO,WAAW/I,EAAK,iBAAiBc,EAAO,kBAAkBiI,CAAK,CAAC,GAAK,CAAC,CAC/E,CACA,MAAMC,EAASlI,EAAO,OAChB,CACJ,UAAAmI,EACA,SAAAC,EACA,KAAMC,EACN,aAAcC,EACd,SAAAC,CACJ,EAAMvI,EACEwI,EAAYxI,EAAO,SAAWkI,EAAO,QAAQ,QAC7CO,EAAuBD,EAAYxI,EAAO,QAAQ,OAAO,OAASA,EAAO,OAAO,OAChF0I,EAAS5H,EAAgBsH,EAAU,IAAIpI,EAAO,OAAO,UAAU,gBAAgB,EAC/E2I,EAAeH,EAAYxI,EAAO,QAAQ,OAAO,OAAS0I,EAAO,OACvE,IAAIE,EAAW,CAAA,EACf,MAAMC,EAAa,CAAA,EACbC,EAAkB,CAAA,EACxB,IAAIC,EAAeb,EAAO,mBACtB,OAAOa,GAAiB,aAC1BA,EAAeb,EAAO,mBAAmB,KAAKlI,CAAM,GAEtD,IAAIgJ,EAAcd,EAAO,kBACrB,OAAOc,GAAgB,aACzBA,EAAcd,EAAO,kBAAkB,KAAKlI,CAAM,GAEpD,MAAMiJ,EAAyBjJ,EAAO,SAAS,OACzCkJ,EAA2BlJ,EAAO,WAAW,OACnD,IAAImJ,EAAejB,EAAO,aACtBkB,EAAgB,CAACL,EACjBM,EAAgB,EAChB7B,EAAQ,EACZ,GAAI,OAAOa,EAAe,IACxB,OAEE,OAAOc,GAAiB,UAAYA,EAAa,QAAQ,GAAG,GAAK,EACnEA,EAAe,WAAWA,EAAa,QAAQ,IAAK,EAAE,CAAC,EAAI,IAAMd,EACxD,OAAOc,GAAiB,WACjCA,EAAe,WAAWA,CAAY,GAExCnJ,EAAO,YAAc,CAACmJ,EAGtBT,EAAO,QAAQY,GAAW,CACpBhB,EACFgB,EAAQ,MAAM,WAAa,GAE3BA,EAAQ,MAAM,YAAc,GAE9BA,EAAQ,MAAM,aAAe,GAC7BA,EAAQ,MAAM,UAAY,EAC5B,CAAC,EAGGpB,EAAO,gBAAkBA,EAAO,UAClCvI,GAAewI,EAAW,kCAAmC,EAAE,EAC/DxI,GAAewI,EAAW,iCAAkC,EAAE,GAEhE,MAAMoB,EAAcrB,EAAO,MAAQA,EAAO,KAAK,KAAO,GAAKlI,EAAO,KAC9DuJ,EACFvJ,EAAO,KAAK,WAAW0I,CAAM,EACpB1I,EAAO,MAChBA,EAAO,KAAK,YAAW,EAIzB,IAAIwJ,EACJ,MAAMC,EAAuBvB,EAAO,gBAAkB,QAAUA,EAAO,aAAe,OAAO,KAAKA,EAAO,WAAW,EAAE,OAAO7K,GACpH,OAAO6K,EAAO,YAAY7K,CAAG,EAAE,cAAkB,GACzD,EAAE,OAAS,EACZ,QAAS+B,EAAI,EAAGA,EAAIuJ,EAAcvJ,GAAK,EAAG,CACxCoK,EAAY,EACZ,IAAIE,EAKJ,GAJIhB,EAAOtJ,CAAC,IAAGsK,EAAQhB,EAAOtJ,CAAC,GAC3BmK,GACFvJ,EAAO,KAAK,YAAYZ,EAAGsK,EAAOhB,CAAM,EAEtC,EAAAA,EAAOtJ,CAAC,GAAK+C,EAAauH,EAAO,SAAS,IAAM,QAEpD,IAAIxB,EAAO,gBAAkB,OAAQ,CAC/BuB,IACFf,EAAOtJ,CAAC,EAAE,MAAMY,EAAO,kBAAkB,OAAO,CAAC,EAAI,IAEvD,MAAM2J,EAAc,iBAAiBD,CAAK,EACpCE,EAAmBF,EAAM,MAAM,UAC/BG,EAAyBH,EAAM,MAAM,gBAO3C,GANIE,IACFF,EAAM,MAAM,UAAY,QAEtBG,IACFH,EAAM,MAAM,gBAAkB,QAE5BxB,EAAO,aACTsB,EAAYxJ,EAAO,aAAY,EAAKyC,GAAiBiH,EAAO,OAAa,EAAIjH,GAAiBiH,EAAO,QAAc,MAC9G,CAEL,MAAMjE,EAAQuC,EAA0B2B,EAAa,OAAO,EACtDG,EAAc9B,EAA0B2B,EAAa,cAAc,EACnEI,EAAe/B,EAA0B2B,EAAa,eAAe,EACrEK,EAAahC,EAA0B2B,EAAa,aAAa,EACjEM,EAAcjC,EAA0B2B,EAAa,cAAc,EACnEO,EAAYP,EAAY,iBAAiB,YAAY,EAC3D,GAAIO,GAAaA,IAAc,aAC7BV,EAAY/D,EAAQuE,EAAaC,MAC5B,CACL,KAAM,CACJ,YAAAE,EACA,YAAAC,EACZ,EAAcV,EACJF,EAAY/D,EAAQqE,EAAcC,EAAeC,EAAaC,GAAeG,GAAcD,EAC7F,CACF,CACIP,IACFF,EAAM,MAAM,UAAYE,GAEtBC,IACFH,EAAM,MAAM,gBAAkBG,GAE5B3B,EAAO,eAAcsB,EAAY,KAAK,MAAMA,CAAS,EAC3D,MACEA,GAAanB,GAAcH,EAAO,cAAgB,GAAKiB,GAAgBjB,EAAO,cAC1EA,EAAO,eAAcsB,EAAY,KAAK,MAAMA,CAAS,GACrDd,EAAOtJ,CAAC,IACVsJ,EAAOtJ,CAAC,EAAE,MAAMY,EAAO,kBAAkB,OAAO,CAAC,EAAI,GAAGwJ,CAAS,MAGjEd,EAAOtJ,CAAC,IACVsJ,EAAOtJ,CAAC,EAAE,gBAAkBoK,GAE9BV,EAAgB,KAAKU,CAAS,EAC1BtB,EAAO,gBACTkB,EAAgBA,EAAgBI,EAAY,EAAIH,EAAgB,EAAIF,EAChEE,IAAkB,GAAKjK,IAAM,IAAGgK,EAAgBA,EAAgBf,EAAa,EAAIc,GACjF/J,IAAM,IAAGgK,EAAgBA,EAAgBf,EAAa,EAAIc,GAC1D,KAAK,IAAIC,CAAa,EAAI,EAAI,MAAMA,EAAgB,GACpDlB,EAAO,eAAckB,EAAgB,KAAK,MAAMA,CAAa,GAC7D5B,EAAQU,EAAO,iBAAmB,GAAGU,EAAS,KAAKQ,CAAa,EACpEP,EAAW,KAAKO,CAAa,IAEzBlB,EAAO,eAAckB,EAAgB,KAAK,MAAMA,CAAa,IAC5D5B,EAAQ,KAAK,IAAIxH,EAAO,OAAO,mBAAoBwH,CAAK,GAAKxH,EAAO,OAAO,iBAAmB,GAAG4I,EAAS,KAAKQ,CAAa,EACjIP,EAAW,KAAKO,CAAa,EAC7BA,EAAgBA,EAAgBI,EAAYL,GAE9CnJ,EAAO,aAAewJ,EAAYL,EAClCE,EAAgBG,EAChBhC,GAAS,EACX,CAaA,GAZAxH,EAAO,YAAc,KAAK,IAAIA,EAAO,YAAaqI,CAAU,EAAIW,EAC5DV,GAAOC,IAAaL,EAAO,SAAW,SAAWA,EAAO,SAAW,eACrEC,EAAU,MAAM,MAAQ,GAAGnI,EAAO,YAAcmJ,CAAY,MAE1DjB,EAAO,iBACTC,EAAU,MAAMnI,EAAO,kBAAkB,OAAO,CAAC,EAAI,GAAGA,EAAO,YAAcmJ,CAAY,MAEvFI,GACFvJ,EAAO,KAAK,kBAAkBwJ,EAAWZ,CAAQ,EAI/C,CAACV,EAAO,eAAgB,CAC1B,MAAMmC,EAAgB,CAAA,EACtB,QAASjL,EAAI,EAAGA,EAAIwJ,EAAS,OAAQxJ,GAAK,EAAG,CAC3C,IAAIkL,EAAiB1B,EAASxJ,CAAC,EAC3B8I,EAAO,eAAcoC,EAAiB,KAAK,MAAMA,CAAc,GAC/D1B,EAASxJ,CAAC,GAAKY,EAAO,YAAcqI,GACtCgC,EAAc,KAAKC,CAAc,CAErC,CACA1B,EAAWyB,EACP,KAAK,MAAMrK,EAAO,YAAcqI,CAAU,EAAI,KAAK,MAAMO,EAASA,EAAS,OAAS,CAAC,CAAC,EAAI,GAC5FA,EAAS,KAAK5I,EAAO,YAAcqI,CAAU,CAEjD,CACA,GAAIG,GAAaN,EAAO,KAAM,CAC5B,MAAMxF,EAAOoG,EAAgB,CAAC,EAAIK,EAClC,GAAIjB,EAAO,eAAiB,EAAG,CAC7B,MAAMqC,EAAS,KAAK,MAAMvK,EAAO,QAAQ,aAAeA,EAAO,QAAQ,aAAekI,EAAO,cAAc,EACrGsC,EAAY9H,EAAOwF,EAAO,eAChC,QAAS9I,EAAI,EAAGA,EAAImL,EAAQnL,GAAK,EAC/BwJ,EAAS,KAAKA,EAASA,EAAS,OAAS,CAAC,EAAI4B,CAAS,CAE3D,CACA,QAASpL,EAAI,EAAGA,EAAIY,EAAO,QAAQ,aAAeA,EAAO,QAAQ,YAAaZ,GAAK,EAC7E8I,EAAO,iBAAmB,GAC5BU,EAAS,KAAKA,EAASA,EAAS,OAAS,CAAC,EAAIlG,CAAI,EAEpDmG,EAAW,KAAKA,EAAWA,EAAW,OAAS,CAAC,EAAInG,CAAI,EACxD1C,EAAO,aAAe0C,CAE1B,CAEA,GADIkG,EAAS,SAAW,IAAGA,EAAW,CAAC,CAAC,GACpCO,IAAiB,EAAG,CACtB,MAAM9L,EAAM2C,EAAO,aAAY,GAAMsI,EAAM,aAAetI,EAAO,kBAAkB,aAAa,EAChG0I,EAAO,OAAO,CAAC+B,EAAGC,IACZ,CAACxC,EAAO,SAAWA,EAAO,KAAa,GACvCwC,IAAehC,EAAO,OAAS,CAIpC,EAAE,QAAQY,GAAW,CACpBA,EAAQ,MAAMjM,CAAG,EAAI,GAAG8L,CAAY,IACtC,CAAC,CACH,CACA,GAAIjB,EAAO,gBAAkBA,EAAO,qBAAsB,CACxD,IAAIyC,EAAgB,EACpB7B,EAAgB,QAAQ8B,GAAkB,CACxCD,GAAiBC,GAAkBzB,GAAgB,EACrD,CAAC,EACDwB,GAAiBxB,EACjB,MAAM0B,EAAUF,EAAgBtC,EAAasC,EAAgBtC,EAAa,EAC1EO,EAAWA,EAAS,IAAIkC,GAClBA,GAAQ,EAAU,CAAC/B,EACnB+B,EAAOD,EAAgBA,EAAU7B,EAC9B8B,CACR,CACH,CACA,GAAI5C,EAAO,yBAA0B,CACnC,IAAIyC,EAAgB,EACpB7B,EAAgB,QAAQ8B,GAAkB,CACxCD,GAAiBC,GAAkBzB,GAAgB,EACrD,CAAC,EACDwB,GAAiBxB,EACjB,MAAM4B,GAAc7C,EAAO,oBAAsB,IAAMA,EAAO,mBAAqB,GACnF,GAAIyC,EAAgBI,EAAa1C,EAAY,CAC3C,MAAM2C,GAAmB3C,EAAasC,EAAgBI,GAAc,EACpEnC,EAAS,QAAQ,CAACkC,EAAMG,IAAc,CACpCrC,EAASqC,CAAS,EAAIH,EAAOE,CAC/B,CAAC,EACDnC,EAAW,QAAQ,CAACiC,EAAMG,IAAc,CACtCpC,EAAWoC,CAAS,EAAIH,EAAOE,CACjC,CAAC,CACH,CACF,CAOA,GANA,OAAO,OAAOhL,EAAQ,CACpB,OAAA0I,EACA,SAAAE,EACA,WAAAC,EACA,gBAAAC,CACJ,CAAG,EACGZ,EAAO,gBAAkBA,EAAO,SAAW,CAACA,EAAO,qBAAsB,CAC3EvI,GAAewI,EAAW,kCAAmC,GAAG,CAACS,EAAS,CAAC,CAAC,IAAI,EAChFjJ,GAAewI,EAAW,iCAAkC,GAAGnI,EAAO,KAAO,EAAI8I,EAAgBA,EAAgB,OAAS,CAAC,EAAI,CAAC,IAAI,EACpI,MAAMoC,EAAgB,CAAClL,EAAO,SAAS,CAAC,EAClCmL,EAAkB,CAACnL,EAAO,WAAW,CAAC,EAC5CA,EAAO,SAAWA,EAAO,SAAS,IAAIoL,GAAKA,EAAIF,CAAa,EAC5DlL,EAAO,WAAaA,EAAO,WAAW,IAAIoL,GAAKA,EAAID,CAAe,CACpE,CAeA,GAdIxC,IAAiBF,GACnBzI,EAAO,KAAK,oBAAoB,EAE9B4I,EAAS,SAAWK,IAClBjJ,EAAO,OAAO,eAAeA,EAAO,cAAa,EACrDA,EAAO,KAAK,sBAAsB,GAEhC6I,EAAW,SAAWK,GACxBlJ,EAAO,KAAK,wBAAwB,EAElCkI,EAAO,qBACTlI,EAAO,mBAAkB,EAE3BA,EAAO,KAAK,eAAe,EACvB,CAACwI,GAAa,CAACN,EAAO,UAAYA,EAAO,SAAW,SAAWA,EAAO,SAAW,QAAS,CAC5F,MAAMmD,EAAsB,GAAGnD,EAAO,sBAAsB,kBACtDoD,EAA6BtL,EAAO,GAAG,UAAU,SAASqL,CAAmB,EAC/E1C,GAAgBT,EAAO,wBACpBoD,GAA4BtL,EAAO,GAAG,UAAU,IAAIqL,CAAmB,EACnEC,GACTtL,EAAO,GAAG,UAAU,OAAOqL,CAAmB,CAElD,CACF,CAEA,SAASE,GAAiBC,EAAO,CAC/B,MAAMxL,EAAS,KACTyL,EAAe,CAAA,EACfjD,EAAYxI,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAC1D,IAAI4F,EAAY,EACZxG,EACA,OAAOoM,GAAU,SACnBxL,EAAO,cAAcwL,CAAK,EACjBA,IAAU,IACnBxL,EAAO,cAAcA,EAAO,OAAO,KAAK,EAE1C,MAAM0L,EAAkBlE,GAClBgB,EACKxI,EAAO,OAAOA,EAAO,oBAAoBwH,CAAK,CAAC,EAEjDxH,EAAO,OAAOwH,CAAK,EAG5B,GAAIxH,EAAO,OAAO,gBAAkB,QAAUA,EAAO,OAAO,cAAgB,EAC1E,GAAIA,EAAO,OAAO,gBACfA,EAAO,eAAiB,IAAI,QAAQ0J,GAAS,CAC5C+B,EAAa,KAAK/B,CAAK,CACzB,CAAC,MAED,KAAKtK,EAAI,EAAGA,EAAI,KAAK,KAAKY,EAAO,OAAO,aAAa,EAAGZ,GAAK,EAAG,CAC9D,MAAMoI,EAAQxH,EAAO,YAAcZ,EACnC,GAAIoI,EAAQxH,EAAO,OAAO,QAAU,CAACwI,EAAW,MAChDiD,EAAa,KAAKC,EAAgBlE,CAAK,CAAC,CAC1C,MAGFiE,EAAa,KAAKC,EAAgB1L,EAAO,WAAW,CAAC,EAIvD,IAAKZ,EAAI,EAAGA,EAAIqM,EAAa,OAAQrM,GAAK,EACxC,GAAI,OAAOqM,EAAarM,CAAC,EAAM,IAAa,CAC1C,MAAMsG,EAAS+F,EAAarM,CAAC,EAAE,aAC/BwG,EAAYF,EAASE,EAAYF,EAASE,CAC5C,EAIEA,GAAaA,IAAc,KAAG5F,EAAO,UAAU,MAAM,OAAS,GAAG4F,CAAS,KAChF,CAEA,SAAS+F,IAAqB,CAC5B,MAAM3L,EAAS,KACT0I,EAAS1I,EAAO,OAEhB4L,EAAc5L,EAAO,UAAYA,EAAO,aAAY,EAAKA,EAAO,UAAU,WAAaA,EAAO,UAAU,UAAY,EAC1H,QAASZ,EAAI,EAAGA,EAAIsJ,EAAO,OAAQtJ,GAAK,EACtCsJ,EAAOtJ,CAAC,EAAE,mBAAqBY,EAAO,aAAY,EAAK0I,EAAOtJ,CAAC,EAAE,WAAasJ,EAAOtJ,CAAC,EAAE,WAAawM,EAAc5L,EAAO,sBAAqB,CAEnJ,CAEA,MAAM6L,GAAuB,CAACvC,EAASwC,EAAWC,IAAc,CAC1DD,GAAa,CAACxC,EAAQ,UAAU,SAASyC,CAAS,EACpDzC,EAAQ,UAAU,IAAIyC,CAAS,EACtB,CAACD,GAAaxC,EAAQ,UAAU,SAASyC,CAAS,GAC3DzC,EAAQ,UAAU,OAAOyC,CAAS,CAEtC,EACA,SAASC,GAAqBC,EAAW,CACnCA,IAAc,SAChBA,EAAY,MAAQ,KAAK,WAAa,GAExC,MAAMjM,EAAS,KACTkI,EAASlI,EAAO,OAChB,CACJ,OAAA0I,EACA,aAAcJ,EACd,SAAAM,CACJ,EAAM5I,EACJ,GAAI0I,EAAO,SAAW,EAAG,OACrB,OAAOA,EAAO,CAAC,EAAE,kBAAsB,KAAa1I,EAAO,mBAAkB,EACjF,IAAIkM,EAAe,CAACD,EAChB3D,IAAK4D,EAAeD,GACxBjM,EAAO,qBAAuB,CAAA,EAC9BA,EAAO,cAAgB,CAAA,EACvB,IAAImJ,EAAejB,EAAO,aACtB,OAAOiB,GAAiB,UAAYA,EAAa,QAAQ,GAAG,GAAK,EACnEA,EAAe,WAAWA,EAAa,QAAQ,IAAK,EAAE,CAAC,EAAI,IAAMnJ,EAAO,KAC/D,OAAOmJ,GAAiB,WACjCA,EAAe,WAAWA,CAAY,GAExC,QAAS/J,EAAI,EAAGA,EAAIsJ,EAAO,OAAQtJ,GAAK,EAAG,CACzC,MAAMsK,EAAQhB,EAAOtJ,CAAC,EACtB,IAAI+M,EAAczC,EAAM,kBACpBxB,EAAO,SAAWA,EAAO,iBAC3BiE,GAAezD,EAAO,CAAC,EAAE,mBAE3B,MAAM0D,GAAiBF,GAAgBhE,EAAO,eAAiBlI,EAAO,aAAY,EAAK,GAAKmM,IAAgBzC,EAAM,gBAAkBP,GAC9HkD,GAAyBH,EAAetD,EAAS,CAAC,GAAKV,EAAO,eAAiBlI,EAAO,aAAY,EAAK,GAAKmM,IAAgBzC,EAAM,gBAAkBP,GACpJmD,EAAc,EAAEJ,EAAeC,GAC/BI,EAAaD,EAActM,EAAO,gBAAgBZ,CAAC,EACnDoN,EAAiBF,GAAe,GAAKA,GAAetM,EAAO,KAAOA,EAAO,gBAAgBZ,CAAC,EAC1FqN,EAAYH,GAAe,GAAKA,EAActM,EAAO,KAAO,GAAKuM,EAAa,GAAKA,GAAcvM,EAAO,MAAQsM,GAAe,GAAKC,GAAcvM,EAAO,KAC3JyM,IACFzM,EAAO,cAAc,KAAK0J,CAAK,EAC/B1J,EAAO,qBAAqB,KAAKZ,CAAC,GAEpCyM,GAAqBnC,EAAO+C,EAAWvE,EAAO,iBAAiB,EAC/D2D,GAAqBnC,EAAO8C,EAAgBtE,EAAO,sBAAsB,EACzEwB,EAAM,SAAWpB,EAAM,CAAC8D,EAAgBA,EACxC1C,EAAM,iBAAmBpB,EAAM,CAAC+D,EAAwBA,CAC1D,CACF,CAEA,SAASK,GAAeT,EAAW,CACjC,MAAMjM,EAAS,KACf,GAAI,OAAOiM,EAAc,IAAa,CACpC,MAAMU,EAAa3M,EAAO,aAAe,GAAK,EAE9CiM,EAAYjM,GAAUA,EAAO,WAAaA,EAAO,UAAY2M,GAAc,CAC7E,CACA,MAAMzE,EAASlI,EAAO,OAChB4M,EAAiB5M,EAAO,aAAY,EAAKA,EAAO,aAAY,EAClE,GAAI,CACF,SAAAW,EACA,YAAAkM,EACA,MAAAC,EACA,aAAAC,CACJ,EAAM/M,EACJ,MAAMgN,EAAeH,EACfI,EAASH,EACf,GAAIF,IAAmB,EACrBjM,EAAW,EACXkM,EAAc,GACdC,EAAQ,OACH,CACLnM,GAAYsL,EAAYjM,EAAO,aAAY,GAAM4M,EACjD,MAAMM,EAAqB,KAAK,IAAIjB,EAAYjM,EAAO,aAAY,CAAE,EAAI,EACnEmN,EAAe,KAAK,IAAIlB,EAAYjM,EAAO,aAAY,CAAE,EAAI,EACnE6M,EAAcK,GAAsBvM,GAAY,EAChDmM,EAAQK,GAAgBxM,GAAY,EAChCuM,IAAoBvM,EAAW,GAC/BwM,IAAcxM,EAAW,EAC/B,CACA,GAAIuH,EAAO,KAAM,CACf,MAAMkF,EAAkBpN,EAAO,oBAAoB,CAAC,EAC9CqN,EAAiBrN,EAAO,oBAAoBA,EAAO,OAAO,OAAS,CAAC,EACpEsN,EAAsBtN,EAAO,WAAWoN,CAAe,EACvDG,EAAqBvN,EAAO,WAAWqN,CAAc,EACrDG,EAAexN,EAAO,WAAWA,EAAO,WAAW,OAAS,CAAC,EAC7DyN,EAAe,KAAK,IAAIxB,CAAS,EACnCwB,GAAgBH,EAClBP,GAAgBU,EAAeH,GAAuBE,EAEtDT,GAAgBU,EAAeD,EAAeD,GAAsBC,EAElET,EAAe,IAAGA,GAAgB,EACxC,CACA,OAAO,OAAO/M,EAAQ,CACpB,SAAAW,EACA,aAAAoM,EACA,YAAAF,EACA,MAAAC,CACJ,CAAG,GACG5E,EAAO,qBAAuBA,EAAO,gBAAkBA,EAAO,aAAYlI,EAAO,qBAAqBiM,CAAS,EAC/GY,GAAe,CAACG,GAClBhN,EAAO,KAAK,uBAAuB,EAEjC8M,GAAS,CAACG,GACZjN,EAAO,KAAK,iBAAiB,GAE3BgN,GAAgB,CAACH,GAAeI,GAAU,CAACH,IAC7C9M,EAAO,KAAK,UAAU,EAExBA,EAAO,KAAK,WAAYW,CAAQ,CAClC,CAEA,MAAM+M,GAAqB,CAACpE,EAASwC,EAAWC,IAAc,CACxDD,GAAa,CAACxC,EAAQ,UAAU,SAASyC,CAAS,EACpDzC,EAAQ,UAAU,IAAIyC,CAAS,EACtB,CAACD,GAAaxC,EAAQ,UAAU,SAASyC,CAAS,GAC3DzC,EAAQ,UAAU,OAAOyC,CAAS,CAEtC,EACA,SAAS4B,IAAsB,CAC7B,MAAM3N,EAAS,KACT,CACJ,OAAA0I,EACA,OAAAR,EACA,SAAAE,EACA,YAAAwF,CACJ,EAAM5N,EACEwI,EAAYxI,EAAO,SAAWkI,EAAO,QAAQ,QAC7CqB,EAAcvJ,EAAO,MAAQkI,EAAO,MAAQA,EAAO,KAAK,KAAO,EAC/D2F,EAAmB7M,GAChBF,EAAgBsH,EAAU,IAAIF,EAAO,UAAU,GAAGlH,CAAQ,iBAAiBA,CAAQ,EAAE,EAAE,CAAC,EAEjG,IAAI8M,EACAC,EACAC,EACJ,GAAIxF,EACF,GAAIN,EAAO,KAAM,CACf,IAAIwC,EAAakD,EAAc5N,EAAO,QAAQ,aAC1C0K,EAAa,IAAGA,EAAa1K,EAAO,QAAQ,OAAO,OAAS0K,GAC5DA,GAAc1K,EAAO,QAAQ,OAAO,SAAQ0K,GAAc1K,EAAO,QAAQ,OAAO,QACpF8N,EAAcD,EAAiB,6BAA6BnD,CAAU,IAAI,CAC5E,MACEoD,EAAcD,EAAiB,6BAA6BD,CAAW,IAAI,OAGzErE,GACFuE,EAAcpF,EAAO,KAAKY,GAAWA,EAAQ,SAAWsE,CAAW,EACnEI,EAAYtF,EAAO,KAAKY,GAAWA,EAAQ,SAAWsE,EAAc,CAAC,EACrEG,EAAYrF,EAAO,KAAKY,GAAWA,EAAQ,SAAWsE,EAAc,CAAC,GAErEE,EAAcpF,EAAOkF,CAAW,EAGhCE,IACGvE,IAEHyE,EAAYhM,GAAe8L,EAAa,IAAI5F,EAAO,UAAU,gBAAgB,EAAE,CAAC,EAC5EA,EAAO,MAAQ,CAAC8F,IAClBA,EAAYtF,EAAO,CAAC,GAItBqF,EAAYlM,GAAeiM,EAAa,IAAI5F,EAAO,UAAU,gBAAgB,EAAE,CAAC,EAC5EA,EAAO,MAAQ,CAAC6F,IAAc,IAChCA,EAAYrF,EAAOA,EAAO,OAAS,CAAC,KAI1CA,EAAO,QAAQY,GAAW,CACxBoE,GAAmBpE,EAASA,IAAYwE,EAAa5F,EAAO,gBAAgB,EAC5EwF,GAAmBpE,EAASA,IAAY0E,EAAW9F,EAAO,cAAc,EACxEwF,GAAmBpE,EAASA,IAAYyE,EAAW7F,EAAO,cAAc,CAC1E,CAAC,EACDlI,EAAO,kBAAiB,CAC1B,CAEA,MAAMiO,GAAuB,CAACjO,EAAQkO,IAAY,CAChD,GAAI,CAAClO,GAAUA,EAAO,WAAa,CAACA,EAAO,OAAQ,OACnD,MAAMmO,EAAgB,IAAMnO,EAAO,UAAY,eAAiB,IAAIA,EAAO,OAAO,UAAU,GACtFsJ,EAAU4E,EAAQ,QAAQC,EAAa,CAAE,EAC/C,GAAI7E,EAAS,CACX,IAAI8E,EAAS9E,EAAQ,cAAc,IAAItJ,EAAO,OAAO,kBAAkB,EAAE,EACrE,CAACoO,GAAUpO,EAAO,YAChBsJ,EAAQ,WACV8E,EAAS9E,EAAQ,WAAW,cAAc,IAAItJ,EAAO,OAAO,kBAAkB,EAAE,EAGhF,sBAAsB,IAAM,CACtBsJ,EAAQ,aACV8E,EAAS9E,EAAQ,WAAW,cAAc,IAAItJ,EAAO,OAAO,kBAAkB,EAAE,EAC5EoO,GAAQA,EAAO,OAAM,EAE7B,CAAC,GAGDA,GAAQA,EAAO,OAAM,CAC3B,CACF,EACMC,GAAS,CAACrO,EAAQwH,IAAU,CAChC,GAAI,CAACxH,EAAO,OAAOwH,CAAK,EAAG,OAC3B,MAAM0G,EAAUlO,EAAO,OAAOwH,CAAK,EAAE,cAAc,kBAAkB,EACjE0G,GAASA,EAAQ,gBAAgB,SAAS,CAChD,EACMI,GAAUtO,GAAU,CACxB,GAAI,CAACA,GAAUA,EAAO,WAAa,CAACA,EAAO,OAAQ,OACnD,IAAIuO,EAASvO,EAAO,OAAO,oBAC3B,MAAMR,EAAMQ,EAAO,OAAO,OAC1B,GAAI,CAACR,GAAO,CAAC+O,GAAUA,EAAS,EAAG,OACnCA,EAAS,KAAK,IAAIA,EAAQ/O,CAAG,EAC7B,MAAMgP,EAAgBxO,EAAO,OAAO,gBAAkB,OAASA,EAAO,qBAAoB,EAAK,KAAK,KAAKA,EAAO,OAAO,aAAa,EAC9H4N,EAAc5N,EAAO,YAC3B,GAAIA,EAAO,OAAO,MAAQA,EAAO,OAAO,KAAK,KAAO,EAAG,CACrD,MAAMyO,EAAeb,EACfc,EAAiB,CAACD,EAAeF,CAAM,EAC7CG,EAAe,KAAK,GAAG,MAAM,KAAK,CAChC,OAAQH,CACd,CAAK,EAAE,IAAI,CAAC9D,EAAGrL,IACFqP,EAAeD,EAAgBpP,CACvC,CAAC,EACFY,EAAO,OAAO,QAAQ,CAACsJ,EAASlK,IAAM,CAChCsP,EAAe,SAASpF,EAAQ,MAAM,GAAG+E,GAAOrO,EAAQZ,CAAC,CAC/D,CAAC,EACD,MACF,CACA,MAAMuP,EAAuBf,EAAcY,EAAgB,EAC3D,GAAIxO,EAAO,OAAO,QAAUA,EAAO,OAAO,KACxC,QAASZ,EAAIwO,EAAcW,EAAQnP,GAAKuP,EAAuBJ,EAAQnP,GAAK,EAAG,CAC7E,MAAMwP,GAAaxP,EAAII,EAAMA,GAAOA,GAChCoP,EAAYhB,GAAegB,EAAYD,IAAsBN,GAAOrO,EAAQ4O,CAAS,CAC3F,KAEA,SAASxP,EAAI,KAAK,IAAIwO,EAAcW,EAAQ,CAAC,EAAGnP,GAAK,KAAK,IAAIuP,EAAuBJ,EAAQ/O,EAAM,CAAC,EAAGJ,GAAK,EACtGA,IAAMwO,IAAgBxO,EAAIuP,GAAwBvP,EAAIwO,IACxDS,GAAOrO,EAAQZ,CAAC,CAIxB,EAEA,SAASyP,GAA0B7O,EAAQ,CACzC,KAAM,CACJ,WAAA6I,EACA,OAAAX,CACJ,EAAMlI,EACEiM,EAAYjM,EAAO,aAAeA,EAAO,UAAY,CAACA,EAAO,UACnE,IAAI4N,EACJ,QAASxO,EAAI,EAAGA,EAAIyJ,EAAW,OAAQzJ,GAAK,EACtC,OAAOyJ,EAAWzJ,EAAI,CAAC,EAAM,IAC3B6M,GAAapD,EAAWzJ,CAAC,GAAK6M,EAAYpD,EAAWzJ,EAAI,CAAC,GAAKyJ,EAAWzJ,EAAI,CAAC,EAAIyJ,EAAWzJ,CAAC,GAAK,EACtGwO,EAAcxO,EACL6M,GAAapD,EAAWzJ,CAAC,GAAK6M,EAAYpD,EAAWzJ,EAAI,CAAC,IACnEwO,EAAcxO,EAAI,GAEX6M,GAAapD,EAAWzJ,CAAC,IAClCwO,EAAcxO,GAIlB,OAAI8I,EAAO,sBACL0F,EAAc,GAAK,OAAOA,EAAgB,OAAaA,EAAc,GAEpEA,CACT,CACA,SAASkB,GAAkBC,EAAgB,CACzC,MAAM/O,EAAS,KACTiM,EAAYjM,EAAO,aAAeA,EAAO,UAAY,CAACA,EAAO,UAC7D,CACJ,SAAA4I,EACA,OAAAV,EACA,YAAa8G,EACb,UAAWC,EACX,UAAWC,CACf,EAAMlP,EACJ,IAAI4N,EAAcmB,EACd9D,EACJ,MAAMkE,EAAsBC,GAAU,CACpC,IAAIR,EAAYQ,EAASpP,EAAO,QAAQ,aACxC,OAAI4O,EAAY,IACdA,EAAY5O,EAAO,QAAQ,OAAO,OAAS4O,GAEzCA,GAAa5O,EAAO,QAAQ,OAAO,SACrC4O,GAAa5O,EAAO,QAAQ,OAAO,QAE9B4O,CACT,EAIA,GAHI,OAAOhB,EAAgB,MACzBA,EAAciB,GAA0B7O,CAAM,GAE5C4I,EAAS,QAAQqD,CAAS,GAAK,EACjChB,EAAYrC,EAAS,QAAQqD,CAAS,MACjC,CACL,MAAMoD,EAAO,KAAK,IAAInH,EAAO,mBAAoB0F,CAAW,EAC5D3C,EAAYoE,EAAO,KAAK,OAAOzB,EAAcyB,GAAQnH,EAAO,cAAc,CAC5E,CAEA,GADI+C,GAAarC,EAAS,SAAQqC,EAAYrC,EAAS,OAAS,GAC5DgF,IAAgBoB,GAAiB,CAAChP,EAAO,OAAO,KAAM,CACpDiL,IAAciE,IAChBlP,EAAO,UAAYiL,EACnBjL,EAAO,KAAK,iBAAiB,GAE/B,MACF,CACA,GAAI4N,IAAgBoB,GAAiBhP,EAAO,OAAO,MAAQA,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAAS,CAC1GA,EAAO,UAAYmP,EAAoBvB,CAAW,EAClD,MACF,CACA,MAAMrE,EAAcvJ,EAAO,MAAQkI,EAAO,MAAQA,EAAO,KAAK,KAAO,EAGrE,IAAI0G,EACJ,GAAI5O,EAAO,SAAWkI,EAAO,QAAQ,SAAWA,EAAO,KACrD0G,EAAYO,EAAoBvB,CAAW,UAClCrE,EAAa,CACtB,MAAM+F,EAAqBtP,EAAO,OAAO,KAAKsJ,GAAWA,EAAQ,SAAWsE,CAAW,EACvF,IAAI2B,EAAmB,SAASD,EAAmB,aAAa,yBAAyB,EAAG,EAAE,EAC1F,OAAO,MAAMC,CAAgB,IAC/BA,EAAmB,KAAK,IAAIvP,EAAO,OAAO,QAAQsP,CAAkB,EAAG,CAAC,GAE1EV,EAAY,KAAK,MAAMW,EAAmBrH,EAAO,KAAK,IAAI,CAC5D,SAAWlI,EAAO,OAAO4N,CAAW,EAAG,CACrC,MAAMlD,EAAa1K,EAAO,OAAO4N,CAAW,EAAE,aAAa,yBAAyB,EAChFlD,EACFkE,EAAY,SAASlE,EAAY,EAAE,EAEnCkE,EAAYhB,CAEhB,MACEgB,EAAYhB,EAEd,OAAO,OAAO5N,EAAQ,CACpB,kBAAAkP,EACA,UAAAjE,EACA,kBAAAgE,EACA,UAAAL,EACA,cAAAI,EACA,YAAApB,CACJ,CAAG,EACG5N,EAAO,aACTsO,GAAQtO,CAAM,EAEhBA,EAAO,KAAK,mBAAmB,EAC/BA,EAAO,KAAK,iBAAiB,GACzBA,EAAO,aAAeA,EAAO,OAAO,sBAClCiP,IAAsBL,GACxB5O,EAAO,KAAK,iBAAiB,EAE/BA,EAAO,KAAK,aAAa,EAE7B,CAEA,SAASwP,GAAmBjR,EAAIkR,EAAM,CACpC,MAAMzP,EAAS,KACTkI,EAASlI,EAAO,OACtB,IAAI0J,EAAQnL,EAAG,QAAQ,IAAI2J,EAAO,UAAU,gBAAgB,EACxD,CAACwB,GAAS1J,EAAO,WAAayP,GAAQA,EAAK,OAAS,GAAKA,EAAK,SAASlR,CAAE,GAC3E,CAAC,GAAGkR,EAAK,MAAMA,EAAK,QAAQlR,CAAE,EAAI,EAAGkR,EAAK,MAAM,CAAC,EAAE,QAAQC,GAAU,CAC/D,CAAChG,GAASgG,EAAO,SAAWA,EAAO,QAAQ,IAAIxH,EAAO,UAAU,gBAAgB,IAClFwB,EAAQgG,EAEZ,CAAC,EAEH,IAAIC,EAAa,GACbjF,EACJ,GAAIhB,GACF,QAAStK,EAAI,EAAGA,EAAIY,EAAO,OAAO,OAAQZ,GAAK,EAC7C,GAAIY,EAAO,OAAOZ,CAAC,IAAMsK,EAAO,CAC9BiG,EAAa,GACbjF,EAAatL,EACb,KACF,EAGJ,GAAIsK,GAASiG,EACX3P,EAAO,aAAe0J,EAClB1J,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAC1CA,EAAO,aAAe,SAAS0J,EAAM,aAAa,yBAAyB,EAAG,EAAE,EAEhF1J,EAAO,aAAe0K,MAEnB,CACL1K,EAAO,aAAe,OACtBA,EAAO,aAAe,OACtB,MACF,CACIkI,EAAO,qBAAuBlI,EAAO,eAAiB,QAAaA,EAAO,eAAiBA,EAAO,aACpGA,EAAO,oBAAmB,CAE9B,CAEA,IAAI4P,GAAS,CACX,WAAA9H,GACA,aAAAC,GACA,iBAAAwD,GACA,mBAAAI,GACA,qBAAAK,GACA,eAAAU,GACA,oBAAAiB,GACA,kBAAAmB,GACA,mBAAAU,EACF,EAEA,SAASK,GAAmBlR,EAAM,CAC5BA,IAAS,SACXA,EAAO,KAAK,aAAY,EAAK,IAAM,KAErC,MAAMqB,EAAS,KACT,CACJ,OAAAkI,EACA,aAAcI,EACd,UAAA2D,EACA,UAAA9D,CACJ,EAAMnI,EACJ,GAAIkI,EAAO,iBACT,OAAOI,EAAM,CAAC2D,EAAYA,EAE5B,GAAI/D,EAAO,QACT,OAAO+D,EAET,IAAI6D,EAAmBpR,GAAayJ,EAAWxJ,CAAI,EACnD,OAAAmR,GAAoB9P,EAAO,sBAAqB,EAC5CsI,IAAKwH,EAAmB,CAACA,GACtBA,GAAoB,CAC7B,CAEA,SAASC,GAAa9D,EAAW+D,EAAc,CAC7C,MAAMhQ,EAAS,KACT,CACJ,aAAcsI,EACd,OAAAJ,EACA,UAAAC,EACA,SAAAxH,CACJ,EAAMX,EACJ,IAAIiQ,EAAI,EACJC,EAAI,EACR,MAAMC,EAAI,EACNnQ,EAAO,eACTiQ,EAAI3H,EAAM,CAAC2D,EAAYA,EAEvBiE,EAAIjE,EAEF/D,EAAO,eACT+H,EAAI,KAAK,MAAMA,CAAC,EAChBC,EAAI,KAAK,MAAMA,CAAC,GAElBlQ,EAAO,kBAAoBA,EAAO,UAClCA,EAAO,UAAYA,EAAO,aAAY,EAAKiQ,EAAIC,EAC3ChI,EAAO,QACTC,EAAUnI,EAAO,aAAY,EAAK,aAAe,WAAW,EAAIA,EAAO,aAAY,EAAK,CAACiQ,EAAI,CAACC,EACpFhI,EAAO,mBACblI,EAAO,eACTiQ,GAAKjQ,EAAO,sBAAqB,EAEjCkQ,GAAKlQ,EAAO,sBAAqB,EAEnCmI,EAAU,MAAM,UAAY,eAAe8H,CAAC,OAAOC,CAAC,OAAOC,CAAC,OAI9D,IAAIC,EACJ,MAAMxD,EAAiB5M,EAAO,aAAY,EAAKA,EAAO,aAAY,EAC9D4M,IAAmB,EACrBwD,EAAc,EAEdA,GAAenE,EAAYjM,EAAO,aAAY,GAAM4M,EAElDwD,IAAgBzP,GAClBX,EAAO,eAAeiM,CAAS,EAEjCjM,EAAO,KAAK,eAAgBA,EAAO,UAAWgQ,CAAY,CAC5D,CAEA,SAASK,IAAe,CACtB,MAAO,CAAC,KAAK,SAAS,CAAC,CACzB,CAEA,SAASC,IAAe,CACtB,MAAO,CAAC,KAAK,SAAS,KAAK,SAAS,OAAS,CAAC,CAChD,CAEA,SAASC,GAAYtE,EAAWT,EAAOgF,EAAcC,EAAiBC,EAAU,CAC1EzE,IAAc,SAChBA,EAAY,GAEVT,IAAU,SACZA,EAAQ,KAAK,OAAO,OAElBgF,IAAiB,SACnBA,EAAe,IAEbC,IAAoB,SACtBA,EAAkB,IAEpB,MAAMzQ,EAAS,KACT,CACJ,OAAAkI,EACA,UAAAC,CACJ,EAAMnI,EACJ,GAAIA,EAAO,WAAakI,EAAO,+BAC7B,MAAO,GAET,MAAMmI,EAAerQ,EAAO,aAAY,EAClCsQ,EAAetQ,EAAO,aAAY,EACxC,IAAI2Q,EAKJ,GAJIF,GAAmBxE,EAAYoE,EAAcM,EAAeN,EAAsBI,GAAmBxE,EAAYqE,EAAcK,EAAeL,EAAkBK,EAAe1E,EAGnLjM,EAAO,eAAe2Q,CAAY,EAC9BzI,EAAO,QAAS,CAClB,MAAM0I,EAAM5Q,EAAO,aAAY,EAC/B,GAAIwL,IAAU,EACZrD,EAAUyI,EAAM,aAAe,WAAW,EAAI,CAACD,MAC1C,CACL,GAAI,CAAC3Q,EAAO,QAAQ,aAClB,OAAAF,GAAqB,CACnB,OAAAE,EACA,eAAgB,CAAC2Q,EACjB,KAAMC,EAAM,OAAS,KAC/B,CAAS,EACM,GAETzI,EAAU,SAAS,CACjB,CAACyI,EAAM,OAAS,KAAK,EAAG,CAACD,EACzB,SAAU,QAClB,CAAO,CACH,CACA,MAAO,EACT,CACA,OAAInF,IAAU,GACZxL,EAAO,cAAc,CAAC,EACtBA,EAAO,aAAa2Q,CAAY,EAC5BH,IACFxQ,EAAO,KAAK,wBAAyBwL,EAAOkF,CAAQ,EACpD1Q,EAAO,KAAK,eAAe,KAG7BA,EAAO,cAAcwL,CAAK,EAC1BxL,EAAO,aAAa2Q,CAAY,EAC5BH,IACFxQ,EAAO,KAAK,wBAAyBwL,EAAOkF,CAAQ,EACpD1Q,EAAO,KAAK,iBAAiB,GAE1BA,EAAO,YACVA,EAAO,UAAY,GACdA,EAAO,oCACVA,EAAO,kCAAoC,SAAuB6Q,EAAG,CAC/D,CAAC7Q,GAAUA,EAAO,WAClB6Q,EAAE,SAAW,OACjB7Q,EAAO,UAAU,oBAAoB,gBAAiBA,EAAO,iCAAiC,EAC9FA,EAAO,kCAAoC,KAC3C,OAAOA,EAAO,kCACdA,EAAO,UAAY,GACfwQ,GACFxQ,EAAO,KAAK,eAAe,EAE/B,GAEFA,EAAO,UAAU,iBAAiB,gBAAiBA,EAAO,iCAAiC,IAGxF,EACT,CAEA,IAAIiM,GAAY,CACd,aAAc4D,GACd,aAAAE,GACA,aAAAM,GACA,aAAAC,GACA,YAAAC,EACF,EAEA,SAASO,GAAcxQ,EAAU0P,EAAc,CAC7C,MAAMhQ,EAAS,KACVA,EAAO,OAAO,UACjBA,EAAO,UAAU,MAAM,mBAAqB,GAAGM,CAAQ,KACvDN,EAAO,UAAU,MAAM,gBAAkBM,IAAa,EAAI,MAAQ,IAEpEN,EAAO,KAAK,gBAAiBM,EAAU0P,CAAY,CACrD,CAEA,SAASe,GAAehR,EAAM,CAC5B,GAAI,CACF,OAAAC,EACA,aAAAwQ,EACA,UAAAQ,EACA,KAAAC,CACJ,EAAMlR,EACJ,KAAM,CACJ,YAAA6N,EACA,cAAAoB,CACJ,EAAMhP,EACJ,IAAIO,EAAMyQ,EACLzQ,IACCqN,EAAcoB,EAAezO,EAAM,OAAgBqN,EAAcoB,EAAezO,EAAM,OAAYA,EAAM,SAE9GP,EAAO,KAAK,aAAaiR,CAAI,EAAE,EAC3BT,GAAgBjQ,IAAQ,QAC1BP,EAAO,KAAK,uBAAuBiR,CAAI,EAAE,EAChCT,GAAgB5C,IAAgBoB,IACzChP,EAAO,KAAK,wBAAwBiR,CAAI,EAAE,EACtC1Q,IAAQ,OACVP,EAAO,KAAK,sBAAsBiR,CAAI,EAAE,EAExCjR,EAAO,KAAK,sBAAsBiR,CAAI,EAAE,EAG9C,CAEA,SAASC,GAAgBV,EAAcQ,EAAW,CAC5CR,IAAiB,SACnBA,EAAe,IAEjB,MAAMxQ,EAAS,KACT,CACJ,OAAAkI,CACJ,EAAMlI,EACAkI,EAAO,UACPA,EAAO,YACTlI,EAAO,iBAAgB,EAEzB+Q,GAAe,CACb,OAAA/Q,EACA,aAAAwQ,EACA,UAAAQ,EACA,KAAM,OACV,CAAG,EACH,CAEA,SAASG,GAAcX,EAAcQ,EAAW,CAC1CR,IAAiB,SACnBA,EAAe,IAEjB,MAAMxQ,EAAS,KACT,CACJ,OAAAkI,CACJ,EAAMlI,EACJA,EAAO,UAAY,GACf,CAAAkI,EAAO,UACXlI,EAAO,cAAc,CAAC,EACtB+Q,GAAe,CACb,OAAA/Q,EACA,aAAAwQ,EACA,UAAAQ,EACA,KAAM,KACV,CAAG,EACH,CAEA,IAAII,GAAa,CACf,cAAAN,GACA,gBAAAI,GACA,cAAAC,EACF,EAEA,SAASE,GAAQ7J,EAAOgE,EAAOgF,EAAcE,EAAUY,EAAS,CAC1D9J,IAAU,SACZA,EAAQ,GAENgJ,IAAiB,SACnBA,EAAe,IAEb,OAAOhJ,GAAU,WACnBA,EAAQ,SAASA,EAAO,EAAE,GAE5B,MAAMxH,EAAS,KACf,IAAI0K,EAAalD,EACbkD,EAAa,IAAGA,EAAa,GACjC,KAAM,CACJ,OAAAxC,EACA,SAAAU,EACA,WAAAC,EACA,cAAAmG,EACA,YAAApB,EACA,aAActF,EACd,UAAAH,EACA,QAAAoJ,CACJ,EAAMvR,EACJ,GAAI,CAACuR,GAAW,CAACb,GAAY,CAACY,GAAWtR,EAAO,WAAaA,EAAO,WAAakI,EAAO,+BACtF,MAAO,GAEL,OAAOsD,EAAU,MACnBA,EAAQxL,EAAO,OAAO,OAExB,MAAMqP,EAAO,KAAK,IAAIrP,EAAO,OAAO,mBAAoB0K,CAAU,EAClE,IAAIO,EAAYoE,EAAO,KAAK,OAAO3E,EAAa2E,GAAQrP,EAAO,OAAO,cAAc,EAChFiL,GAAarC,EAAS,SAAQqC,EAAYrC,EAAS,OAAS,GAChE,MAAMqD,EAAY,CAACrD,EAASqC,CAAS,EAErC,GAAI/C,EAAO,oBACT,QAAS9I,EAAI,EAAGA,EAAIyJ,EAAW,OAAQzJ,GAAK,EAAG,CAC7C,MAAMoS,EAAsB,CAAC,KAAK,MAAMvF,EAAY,GAAG,EACjDwF,EAAiB,KAAK,MAAM5I,EAAWzJ,CAAC,EAAI,GAAG,EAC/CsS,EAAqB,KAAK,MAAM7I,EAAWzJ,EAAI,CAAC,EAAI,GAAG,EACzD,OAAOyJ,EAAWzJ,EAAI,CAAC,EAAM,IAC3BoS,GAAuBC,GAAkBD,EAAsBE,GAAsBA,EAAqBD,GAAkB,EAC9H/G,EAAatL,EACJoS,GAAuBC,GAAkBD,EAAsBE,IACxEhH,EAAatL,EAAI,GAEVoS,GAAuBC,IAChC/G,EAAatL,EAEjB,CAGF,GAAIY,EAAO,aAAe0K,IAAekD,IACnC,CAAC5N,EAAO,iBAAmBsI,EAAM2D,EAAYjM,EAAO,WAAaiM,EAAYjM,EAAO,aAAY,EAAKiM,EAAYjM,EAAO,WAAaiM,EAAYjM,EAAO,aAAY,IAGpK,CAACA,EAAO,gBAAkBiM,EAAYjM,EAAO,WAAaiM,EAAYjM,EAAO,iBAC1E4N,GAAe,KAAOlD,GACzB,MAAO,GAITA,KAAgBsE,GAAiB,IAAMwB,GACzCxQ,EAAO,KAAK,wBAAwB,EAItCA,EAAO,eAAeiM,CAAS,EAC/B,IAAI+E,EACAtG,EAAakD,EAAaoD,EAAY,OAAgBtG,EAAakD,EAAaoD,EAAY,OAAYA,EAAY,QAGxH,MAAMxI,EAAYxI,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAG1D,GAAI,EAFqBwI,GAAa8I,KAEZhJ,GAAO,CAAC2D,IAAcjM,EAAO,WAAa,CAACsI,GAAO2D,IAAcjM,EAAO,WAC/F,OAAAA,EAAO,kBAAkB0K,CAAU,EAE/BxC,EAAO,YACTlI,EAAO,iBAAgB,EAEzBA,EAAO,oBAAmB,EACtBkI,EAAO,SAAW,SACpBlI,EAAO,aAAaiM,CAAS,EAE3B+E,IAAc,UAChBhR,EAAO,gBAAgBwQ,EAAcQ,CAAS,EAC9ChR,EAAO,cAAcwQ,EAAcQ,CAAS,GAEvC,GAET,GAAI9I,EAAO,QAAS,CAClB,MAAM0I,EAAM5Q,EAAO,aAAY,EACzB2R,EAAIrJ,EAAM2D,EAAY,CAACA,EAC7B,GAAIT,IAAU,EACRhD,IACFxI,EAAO,UAAU,MAAM,eAAiB,OACxCA,EAAO,kBAAoB,IAEzBwI,GAAa,CAACxI,EAAO,2BAA6BA,EAAO,OAAO,aAAe,GACjFA,EAAO,0BAA4B,GACnC,sBAAsB,IAAM,CAC1BmI,EAAUyI,EAAM,aAAe,WAAW,EAAIe,CAChD,CAAC,GAEDxJ,EAAUyI,EAAM,aAAe,WAAW,EAAIe,EAE5CnJ,GACF,sBAAsB,IAAM,CAC1BxI,EAAO,UAAU,MAAM,eAAiB,GACxCA,EAAO,kBAAoB,EAC7B,CAAC,MAEE,CACL,GAAI,CAACA,EAAO,QAAQ,aAClB,OAAAF,GAAqB,CACnB,OAAAE,EACA,eAAgB2R,EAChB,KAAMf,EAAM,OAAS,KAC/B,CAAS,EACM,GAETzI,EAAU,SAAS,CACjB,CAACyI,EAAM,OAAS,KAAK,EAAGe,EACxB,SAAU,QAClB,CAAO,CACH,CACA,MAAO,EACT,CAEA,MAAMlN,EADUO,GAAU,EACD,SACzB,OAAIwD,GAAa,CAAC8I,GAAW7M,GAAYzE,EAAO,WAC9CA,EAAO,QAAQ,OAAO,GAAO,GAAO0K,CAAU,EAEhD1K,EAAO,cAAcwL,CAAK,EAC1BxL,EAAO,aAAaiM,CAAS,EAC7BjM,EAAO,kBAAkB0K,CAAU,EACnC1K,EAAO,oBAAmB,EAC1BA,EAAO,KAAK,wBAAyBwL,EAAOkF,CAAQ,EACpD1Q,EAAO,gBAAgBwQ,EAAcQ,CAAS,EAC1CxF,IAAU,EACZxL,EAAO,cAAcwQ,EAAcQ,CAAS,EAClChR,EAAO,YACjBA,EAAO,UAAY,GACdA,EAAO,gCACVA,EAAO,8BAAgC,SAAuB6Q,EAAG,CAC3D,CAAC7Q,GAAUA,EAAO,WAClB6Q,EAAE,SAAW,OACjB7Q,EAAO,UAAU,oBAAoB,gBAAiBA,EAAO,6BAA6B,EAC1FA,EAAO,8BAAgC,KACvC,OAAOA,EAAO,8BACdA,EAAO,cAAcwQ,EAAcQ,CAAS,EAC9C,GAEFhR,EAAO,UAAU,iBAAiB,gBAAiBA,EAAO,6BAA6B,GAElF,EACT,CAEA,SAAS4R,GAAYpK,EAAOgE,EAAOgF,EAAcE,EAAU,CACrDlJ,IAAU,SACZA,EAAQ,GAENgJ,IAAiB,SACnBA,EAAe,IAEb,OAAOhJ,GAAU,WAEnBA,EADsB,SAASA,EAAO,EAAE,GAG1C,MAAMxH,EAAS,KACf,GAAIA,EAAO,UAAW,OAClB,OAAOwL,EAAU,MACnBA,EAAQxL,EAAO,OAAO,OAExB,MAAMuJ,EAAcvJ,EAAO,MAAQA,EAAO,OAAO,MAAQA,EAAO,OAAO,KAAK,KAAO,EACnF,IAAI6R,EAAWrK,EACf,GAAIxH,EAAO,OAAO,KAChB,GAAIA,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAE1C6R,EAAWA,EAAW7R,EAAO,QAAQ,iBAChC,CACL,IAAI8R,EACJ,GAAIvI,EAAa,CACf,MAAMmB,EAAamH,EAAW7R,EAAO,OAAO,KAAK,KACjD8R,EAAmB9R,EAAO,OAAO,KAAKsJ,GAAWA,EAAQ,aAAa,yBAAyB,EAAI,IAAMoB,CAAU,EAAE,MACvH,MACEoH,EAAmB9R,EAAO,oBAAoB6R,CAAQ,EAExD,MAAME,EAAOxI,EAAc,KAAK,KAAKvJ,EAAO,OAAO,OAASA,EAAO,OAAO,KAAK,IAAI,EAAIA,EAAO,OAAO,OAC/F,CACJ,eAAAgS,CACR,EAAUhS,EAAO,OACX,IAAIwO,EAAgBxO,EAAO,OAAO,cAC9BwO,IAAkB,OACpBA,EAAgBxO,EAAO,qBAAoB,GAE3CwO,EAAgB,KAAK,KAAK,WAAWxO,EAAO,OAAO,cAAe,EAAE,CAAC,EACjEgS,GAAkBxD,EAAgB,IAAM,IAC1CA,EAAgBA,EAAgB,IAGpC,IAAIyD,EAAcF,EAAOD,EAAmBtD,EAO5C,GANIwD,IACFC,EAAcA,GAAeH,EAAmB,KAAK,KAAKtD,EAAgB,CAAC,GAEzEkC,GAAYsB,GAAkBhS,EAAO,OAAO,gBAAkB,QAAU,CAACuJ,IAC3E0I,EAAc,IAEZA,EAAa,CACf,MAAMjB,EAAYgB,EAAiBF,EAAmB9R,EAAO,YAAc,OAAS,OAAS8R,EAAmB9R,EAAO,YAAc,EAAIA,EAAO,OAAO,cAAgB,OAAS,OAChLA,EAAO,QAAQ,CACb,UAAAgR,EACA,QAAS,GACT,iBAAkBA,IAAc,OAASc,EAAmB,EAAIA,EAAmBC,EAAO,EAC1F,eAAgBf,IAAc,OAAShR,EAAO,UAAY,MACpE,CAAS,CACH,CACA,GAAIuJ,EAAa,CACf,MAAMmB,EAAamH,EAAW7R,EAAO,OAAO,KAAK,KACjD6R,EAAW7R,EAAO,OAAO,KAAKsJ,GAAWA,EAAQ,aAAa,yBAAyB,EAAI,IAAMoB,CAAU,EAAE,MAC/G,MACEmH,EAAW7R,EAAO,oBAAoB6R,CAAQ,CAElD,CAEF,6BAAsB,IAAM,CAC1B7R,EAAO,QAAQ6R,EAAUrG,EAAOgF,EAAcE,CAAQ,CACxD,CAAC,EACM1Q,CACT,CAGA,SAASkS,GAAU1G,EAAOgF,EAAcE,EAAU,CAC5CF,IAAiB,SACnBA,EAAe,IAEjB,MAAMxQ,EAAS,KACT,CACJ,QAAAuR,EACA,OAAArJ,EACA,UAAAiK,CACJ,EAAMnS,EACJ,GAAI,CAACuR,GAAWvR,EAAO,UAAW,OAAOA,EACrC,OAAOwL,EAAU,MACnBA,EAAQxL,EAAO,OAAO,OAExB,IAAIoS,EAAWlK,EAAO,eAClBA,EAAO,gBAAkB,QAAUA,EAAO,iBAAmB,GAAKA,EAAO,qBAC3EkK,EAAW,KAAK,IAAIpS,EAAO,qBAAqB,UAAW,EAAI,EAAG,CAAC,GAErE,MAAMqS,EAAYrS,EAAO,YAAckI,EAAO,mBAAqB,EAAIkK,EACjE5J,EAAYxI,EAAO,SAAWkI,EAAO,QAAQ,QACnD,GAAIA,EAAO,KAAM,CACf,GAAIiK,GAAa,CAAC3J,GAAaN,EAAO,oBAAqB,MAAO,GAMlE,GALAlI,EAAO,QAAQ,CACb,UAAW,MACjB,CAAK,EAEDA,EAAO,YAAcA,EAAO,UAAU,WAClCA,EAAO,cAAgBA,EAAO,OAAO,OAAS,GAAKkI,EAAO,QAC5D,6BAAsB,IAAM,CAC1BlI,EAAO,QAAQA,EAAO,YAAcqS,EAAW7G,EAAOgF,EAAcE,CAAQ,CAC9E,CAAC,EACM,EAEX,CACA,OAAIxI,EAAO,QAAUlI,EAAO,MACnBA,EAAO,QAAQ,EAAGwL,EAAOgF,EAAcE,CAAQ,EAEjD1Q,EAAO,QAAQA,EAAO,YAAcqS,EAAW7G,EAAOgF,EAAcE,CAAQ,CACrF,CAGA,SAAS4B,GAAU9G,EAAOgF,EAAcE,EAAU,CAC5CF,IAAiB,SACnBA,EAAe,IAEjB,MAAMxQ,EAAS,KACT,CACJ,OAAAkI,EACA,SAAAU,EACA,WAAAC,EACA,aAAA0J,EACA,QAAAhB,EACA,UAAAY,CACJ,EAAMnS,EACJ,GAAI,CAACuR,GAAWvR,EAAO,UAAW,OAAOA,EACrC,OAAOwL,EAAU,MACnBA,EAAQxL,EAAO,OAAO,OAExB,MAAMwI,EAAYxI,EAAO,SAAWkI,EAAO,QAAQ,QACnD,GAAIA,EAAO,KAAM,CACf,GAAIiK,GAAa,CAAC3J,GAAaN,EAAO,oBAAqB,MAAO,GAClElI,EAAO,QAAQ,CACb,UAAW,MACjB,CAAK,EAEDA,EAAO,YAAcA,EAAO,UAAU,UACxC,CACA,MAAMiM,EAAYsG,EAAevS,EAAO,UAAY,CAACA,EAAO,UAC5D,SAASwS,EAAUC,EAAK,CACtB,OAAIA,EAAM,EAAU,CAAC,KAAK,MAAM,KAAK,IAAIA,CAAG,CAAC,EACtC,KAAK,MAAMA,CAAG,CACvB,CACA,MAAMjB,EAAsBgB,EAAUvG,CAAS,EACzCyG,EAAqB9J,EAAS,IAAI6J,GAAOD,EAAUC,CAAG,CAAC,EACvDE,EAAazK,EAAO,UAAYA,EAAO,SAAS,QACtD,IAAI0K,EAAWhK,EAAS8J,EAAmB,QAAQlB,CAAmB,EAAI,CAAC,EAC3E,GAAI,OAAOoB,EAAa,MAAgB1K,EAAO,SAAWyK,GAAa,CACrE,IAAIE,EACJjK,EAAS,QAAQ,CAACkC,EAAMG,IAAc,CAChCuG,GAAuB1G,IAEzB+H,EAAgB5H,EAEpB,CAAC,EACG,OAAO4H,EAAkB,MAC3BD,EAAWD,EAAa/J,EAASiK,CAAa,EAAIjK,EAASiK,EAAgB,EAAIA,EAAgB,EAAIA,CAAa,EAEpH,CACA,IAAIC,EAAY,EAShB,GARI,OAAOF,EAAa,MACtBE,EAAYjK,EAAW,QAAQ+J,CAAQ,EACnCE,EAAY,IAAGA,EAAY9S,EAAO,YAAc,GAChDkI,EAAO,gBAAkB,QAAUA,EAAO,iBAAmB,GAAKA,EAAO,qBAC3E4K,EAAYA,EAAY9S,EAAO,qBAAqB,WAAY,EAAI,EAAI,EACxE8S,EAAY,KAAK,IAAIA,EAAW,CAAC,IAGjC5K,EAAO,QAAUlI,EAAO,YAAa,CACvC,MAAM+S,EAAY/S,EAAO,OAAO,SAAWA,EAAO,OAAO,QAAQ,SAAWA,EAAO,QAAUA,EAAO,QAAQ,OAAO,OAAS,EAAIA,EAAO,OAAO,OAAS,EACvJ,OAAOA,EAAO,QAAQ+S,EAAWvH,EAAOgF,EAAcE,CAAQ,CAChE,SAAWxI,EAAO,MAAQlI,EAAO,cAAgB,GAAKkI,EAAO,QAC3D,6BAAsB,IAAM,CAC1BlI,EAAO,QAAQ8S,EAAWtH,EAAOgF,EAAcE,CAAQ,CACzD,CAAC,EACM,GAET,OAAO1Q,EAAO,QAAQ8S,EAAWtH,EAAOgF,EAAcE,CAAQ,CAChE,CAGA,SAASsC,GAAWxH,EAAOgF,EAAcE,EAAU,CAC7CF,IAAiB,SACnBA,EAAe,IAEjB,MAAMxQ,EAAS,KACf,GAAI,CAAAA,EAAO,UACX,OAAI,OAAOwL,EAAU,MACnBA,EAAQxL,EAAO,OAAO,OAEjBA,EAAO,QAAQA,EAAO,YAAawL,EAAOgF,EAAcE,CAAQ,CACzE,CAGA,SAASuC,GAAezH,EAAOgF,EAAcE,EAAUwC,EAAW,CAC5D1C,IAAiB,SACnBA,EAAe,IAEb0C,IAAc,SAChBA,EAAY,IAEd,MAAMlT,EAAS,KACf,GAAIA,EAAO,UAAW,OAClB,OAAOwL,EAAU,MACnBA,EAAQxL,EAAO,OAAO,OAExB,IAAIwH,EAAQxH,EAAO,YACnB,MAAMqP,EAAO,KAAK,IAAIrP,EAAO,OAAO,mBAAoBwH,CAAK,EACvDyD,EAAYoE,EAAO,KAAK,OAAO7H,EAAQ6H,GAAQrP,EAAO,OAAO,cAAc,EAC3EiM,EAAYjM,EAAO,aAAeA,EAAO,UAAY,CAACA,EAAO,UACnE,GAAIiM,GAAajM,EAAO,SAASiL,CAAS,EAAG,CAG3C,MAAMkI,EAAcnT,EAAO,SAASiL,CAAS,EACvCmI,EAAWpT,EAAO,SAASiL,EAAY,CAAC,EAC1CgB,EAAYkH,GAAeC,EAAWD,GAAeD,IACvD1L,GAASxH,EAAO,OAAO,eAE3B,KAAO,CAGL,MAAM4S,EAAW5S,EAAO,SAASiL,EAAY,CAAC,EACxCkI,EAAcnT,EAAO,SAASiL,CAAS,EACzCgB,EAAY2G,IAAaO,EAAcP,GAAYM,IACrD1L,GAASxH,EAAO,OAAO,eAE3B,CACA,OAAAwH,EAAQ,KAAK,IAAIA,EAAO,CAAC,EACzBA,EAAQ,KAAK,IAAIA,EAAOxH,EAAO,WAAW,OAAS,CAAC,EAC7CA,EAAO,QAAQwH,EAAOgE,EAAOgF,EAAcE,CAAQ,CAC5D,CAEA,SAAS2C,IAAsB,CAC7B,MAAMrT,EAAS,KACf,GAAIA,EAAO,UAAW,OACtB,KAAM,CACJ,OAAAkI,EACA,SAAAE,CACJ,EAAMpI,EACEwO,EAAgBtG,EAAO,gBAAkB,OAASlI,EAAO,qBAAoB,EAAKkI,EAAO,cAC/F,IAAIoL,EAAetT,EAAO,sBAAsBA,EAAO,YAAY,EAC/D4O,EACJ,MAAMT,EAAgBnO,EAAO,UAAY,eAAiB,IAAIkI,EAAO,UAAU,GACzEqL,EAASvT,EAAO,MAAQA,EAAO,OAAO,MAAQA,EAAO,OAAO,KAAK,KAAO,EAC9E,GAAIkI,EAAO,KAAM,CACf,GAAIlI,EAAO,UAAW,OACtB4O,EAAY,SAAS5O,EAAO,aAAa,aAAa,yBAAyB,EAAG,EAAE,EAChFkI,EAAO,eACTlI,EAAO,YAAY4O,CAAS,EACnB0E,GAAgBC,GAAUvT,EAAO,OAAO,OAASwO,GAAiB,GAAKxO,EAAO,OAAO,KAAK,KAAO,GAAKA,EAAO,OAAO,OAASwO,IACtIxO,EAAO,QAAO,EACdsT,EAAetT,EAAO,cAAcc,EAAgBsH,EAAU,GAAG+F,CAAa,6BAA6BS,CAAS,IAAI,EAAE,CAAC,CAAC,EAC5HzQ,GAAS,IAAM,CACb6B,EAAO,QAAQsT,CAAY,CAC7B,CAAC,GAEDtT,EAAO,QAAQsT,CAAY,CAE/B,MACEtT,EAAO,QAAQsT,CAAY,CAE/B,CAEA,IAAI5J,GAAQ,CACV,QAAA2H,GACA,YAAAO,GACA,UAAAM,GACA,UAAAI,GACA,WAAAU,GACA,eAAAC,GACA,oBAAAI,EACF,EAEA,SAASG,GAAWC,EAAgBnC,EAAS,CAC3C,MAAMtR,EAAS,KACT,CACJ,OAAAkI,EACA,SAAAE,CACJ,EAAMpI,EACJ,GAAI,CAACkI,EAAO,MAAQlI,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAAS,OACrE,MAAM0T,EAAa,IAAM,CACR5S,EAAgBsH,EAAU,IAAIF,EAAO,UAAU,gBAAgB,EACvE,QAAQ,CAAC3J,EAAIiJ,IAAU,CAC5BjJ,EAAG,aAAa,0BAA2BiJ,CAAK,CAClD,CAAC,CACH,EACMmM,EAAmB,IAAM,CAC7B,MAAMjL,EAAS5H,EAAgBsH,EAAU,IAAIF,EAAO,eAAe,EAAE,EACrEQ,EAAO,QAAQnK,GAAM,CACnBA,EAAG,OAAM,CACX,CAAC,EACGmK,EAAO,OAAS,IAClB1I,EAAO,aAAY,EACnBA,EAAO,aAAY,EAEvB,EACMuJ,EAAcvJ,EAAO,MAAQkI,EAAO,MAAQA,EAAO,KAAK,KAAO,EACjEA,EAAO,qBAAuBA,EAAO,eAAiB,GAAKqB,IAC7DoK,EAAgB,EAElB,MAAMC,EAAiB1L,EAAO,gBAAkBqB,EAAcrB,EAAO,KAAK,KAAO,GAC3E2L,EAAkB7T,EAAO,OAAO,OAAS4T,IAAmB,EAC5DE,EAAiBvK,GAAevJ,EAAO,OAAO,OAASkI,EAAO,KAAK,OAAS,EAC5E6L,EAAiBC,GAAkB,CACvC,QAAS5U,EAAI,EAAGA,EAAI4U,EAAgB5U,GAAK,EAAG,CAC1C,MAAMkK,EAAUtJ,EAAO,UAAY2B,GAAc,eAAgB,CAACuG,EAAO,eAAe,CAAC,EAAIvG,GAAc,MAAO,CAACuG,EAAO,WAAYA,EAAO,eAAe,CAAC,EAC7JlI,EAAO,SAAS,OAAOsJ,CAAO,CAChC,CACF,EACA,GAAIuK,EAAiB,CACnB,GAAI3L,EAAO,mBAAoB,CAC7B,MAAM+L,EAAcL,EAAiB5T,EAAO,OAAO,OAAS4T,EAC5DG,EAAeE,CAAW,EAC1BjU,EAAO,aAAY,EACnBA,EAAO,aAAY,CACrB,MACEyB,GAAY,iLAAiL,EAE/LiS,EAAU,CACZ,SAAWI,EAAgB,CACzB,GAAI5L,EAAO,mBAAoB,CAC7B,MAAM+L,EAAc/L,EAAO,KAAK,KAAOlI,EAAO,OAAO,OAASkI,EAAO,KAAK,KAC1E6L,EAAeE,CAAW,EAC1BjU,EAAO,aAAY,EACnBA,EAAO,aAAY,CACrB,MACEyB,GAAY,4KAA4K,EAE1LiS,EAAU,CACZ,MACEA,EAAU,EAEZ1T,EAAO,QAAQ,CACb,eAAAyT,EACA,UAAWvL,EAAO,eAAiB,OAAY,OAC/C,QAAAoJ,CACJ,CAAG,CACH,CAEA,SAAS4C,GAAQ5Q,EAAO,CACtB,GAAI,CACF,eAAAmQ,EACA,QAAApC,EAAU,GACV,UAAAL,EACA,aAAAjB,EACA,iBAAAR,EACA,QAAA+B,EACA,aAAAtB,EACA,aAAAmE,CACJ,EAAM7Q,IAAU,OAAS,CAAA,EAAKA,EAC5B,MAAMtD,EAAS,KACf,GAAI,CAACA,EAAO,OAAO,KAAM,OACzBA,EAAO,KAAK,eAAe,EAC3B,KAAM,CACJ,OAAA0I,EACA,eAAA0L,EACA,eAAAC,EACA,SAAAjM,EACA,OAAAF,CACJ,EAAMlI,EACE,CACJ,eAAAgS,EACA,aAAAsC,CACJ,EAAMpM,EAGJ,GAFAlI,EAAO,eAAiB,GACxBA,EAAO,eAAiB,GACpBA,EAAO,SAAWkI,EAAO,QAAQ,QAAS,CACxCmJ,IACE,CAACnJ,EAAO,gBAAkBlI,EAAO,YAAc,EACjDA,EAAO,QAAQA,EAAO,QAAQ,OAAO,OAAQ,EAAG,GAAO,EAAI,EAClDkI,EAAO,gBAAkBlI,EAAO,UAAYkI,EAAO,cAC5DlI,EAAO,QAAQA,EAAO,QAAQ,OAAO,OAASA,EAAO,UAAW,EAAG,GAAO,EAAI,EACrEA,EAAO,YAAcA,EAAO,SAAS,OAAS,GACvDA,EAAO,QAAQA,EAAO,QAAQ,aAAc,EAAG,GAAO,EAAI,GAG9DA,EAAO,eAAiBoU,EACxBpU,EAAO,eAAiBqU,EACxBrU,EAAO,KAAK,SAAS,EACrB,MACF,CACA,IAAIwO,EAAgBtG,EAAO,cACvBsG,IAAkB,OACpBA,EAAgBxO,EAAO,qBAAoB,GAE3CwO,EAAgB,KAAK,KAAK,WAAWtG,EAAO,cAAe,EAAE,CAAC,EAC1D8J,GAAkBxD,EAAgB,IAAM,IAC1CA,EAAgBA,EAAgB,IAGpC,MAAMoF,EAAiB1L,EAAO,mBAAqBsG,EAAgBtG,EAAO,eAC1E,IAAIqM,EAAevC,EAAiB,KAAK,IAAI4B,EAAgB,KAAK,KAAKpF,EAAgB,CAAC,CAAC,EAAIoF,EACzFW,EAAeX,IAAmB,IACpCW,GAAgBX,EAAiBW,EAAeX,GAElDW,GAAgBrM,EAAO,qBACvBlI,EAAO,aAAeuU,EACtB,MAAMhL,EAAcvJ,EAAO,MAAQkI,EAAO,MAAQA,EAAO,KAAK,KAAO,EACjEQ,EAAO,OAAS8F,EAAgB+F,GAAgBvU,EAAO,OAAO,SAAW,SAAW0I,EAAO,OAAS8F,EAAgB+F,EAAe,EACrI9S,GAAY,0OAA0O,EAC7O8H,GAAerB,EAAO,KAAK,OAAS,OAC7CzG,GAAY,yEAAyE,EAEvF,MAAM+S,EAAuB,CAAA,EACvBC,EAAsB,CAAA,EACtB1C,EAAOxI,EAAc,KAAK,KAAKb,EAAO,OAASR,EAAO,KAAK,IAAI,EAAIQ,EAAO,OAC1EgM,EAAoBpD,GAAWS,EAAOuC,EAAe9F,GAAiB,CAACwD,EAC7E,IAAIpE,EAAc8G,EAAoBJ,EAAetU,EAAO,YACxD,OAAOuP,EAAqB,IAC9BA,EAAmBvP,EAAO,cAAc0I,EAAO,KAAKnK,GAAMA,EAAG,UAAU,SAAS2J,EAAO,gBAAgB,CAAC,CAAC,EAEzG0F,EAAc2B,EAEhB,MAAMoF,EAAS3D,IAAc,QAAU,CAACA,EAClC4D,EAAS5D,IAAc,QAAU,CAACA,EACxC,IAAI6D,EAAkB,EAClBC,EAAiB,EAErB,MAAMC,GADiBxL,EAAcb,EAAO6G,CAAgB,EAAE,OAASA,IACrByC,GAAkB,OAAOjC,EAAiB,IAAc,CAACvB,EAAgB,EAAI,GAAM,GAErI,GAAIuG,EAA0BR,EAAc,CAC1CM,EAAkB,KAAK,IAAIN,EAAeQ,EAAyBnB,CAAc,EACjF,QAASxU,EAAI,EAAGA,EAAImV,EAAeQ,EAAyB3V,GAAK,EAAG,CAClE,MAAMoI,EAAQpI,EAAI,KAAK,MAAMA,EAAI2S,CAAI,EAAIA,EACzC,GAAIxI,EAAa,CACf,MAAMyL,EAAoBjD,EAAOvK,EAAQ,EACzC,QAASpI,EAAIsJ,EAAO,OAAS,EAAGtJ,GAAK,EAAGA,GAAK,EACvCsJ,EAAOtJ,CAAC,EAAE,SAAW4V,GAAmBR,EAAqB,KAAKpV,CAAC,CAK3E,MACEoV,EAAqB,KAAKzC,EAAOvK,EAAQ,CAAC,CAE9C,CACF,SAAWuN,EAA0BvG,EAAgBuD,EAAOwC,EAAc,CACxEO,EAAiB,KAAK,IAAIC,GAA2BhD,EAAOwC,EAAe,GAAIX,CAAc,EACzFc,IACFI,EAAiB,KAAK,IAAIA,EAAgBtG,EAAgBuD,EAAOuC,EAAe,CAAC,GAEnF,QAASlV,EAAI,EAAGA,EAAI0V,EAAgB1V,GAAK,EAAG,CAC1C,MAAMoI,EAAQpI,EAAI,KAAK,MAAMA,EAAI2S,CAAI,EAAIA,EACrCxI,EACFb,EAAO,QAAQ,CAACgB,EAAOgB,IAAe,CAChChB,EAAM,SAAWlC,GAAOiN,EAAoB,KAAK/J,CAAU,CACjE,CAAC,EAED+J,EAAoB,KAAKjN,CAAK,CAElC,CACF,CAsCA,GArCAxH,EAAO,oBAAsB,GAC7B,sBAAsB,IAAM,CAC1BA,EAAO,oBAAsB,EAC/B,CAAC,EACGA,EAAO,OAAO,SAAW,SAAW0I,EAAO,OAAS8F,EAAgB+F,EAAe,IACjFE,EAAoB,SAASlF,CAAgB,GAC/CkF,EAAoB,OAAOA,EAAoB,QAAQlF,CAAgB,EAAG,CAAC,EAEzEiF,EAAqB,SAASjF,CAAgB,GAChDiF,EAAqB,OAAOA,EAAqB,QAAQjF,CAAgB,EAAG,CAAC,GAG7EqF,GACFJ,EAAqB,QAAQhN,GAAS,CACpCkB,EAAOlB,CAAK,EAAE,kBAAoB,GAClCY,EAAS,QAAQM,EAAOlB,CAAK,CAAC,EAC9BkB,EAAOlB,CAAK,EAAE,kBAAoB,EACpC,CAAC,EAECmN,GACFF,EAAoB,QAAQjN,GAAS,CACnCkB,EAAOlB,CAAK,EAAE,kBAAoB,GAClCY,EAAS,OAAOM,EAAOlB,CAAK,CAAC,EAC7BkB,EAAOlB,CAAK,EAAE,kBAAoB,EACpC,CAAC,EAEHxH,EAAO,aAAY,EACfkI,EAAO,gBAAkB,OAC3BlI,EAAO,aAAY,EACVuJ,IAAgBiL,EAAqB,OAAS,GAAKI,GAAUH,EAAoB,OAAS,GAAKE,IACxG3U,EAAO,OAAO,QAAQ,CAAC0J,EAAOgB,IAAe,CAC3C1K,EAAO,KAAK,YAAY0K,EAAYhB,EAAO1J,EAAO,MAAM,CAC1D,CAAC,EAECkI,EAAO,qBACTlI,EAAO,mBAAkB,EAEvBqR,GACF,GAAImD,EAAqB,OAAS,GAAKI,GACrC,GAAI,OAAOnB,EAAmB,IAAa,CACzC,MAAMwB,EAAwBjV,EAAO,WAAW4N,CAAW,EAErDsH,EADoBlV,EAAO,WAAW4N,EAAciH,CAAe,EACxCI,EAC7Bd,EACFnU,EAAO,aAAaA,EAAO,UAAYkV,CAAI,GAE3ClV,EAAO,QAAQ4N,EAAc,KAAK,KAAKiH,CAAe,EAAG,EAAG,GAAO,EAAI,EACnE9E,IACF/P,EAAO,gBAAgB,eAAiBA,EAAO,gBAAgB,eAAiBkV,EAChFlV,EAAO,gBAAgB,iBAAmBA,EAAO,gBAAgB,iBAAmBkV,GAG1F,SACMnF,EAAc,CAChB,MAAMoF,EAAQ5L,EAAciL,EAAqB,OAAStM,EAAO,KAAK,KAAOsM,EAAqB,OAClGxU,EAAO,QAAQA,EAAO,YAAcmV,EAAO,EAAG,GAAO,EAAI,EACzDnV,EAAO,gBAAgB,iBAAmBA,EAAO,SACnD,UAEOyU,EAAoB,OAAS,GAAKE,EAC3C,GAAI,OAAOlB,EAAmB,IAAa,CACzC,MAAMwB,EAAwBjV,EAAO,WAAW4N,CAAW,EAErDsH,EADoBlV,EAAO,WAAW4N,EAAckH,CAAc,EACvCG,EAC7Bd,EACFnU,EAAO,aAAaA,EAAO,UAAYkV,CAAI,GAE3ClV,EAAO,QAAQ4N,EAAckH,EAAgB,EAAG,GAAO,EAAI,EACvD/E,IACF/P,EAAO,gBAAgB,eAAiBA,EAAO,gBAAgB,eAAiBkV,EAChFlV,EAAO,gBAAgB,iBAAmBA,EAAO,gBAAgB,iBAAmBkV,GAG1F,KAAO,CACL,MAAMC,EAAQ5L,EAAckL,EAAoB,OAASvM,EAAO,KAAK,KAAOuM,EAAoB,OAChGzU,EAAO,QAAQA,EAAO,YAAcmV,EAAO,EAAG,GAAO,EAAI,CAC3D,EAKJ,GAFAnV,EAAO,eAAiBoU,EACxBpU,EAAO,eAAiBqU,EACpBrU,EAAO,YAAcA,EAAO,WAAW,SAAW,CAACgQ,EAAc,CACnE,MAAMoF,EAAa,CACjB,eAAA3B,EACA,UAAAzC,EACA,aAAAjB,EACA,iBAAAR,EACA,aAAc,EACpB,EACQ,MAAM,QAAQvP,EAAO,WAAW,OAAO,EACzCA,EAAO,WAAW,QAAQ,QAAQhC,GAAK,CACjC,CAACA,EAAE,WAAaA,EAAE,OAAO,MAAMA,EAAE,QAAQ,CAC3C,GAAGoX,EACH,QAASpX,EAAE,OAAO,gBAAkBkK,EAAO,cAAgBmJ,EAAU,EAC/E,CAAS,CACH,CAAC,EACQrR,EAAO,WAAW,mBAAmBA,EAAO,aAAeA,EAAO,WAAW,QAAQ,OAAO,MACrGA,EAAO,WAAW,QAAQ,QAAQ,CAChC,GAAGoV,EACH,QAASpV,EAAO,WAAW,QAAQ,OAAO,gBAAkBkI,EAAO,cAAgBmJ,EAAU,EACrG,CAAO,CAEL,CACArR,EAAO,KAAK,SAAS,CACvB,CAEA,SAASqV,IAAc,CACrB,MAAMrV,EAAS,KACT,CACJ,OAAAkI,EACA,SAAAE,CACJ,EAAMpI,EACJ,GAAI,CAACkI,EAAO,MAAQ,CAACE,GAAYpI,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAAS,OAClFA,EAAO,aAAY,EACnB,MAAMsV,EAAiB,CAAA,EACvBtV,EAAO,OAAO,QAAQsJ,GAAW,CAC/B,MAAM9B,EAAQ,OAAO8B,EAAQ,iBAAqB,IAAcA,EAAQ,aAAa,yBAAyB,EAAI,EAAIA,EAAQ,iBAC9HgM,EAAe9N,CAAK,EAAI8B,CAC1B,CAAC,EACDtJ,EAAO,OAAO,QAAQsJ,GAAW,CAC/BA,EAAQ,gBAAgB,yBAAyB,CACnD,CAAC,EACDgM,EAAe,QAAQhM,GAAW,CAChClB,EAAS,OAAOkB,CAAO,CACzB,CAAC,EACDtJ,EAAO,aAAY,EACnBA,EAAO,QAAQA,EAAO,UAAW,CAAC,CACpC,CAEA,IAAIuV,GAAO,CACT,WAAA/B,GACA,QAAAU,GACA,YAAAmB,EACF,EAEA,SAASG,GAAcC,EAAQ,CAC7B,MAAMzV,EAAS,KACf,GAAI,CAACA,EAAO,OAAO,eAAiBA,EAAO,OAAO,eAAiBA,EAAO,UAAYA,EAAO,OAAO,QAAS,OAC7G,MAAMzB,EAAKyB,EAAO,OAAO,oBAAsB,YAAcA,EAAO,GAAKA,EAAO,UAC5EA,EAAO,YACTA,EAAO,oBAAsB,IAE/BzB,EAAG,MAAM,OAAS,OAClBA,EAAG,MAAM,OAASkX,EAAS,WAAa,OACpCzV,EAAO,WACT,sBAAsB,IAAM,CAC1BA,EAAO,oBAAsB,EAC/B,CAAC,CAEL,CAEA,SAAS0V,IAAkB,CACzB,MAAM1V,EAAS,KACXA,EAAO,OAAO,eAAiBA,EAAO,UAAYA,EAAO,OAAO,UAGhEA,EAAO,YACTA,EAAO,oBAAsB,IAE/BA,EAAOA,EAAO,OAAO,oBAAsB,YAAc,KAAO,WAAW,EAAE,MAAM,OAAS,GACxFA,EAAO,WACT,sBAAsB,IAAM,CAC1BA,EAAO,oBAAsB,EAC/B,CAAC,EAEL,CAEA,IAAI2V,GAAa,CACf,cAAAH,GACA,gBAAAE,EACF,EAGA,SAASE,GAAe5U,EAAU6U,EAAM,CAClCA,IAAS,SACXA,EAAO,MAET,SAASC,EAAcvX,EAAI,CACzB,GAAI,CAACA,GAAMA,IAAOhB,EAAW,GAAMgB,IAAOX,EAAS,EAAI,OAAO,KAC1DW,EAAG,eAAcA,EAAKA,EAAG,cAC7B,MAAMwX,EAAQxX,EAAG,QAAQyC,CAAQ,EACjC,MAAI,CAAC+U,GAAS,CAACxX,EAAG,YACT,KAEFwX,GAASD,EAAcvX,EAAG,YAAW,EAAG,IAAI,CACrD,CACA,OAAOuX,EAAcD,CAAI,CAC3B,CACA,SAASG,GAAiBhW,EAAQmH,EAAO8O,EAAQ,CAC/C,MAAMzX,EAASZ,EAAS,EAClB,CACJ,OAAAsK,CACJ,EAAMlI,EACEkW,EAAqBhO,EAAO,mBAC5BiO,EAAqBjO,EAAO,mBAClC,OAAIgO,IAAuBD,GAAUE,GAAsBF,GAAUzX,EAAO,WAAa2X,GACnFD,IAAuB,WACzB/O,EAAM,eAAc,EACb,IAEF,GAEF,EACT,CACA,SAASiP,GAAajP,EAAO,CAC3B,MAAMnH,EAAS,KACTkD,EAAW3F,EAAW,EAC5B,IAAIsT,EAAI1J,EACJ0J,EAAE,gBAAeA,EAAIA,EAAE,eAC3B,MAAMnJ,EAAO1H,EAAO,gBACpB,GAAI6Q,EAAE,OAAS,cAAe,CAC5B,GAAInJ,EAAK,YAAc,MAAQA,EAAK,YAAcmJ,EAAE,UAClD,OAEFnJ,EAAK,UAAYmJ,EAAE,SACrB,MAAWA,EAAE,OAAS,cAAgBA,EAAE,cAAc,SAAW,IAC/DnJ,EAAK,QAAUmJ,EAAE,cAAc,CAAC,EAAE,YAEpC,GAAIA,EAAE,OAAS,aAAc,CAE3BmF,GAAiBhW,EAAQ6Q,EAAGA,EAAE,cAAc,CAAC,EAAE,KAAK,EACpD,MACF,CACA,KAAM,CACJ,OAAA3I,EACA,QAAAmO,EACA,QAAA9E,CACJ,EAAMvR,EAGJ,GAFI,CAACuR,GACD,CAACrJ,EAAO,eAAiB2I,EAAE,cAAgB,SAC3C7Q,EAAO,WAAakI,EAAO,+BAC7B,OAEE,CAAClI,EAAO,WAAakI,EAAO,SAAWA,EAAO,MAChDlI,EAAO,QAAO,EAEhB,IAAIsW,EAAWzF,EAAE,OAMjB,GALI3I,EAAO,oBAAsB,WAC3B,CAAC5G,GAAiBgV,EAAUtW,EAAO,SAAS,GAE9C,UAAW6Q,GAAKA,EAAE,QAAU,GAC5B,WAAYA,GAAKA,EAAE,OAAS,GAC5BnJ,EAAK,WAAaA,EAAK,QAAS,OAGpC,MAAM6O,EAAuB,CAAC,CAACrO,EAAO,gBAAkBA,EAAO,iBAAmB,GAE5EsO,EAAY3F,EAAE,aAAeA,EAAE,aAAY,EAAKA,EAAE,KACpD0F,GAAwB1F,EAAE,QAAUA,EAAE,OAAO,YAAc2F,IAC7DF,EAAWE,EAAU,CAAC,GAExB,MAAMC,EAAoBvO,EAAO,kBAAoBA,EAAO,kBAAoB,IAAIA,EAAO,cAAc,GACnGwO,EAAiB,CAAC,EAAE7F,EAAE,QAAUA,EAAE,OAAO,YAG/C,GAAI3I,EAAO,YAAcwO,EAAiBd,GAAea,EAAmBH,CAAQ,EAAIA,EAAS,QAAQG,CAAiB,GAAI,CAC5HzW,EAAO,WAAa,GACpB,MACF,CACA,GAAIkI,EAAO,cACL,CAACoO,EAAS,QAAQpO,EAAO,YAAY,EAAG,OAE9CmO,EAAQ,SAAWxF,EAAE,MACrBwF,EAAQ,SAAWxF,EAAE,MACrB,MAAMoF,EAASI,EAAQ,SACjBM,EAASN,EAAQ,SAIvB,GAAI,CAACL,GAAiBhW,EAAQ6Q,EAAGoF,CAAM,EACrC,OAEF,OAAO,OAAOvO,EAAM,CAClB,UAAW,GACX,QAAS,GACT,oBAAqB,GACrB,YAAa,OACb,YAAa,MACjB,CAAG,EACD2O,EAAQ,OAASJ,EACjBI,EAAQ,OAASM,EACjBjP,EAAK,eAAiBrJ,EAAG,EACzB2B,EAAO,WAAa,GACpBA,EAAO,WAAU,EACjBA,EAAO,eAAiB,OACpBkI,EAAO,UAAY,IAAGR,EAAK,mBAAqB,IACpD,IAAIkP,EAAiB,GACjBN,EAAS,QAAQ5O,EAAK,iBAAiB,IACzCkP,EAAiB,GACbN,EAAS,WAAa,WACxB5O,EAAK,UAAY,KAGjBxE,EAAS,eAAiBA,EAAS,cAAc,QAAQwE,EAAK,iBAAiB,GAAKxE,EAAS,gBAAkBoT,IAAazF,EAAE,cAAgB,SAAWA,EAAE,cAAgB,SAAW,CAACyF,EAAS,QAAQ5O,EAAK,iBAAiB,IAChOxE,EAAS,cAAc,KAAI,EAE7B,MAAM2T,EAAuBD,GAAkB5W,EAAO,gBAAkBkI,EAAO,0BAC1EA,EAAO,+BAAiC2O,IAAyB,CAACP,EAAS,mBAC9EzF,EAAE,eAAc,EAEd3I,EAAO,UAAYA,EAAO,SAAS,SAAWlI,EAAO,UAAYA,EAAO,WAAa,CAACkI,EAAO,SAC/FlI,EAAO,SAAS,aAAY,EAE9BA,EAAO,KAAK,aAAc6Q,CAAC,CAC7B,CAEA,SAASiG,GAAY3P,EAAO,CAC1B,MAAMjE,EAAW3F,EAAW,EACtByC,EAAS,KACT0H,EAAO1H,EAAO,gBACd,CACJ,OAAAkI,EACA,QAAAmO,EACA,aAAc/N,EACd,QAAAiJ,CACJ,EAAMvR,EAEJ,GADI,CAACuR,GACD,CAACrJ,EAAO,eAAiBf,EAAM,cAAgB,QAAS,OAC5D,IAAI0J,EAAI1J,EAER,GADI0J,EAAE,gBAAeA,EAAIA,EAAE,eACvBA,EAAE,OAAS,gBACTnJ,EAAK,UAAY,MACVmJ,EAAE,YACFnJ,EAAK,WAAW,OAE7B,IAAIqP,EACJ,GAAIlG,EAAE,OAAS,aAEb,GADAkG,EAAc,CAAC,GAAGlG,EAAE,cAAc,EAAE,KAAKc,GAAKA,EAAE,aAAejK,EAAK,OAAO,EACvE,CAACqP,GAAeA,EAAY,aAAerP,EAAK,QAAS,YAE7DqP,EAAclG,EAEhB,GAAI,CAACnJ,EAAK,UAAW,CACfA,EAAK,aAAeA,EAAK,aAC3B1H,EAAO,KAAK,oBAAqB6Q,CAAC,EAEpC,MACF,CACA,MAAMmG,EAAQD,EAAY,MACpBE,EAAQF,EAAY,MAC1B,GAAIlG,EAAE,wBAAyB,CAC7BwF,EAAQ,OAASW,EACjBX,EAAQ,OAASY,EACjB,MACF,CACA,GAAI,CAACjX,EAAO,eAAgB,CACrB6Q,EAAE,OAAO,QAAQnJ,EAAK,iBAAiB,IAC1C1H,EAAO,WAAa,IAElB0H,EAAK,YACP,OAAO,OAAO2O,EAAS,CACrB,OAAQW,EACR,OAAQC,EACR,SAAUD,EACV,SAAUC,CAClB,CAAO,EACDvP,EAAK,eAAiBrJ,EAAG,GAE3B,MACF,CACA,GAAI6J,EAAO,qBAAuB,CAACA,EAAO,KACxC,GAAIlI,EAAO,cAET,GAAIiX,EAAQZ,EAAQ,QAAUrW,EAAO,WAAaA,EAAO,aAAY,GAAMiX,EAAQZ,EAAQ,QAAUrW,EAAO,WAAaA,EAAO,eAAgB,CAC9I0H,EAAK,UAAY,GACjBA,EAAK,QAAU,GACf,MACF,MACK,IAAIY,IAAQ0O,EAAQX,EAAQ,QAAU,CAACrW,EAAO,WAAaA,EAAO,aAAY,GAAMgX,EAAQX,EAAQ,QAAU,CAACrW,EAAO,WAAaA,EAAO,aAAY,GAC3J,OACK,GAAI,CAACsI,IAAQ0O,EAAQX,EAAQ,QAAUrW,EAAO,WAAaA,EAAO,aAAY,GAAMgX,EAAQX,EAAQ,QAAUrW,EAAO,WAAaA,EAAO,aAAY,GAC1J,OAMJ,GAHIkD,EAAS,eAAiBA,EAAS,cAAc,QAAQwE,EAAK,iBAAiB,GAAKxE,EAAS,gBAAkB2N,EAAE,QAAUA,EAAE,cAAgB,SAC/I3N,EAAS,cAAc,KAAI,EAEzBA,EAAS,eACP2N,EAAE,SAAW3N,EAAS,eAAiB2N,EAAE,OAAO,QAAQnJ,EAAK,iBAAiB,EAAG,CACnFA,EAAK,QAAU,GACf1H,EAAO,WAAa,GACpB,MACF,CAEE0H,EAAK,qBACP1H,EAAO,KAAK,YAAa6Q,CAAC,EAE5BwF,EAAQ,UAAYA,EAAQ,SAC5BA,EAAQ,UAAYA,EAAQ,SAC5BA,EAAQ,SAAWW,EACnBX,EAAQ,SAAWY,EACnB,MAAMC,EAAQb,EAAQ,SAAWA,EAAQ,OACnCc,EAAQd,EAAQ,SAAWA,EAAQ,OACzC,GAAIrW,EAAO,OAAO,WAAa,KAAK,KAAKkX,GAAS,EAAIC,GAAS,CAAC,EAAInX,EAAO,OAAO,UAAW,OAC7F,GAAI,OAAO0H,EAAK,YAAgB,IAAa,CAC3C,IAAI0P,EACApX,EAAO,aAAY,GAAMqW,EAAQ,WAAaA,EAAQ,QAAUrW,EAAO,WAAU,GAAMqW,EAAQ,WAAaA,EAAQ,OACtH3O,EAAK,YAAc,GAGfwP,EAAQA,EAAQC,EAAQA,GAAS,KACnCC,EAAa,KAAK,MAAM,KAAK,IAAID,CAAK,EAAG,KAAK,IAAID,CAAK,CAAC,EAAI,IAAM,KAAK,GACvExP,EAAK,YAAc1H,EAAO,eAAiBoX,EAAalP,EAAO,WAAa,GAAKkP,EAAalP,EAAO,WAG3G,CASA,GARIR,EAAK,aACP1H,EAAO,KAAK,oBAAqB6Q,CAAC,EAEhC,OAAOnJ,EAAK,YAAgB,MAC1B2O,EAAQ,WAAaA,EAAQ,QAAUA,EAAQ,WAAaA,EAAQ,UACtE3O,EAAK,YAAc,IAGnBA,EAAK,aAAemJ,EAAE,OAAS,aAAenJ,EAAK,gCAAiC,CACtFA,EAAK,UAAY,GACjB,MACF,CACA,GAAI,CAACA,EAAK,YACR,OAEF1H,EAAO,WAAa,GAChB,CAACkI,EAAO,SAAW2I,EAAE,YACvBA,EAAE,eAAc,EAEd3I,EAAO,0BAA4B,CAACA,EAAO,QAC7C2I,EAAE,gBAAe,EAEnB,IAAIqE,EAAOlV,EAAO,aAAY,EAAKkX,EAAQC,EACvCE,EAAcrX,EAAO,aAAY,EAAKqW,EAAQ,SAAWA,EAAQ,UAAYA,EAAQ,SAAWA,EAAQ,UACxGnO,EAAO,iBACTgN,EAAO,KAAK,IAAIA,CAAI,GAAK5M,EAAM,EAAI,IACnC+O,EAAc,KAAK,IAAIA,CAAW,GAAK/O,EAAM,EAAI,KAEnD+N,EAAQ,KAAOnB,EACfA,GAAQhN,EAAO,WACXI,IACF4M,EAAO,CAACA,EACRmC,EAAc,CAACA,GAEjB,MAAMC,EAAuBtX,EAAO,iBACpCA,EAAO,eAAiBkV,EAAO,EAAI,OAAS,OAC5ClV,EAAO,iBAAmBqX,EAAc,EAAI,OAAS,OACrD,MAAME,EAASvX,EAAO,OAAO,MAAQ,CAACkI,EAAO,QACvCsP,EAAexX,EAAO,mBAAqB,QAAUA,EAAO,gBAAkBA,EAAO,mBAAqB,QAAUA,EAAO,eACjI,GAAI,CAAC0H,EAAK,QAAS,CAQjB,GAPI6P,GAAUC,GACZxX,EAAO,QAAQ,CACb,UAAWA,EAAO,cAC1B,CAAO,EAEH0H,EAAK,eAAiB1H,EAAO,aAAY,EACzCA,EAAO,cAAc,CAAC,EAClBA,EAAO,UAAW,CACpB,MAAMyX,EAAM,IAAI,OAAO,YAAY,gBAAiB,CAClD,QAAS,GACT,WAAY,GACZ,OAAQ,CACN,kBAAmB,EAC7B,CACA,CAAO,EACDzX,EAAO,UAAU,cAAcyX,CAAG,CACpC,CACA/P,EAAK,oBAAsB,GAEvBQ,EAAO,aAAelI,EAAO,iBAAmB,IAAQA,EAAO,iBAAmB,KACpFA,EAAO,cAAc,EAAI,EAE3BA,EAAO,KAAK,kBAAmB6Q,CAAC,CAClC,CAGA,GADA,IAAI,KAAI,EAAG,QAAO,EACd3I,EAAO,iBAAmB,IAASR,EAAK,SAAWA,EAAK,oBAAsB4P,IAAyBtX,EAAO,kBAAoBuX,GAAUC,GAAgB,KAAK,IAAItC,CAAI,GAAK,EAAG,CACnL,OAAO,OAAOmB,EAAS,CACrB,OAAQW,EACR,OAAQC,EACR,SAAUD,EACV,SAAUC,EACV,eAAgBvP,EAAK,gBAC3B,CAAK,EACDA,EAAK,cAAgB,GACrBA,EAAK,eAAiBA,EAAK,iBAC3B,MACF,CACA1H,EAAO,KAAK,aAAc6Q,CAAC,EAC3BnJ,EAAK,QAAU,GACfA,EAAK,iBAAmBwN,EAAOxN,EAAK,eACpC,IAAIgQ,EAAsB,GACtBC,EAAkBzP,EAAO,gBAiD7B,GAhDIA,EAAO,sBACTyP,EAAkB,GAEhBzC,EAAO,GACLqC,GAAUC,GAA8B9P,EAAK,oBAAsBA,EAAK,kBAAoBQ,EAAO,eAAiBlI,EAAO,eAAiBA,EAAO,gBAAgBA,EAAO,YAAc,CAAC,GAAKkI,EAAO,gBAAkB,QAAUlI,EAAO,OAAO,OAASkI,EAAO,eAAiB,EAAIlI,EAAO,gBAAgBA,EAAO,YAAc,CAAC,EAAIA,EAAO,OAAO,aAAe,GAAKA,EAAO,OAAO,aAAeA,EAAO,aAAY,IACzZA,EAAO,QAAQ,CACb,UAAW,OACX,aAAc,GACd,iBAAkB,CAC1B,CAAO,EAEC0H,EAAK,iBAAmB1H,EAAO,aAAY,IAC7C0X,EAAsB,GAClBxP,EAAO,aACTR,EAAK,iBAAmB1H,EAAO,aAAY,EAAK,GAAK,CAACA,EAAO,aAAY,EAAK0H,EAAK,eAAiBwN,IAASyC,KAGxGzC,EAAO,IACZqC,GAAUC,GAA8B9P,EAAK,oBAAsBA,EAAK,kBAAoBQ,EAAO,eAAiBlI,EAAO,aAAY,EAAKA,EAAO,gBAAgBA,EAAO,gBAAgB,OAAS,CAAC,EAAIA,EAAO,OAAO,cAAgBkI,EAAO,gBAAkB,QAAUlI,EAAO,OAAO,OAASkI,EAAO,eAAiB,EAAIlI,EAAO,gBAAgBA,EAAO,gBAAgB,OAAS,CAAC,EAAIA,EAAO,OAAO,aAAe,GAAKA,EAAO,aAAY,IAC/aA,EAAO,QAAQ,CACb,UAAW,OACX,aAAc,GACd,iBAAkBA,EAAO,OAAO,QAAUkI,EAAO,gBAAkB,OAASlI,EAAO,qBAAoB,EAAK,KAAK,KAAK,WAAWkI,EAAO,cAAe,EAAE,CAAC,EAClK,CAAO,EAECR,EAAK,iBAAmB1H,EAAO,aAAY,IAC7C0X,EAAsB,GAClBxP,EAAO,aACTR,EAAK,iBAAmB1H,EAAO,aAAY,EAAK,GAAKA,EAAO,aAAY,EAAK0H,EAAK,eAAiBwN,IAASyC,KAI9GD,IACF7G,EAAE,wBAA0B,IAI1B,CAAC7Q,EAAO,gBAAkBA,EAAO,iBAAmB,QAAU0H,EAAK,iBAAmBA,EAAK,iBAC7FA,EAAK,iBAAmBA,EAAK,gBAE3B,CAAC1H,EAAO,gBAAkBA,EAAO,iBAAmB,QAAU0H,EAAK,iBAAmBA,EAAK,iBAC7FA,EAAK,iBAAmBA,EAAK,gBAE3B,CAAC1H,EAAO,gBAAkB,CAACA,EAAO,iBACpC0H,EAAK,iBAAmBA,EAAK,gBAI3BQ,EAAO,UAAY,EACrB,GAAI,KAAK,IAAIgN,CAAI,EAAIhN,EAAO,WAAaR,EAAK,oBAC5C,GAAI,CAACA,EAAK,mBAAoB,CAC5BA,EAAK,mBAAqB,GAC1B2O,EAAQ,OAASA,EAAQ,SACzBA,EAAQ,OAASA,EAAQ,SACzB3O,EAAK,iBAAmBA,EAAK,eAC7B2O,EAAQ,KAAOrW,EAAO,aAAY,EAAKqW,EAAQ,SAAWA,EAAQ,OAASA,EAAQ,SAAWA,EAAQ,OACtG,MACF,MACK,CACL3O,EAAK,iBAAmBA,EAAK,eAC7B,MACF,CAEE,CAACQ,EAAO,cAAgBA,EAAO,WAG/BA,EAAO,UAAYA,EAAO,SAAS,SAAWlI,EAAO,UAAYkI,EAAO,uBAC1ElI,EAAO,kBAAiB,EACxBA,EAAO,oBAAmB,GAExBkI,EAAO,UAAYA,EAAO,SAAS,SAAWlI,EAAO,UACvDA,EAAO,SAAS,YAAW,EAG7BA,EAAO,eAAe0H,EAAK,gBAAgB,EAE3C1H,EAAO,aAAa0H,EAAK,gBAAgB,EAC3C,CAEA,SAASkQ,GAAWzQ,EAAO,CACzB,MAAMnH,EAAS,KACT0H,EAAO1H,EAAO,gBACpB,IAAI6Q,EAAI1J,EACJ0J,EAAE,gBAAeA,EAAIA,EAAE,eAC3B,IAAIkG,EAEJ,GADqBlG,EAAE,OAAS,YAAcA,EAAE,OAAS,eAOvD,GADAkG,EAAc,CAAC,GAAGlG,EAAE,cAAc,EAAE,KAAKc,GAAKA,EAAE,aAAejK,EAAK,OAAO,EACvE,CAACqP,GAAeA,EAAY,aAAerP,EAAK,QAAS,WAN5C,CAEjB,GADIA,EAAK,UAAY,MACjBmJ,EAAE,YAAcnJ,EAAK,UAAW,OACpCqP,EAAclG,CAChB,CAIA,GAAI,CAAC,gBAAiB,aAAc,eAAgB,aAAa,EAAE,SAASA,EAAE,IAAI,GAE5E,EADY,CAAC,gBAAiB,aAAa,EAAE,SAASA,EAAE,IAAI,IAAM7Q,EAAO,QAAQ,UAAYA,EAAO,QAAQ,YAE9G,OAGJ0H,EAAK,UAAY,KACjBA,EAAK,QAAU,KACf,KAAM,CACJ,OAAAQ,EACA,QAAAmO,EACA,aAAc/N,EACd,WAAAO,EACA,QAAA0I,CACJ,EAAMvR,EAEJ,GADI,CAACuR,GACD,CAACrJ,EAAO,eAAiB2I,EAAE,cAAgB,QAAS,OAKxD,GAJInJ,EAAK,qBACP1H,EAAO,KAAK,WAAY6Q,CAAC,EAE3BnJ,EAAK,oBAAsB,GACvB,CAACA,EAAK,UAAW,CACfA,EAAK,SAAWQ,EAAO,YACzBlI,EAAO,cAAc,EAAK,EAE5B0H,EAAK,QAAU,GACfA,EAAK,YAAc,GACnB,MACF,CAGIQ,EAAO,YAAcR,EAAK,SAAWA,EAAK,YAAc1H,EAAO,iBAAmB,IAAQA,EAAO,iBAAmB,KACtHA,EAAO,cAAc,EAAK,EAI5B,MAAM6X,EAAexZ,EAAG,EAClByZ,EAAWD,EAAenQ,EAAK,eAGrC,GAAI1H,EAAO,WAAY,CACrB,MAAM+X,EAAWlH,EAAE,MAAQA,EAAE,cAAgBA,EAAE,aAAY,EAC3D7Q,EAAO,mBAAmB+X,GAAYA,EAAS,CAAC,GAAKlH,EAAE,OAAQkH,CAAQ,EACvE/X,EAAO,KAAK,YAAa6Q,CAAC,EACtBiH,EAAW,KAAOD,EAAenQ,EAAK,cAAgB,KACxD1H,EAAO,KAAK,wBAAyB6Q,CAAC,CAE1C,CAKA,GAJAnJ,EAAK,cAAgBrJ,EAAG,EACxBF,GAAS,IAAM,CACR6B,EAAO,YAAWA,EAAO,WAAa,GAC7C,CAAC,EACG,CAAC0H,EAAK,WAAa,CAACA,EAAK,SAAW,CAAC1H,EAAO,gBAAkBqW,EAAQ,OAAS,GAAK,CAAC3O,EAAK,eAAiBA,EAAK,mBAAqBA,EAAK,gBAAkB,CAACA,EAAK,cAAe,CACnLA,EAAK,UAAY,GACjBA,EAAK,QAAU,GACfA,EAAK,YAAc,GACnB,MACF,CACAA,EAAK,UAAY,GACjBA,EAAK,QAAU,GACfA,EAAK,YAAc,GACnB,IAAIsQ,EAMJ,GALI9P,EAAO,aACT8P,EAAa1P,EAAMtI,EAAO,UAAY,CAACA,EAAO,UAE9CgY,EAAa,CAACtQ,EAAK,iBAEjBQ,EAAO,QACT,OAEF,GAAIA,EAAO,UAAYA,EAAO,SAAS,QAAS,CAC9ClI,EAAO,SAAS,WAAW,CACzB,WAAAgY,CACN,CAAK,EACD,MACF,CAGA,MAAMC,EAAcD,GAAc,CAAChY,EAAO,aAAY,GAAM,CAACA,EAAO,OAAO,KAC3E,IAAIkY,EAAY,EACZ1N,EAAYxK,EAAO,gBAAgB,CAAC,EACxC,QAASZ,EAAI,EAAGA,EAAIyJ,EAAW,OAAQzJ,GAAKA,EAAI8I,EAAO,mBAAqB,EAAIA,EAAO,eAAgB,CACrG,MAAMmK,EAAYjT,EAAI8I,EAAO,mBAAqB,EAAI,EAAIA,EAAO,eAC7D,OAAOW,EAAWzJ,EAAIiT,CAAS,EAAM,KACnC4F,GAAeD,GAAcnP,EAAWzJ,CAAC,GAAK4Y,EAAanP,EAAWzJ,EAAIiT,CAAS,KACrF6F,EAAY9Y,EACZoL,EAAY3B,EAAWzJ,EAAIiT,CAAS,EAAIxJ,EAAWzJ,CAAC,IAE7C6Y,GAAeD,GAAcnP,EAAWzJ,CAAC,KAClD8Y,EAAY9Y,EACZoL,EAAY3B,EAAWA,EAAW,OAAS,CAAC,EAAIA,EAAWA,EAAW,OAAS,CAAC,EAEpF,CACA,IAAIsP,EAAmB,KACnBC,EAAkB,KAClBlQ,EAAO,SACLlI,EAAO,YACToY,EAAkBlQ,EAAO,SAAWA,EAAO,QAAQ,SAAWlI,EAAO,QAAUA,EAAO,QAAQ,OAAO,OAAS,EAAIA,EAAO,OAAO,OAAS,EAChIA,EAAO,QAChBmY,EAAmB,IAIvB,MAAME,GAASL,EAAanP,EAAWqP,CAAS,GAAK1N,EAC/C6H,EAAY6F,EAAYhQ,EAAO,mBAAqB,EAAI,EAAIA,EAAO,eACzE,GAAI4P,EAAW5P,EAAO,aAAc,CAElC,GAAI,CAACA,EAAO,WAAY,CACtBlI,EAAO,QAAQA,EAAO,WAAW,EACjC,MACF,CACIA,EAAO,iBAAmB,SACxBqY,GAASnQ,EAAO,gBAAiBlI,EAAO,QAAQkI,EAAO,QAAUlI,EAAO,MAAQmY,EAAmBD,EAAY7F,CAAS,EAAOrS,EAAO,QAAQkY,CAAS,GAEzJlY,EAAO,iBAAmB,SACxBqY,EAAQ,EAAInQ,EAAO,gBACrBlI,EAAO,QAAQkY,EAAY7F,CAAS,EAC3B+F,IAAoB,MAAQC,EAAQ,GAAK,KAAK,IAAIA,CAAK,EAAInQ,EAAO,gBAC3ElI,EAAO,QAAQoY,CAAe,EAE9BpY,EAAO,QAAQkY,CAAS,EAG9B,KAAO,CAEL,GAAI,CAAChQ,EAAO,YAAa,CACvBlI,EAAO,QAAQA,EAAO,WAAW,EACjC,MACF,CAC0BA,EAAO,aAAe6Q,EAAE,SAAW7Q,EAAO,WAAW,QAAU6Q,EAAE,SAAW7Q,EAAO,WAAW,QAQ7G6Q,EAAE,SAAW7Q,EAAO,WAAW,OACxCA,EAAO,QAAQkY,EAAY7F,CAAS,EAEpCrS,EAAO,QAAQkY,CAAS,GATpBlY,EAAO,iBAAmB,QAC5BA,EAAO,QAAQmY,IAAqB,KAAOA,EAAmBD,EAAY7F,CAAS,EAEjFrS,EAAO,iBAAmB,QAC5BA,EAAO,QAAQoY,IAAoB,KAAOA,EAAkBF,CAAS,EAO3E,CACF,CAEA,SAASI,IAAW,CAClB,MAAMtY,EAAS,KACT,CACJ,OAAAkI,EACA,GAAA3J,CACJ,EAAMyB,EACJ,GAAIzB,GAAMA,EAAG,cAAgB,EAAG,OAG5B2J,EAAO,aACTlI,EAAO,cAAa,EAItB,KAAM,CACJ,eAAAqU,EACA,eAAAD,EACA,SAAAxL,CACJ,EAAM5I,EACEwI,EAAYxI,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAG1DA,EAAO,eAAiB,GACxBA,EAAO,eAAiB,GACxBA,EAAO,WAAU,EACjBA,EAAO,aAAY,EACnBA,EAAO,oBAAmB,EAC1B,MAAMuY,EAAgB/P,GAAaN,EAAO,MACrCA,EAAO,gBAAkB,QAAUA,EAAO,cAAgB,IAAMlI,EAAO,OAAS,CAACA,EAAO,aAAe,CAACA,EAAO,OAAO,gBAAkB,CAACuY,EAC5IvY,EAAO,QAAQA,EAAO,OAAO,OAAS,EAAG,EAAG,GAAO,EAAI,EAEnDA,EAAO,OAAO,MAAQ,CAACwI,EACzBxI,EAAO,YAAYA,EAAO,UAAW,EAAG,GAAO,EAAI,EAEnDA,EAAO,QAAQA,EAAO,YAAa,EAAG,GAAO,EAAI,EAGjDA,EAAO,UAAYA,EAAO,SAAS,SAAWA,EAAO,SAAS,SAChE,aAAaA,EAAO,SAAS,aAAa,EAC1CA,EAAO,SAAS,cAAgB,WAAW,IAAM,CAC3CA,EAAO,UAAYA,EAAO,SAAS,SAAWA,EAAO,SAAS,QAChEA,EAAO,SAAS,OAAM,CAE1B,EAAG,GAAG,GAGRA,EAAO,eAAiBoU,EACxBpU,EAAO,eAAiBqU,EACpBrU,EAAO,OAAO,eAAiB4I,IAAa5I,EAAO,UACrDA,EAAO,cAAa,CAExB,CAEA,SAASwY,GAAQ3H,EAAG,CAClB,MAAM7Q,EAAS,KACVA,EAAO,UACPA,EAAO,aACNA,EAAO,OAAO,eAAe6Q,EAAE,eAAc,EAC7C7Q,EAAO,OAAO,0BAA4BA,EAAO,YACnD6Q,EAAE,gBAAe,EACjBA,EAAE,yBAAwB,IAGhC,CAEA,SAAS4H,IAAW,CAClB,MAAMzY,EAAS,KACT,CACJ,UAAAmI,EACA,aAAAoK,EACA,QAAAhB,CACJ,EAAMvR,EACJ,GAAI,CAACuR,EAAS,OACdvR,EAAO,kBAAoBA,EAAO,UAC9BA,EAAO,eACTA,EAAO,UAAY,CAACmI,EAAU,WAE9BnI,EAAO,UAAY,CAACmI,EAAU,UAG5BnI,EAAO,YAAc,IAAGA,EAAO,UAAY,GAC/CA,EAAO,kBAAiB,EACxBA,EAAO,oBAAmB,EAC1B,IAAIoQ,EACJ,MAAMxD,EAAiB5M,EAAO,aAAY,EAAKA,EAAO,aAAY,EAC9D4M,IAAmB,EACrBwD,EAAc,EAEdA,GAAepQ,EAAO,UAAYA,EAAO,aAAY,GAAM4M,EAEzDwD,IAAgBpQ,EAAO,UACzBA,EAAO,eAAeuS,EAAe,CAACvS,EAAO,UAAYA,EAAO,SAAS,EAE3EA,EAAO,KAAK,eAAgBA,EAAO,UAAW,EAAK,CACrD,CAEA,SAAS0Y,GAAO7H,EAAG,CACjB,MAAM7Q,EAAS,KACfiO,GAAqBjO,EAAQ6Q,EAAE,MAAM,EACjC,EAAA7Q,EAAO,OAAO,SAAWA,EAAO,OAAO,gBAAkB,QAAU,CAACA,EAAO,OAAO,aAGtFA,EAAO,OAAM,CACf,CAEA,SAAS2Y,IAAuB,CAC9B,MAAM3Y,EAAS,KACXA,EAAO,gCACXA,EAAO,8BAAgC,GACnCA,EAAO,OAAO,sBAChBA,EAAO,GAAG,MAAM,YAAc,QAElC,CAEA,MAAM8G,GAAS,CAAC9G,EAAQkH,IAAW,CACjC,MAAMhE,EAAW3F,EAAW,EACtB,CACJ,OAAA2K,EACA,GAAA3J,EACA,UAAA4J,EACA,OAAAzE,CACJ,EAAM1D,EACE4Y,EAAU,CAAC,CAAC1Q,EAAO,OACnB2Q,EAAY3R,IAAW,KAAO,mBAAqB,sBACnD4R,EAAe5R,EACjB,CAAC3I,GAAM,OAAOA,GAAO,WAGzB2E,EAAS2V,CAAS,EAAE,aAAc7Y,EAAO,qBAAsB,CAC7D,QAAS,GACT,QAAA4Y,CACJ,CAAG,EACDra,EAAGsa,CAAS,EAAE,aAAc7Y,EAAO,aAAc,CAC/C,QAAS,EACb,CAAG,EACDzB,EAAGsa,CAAS,EAAE,cAAe7Y,EAAO,aAAc,CAChD,QAAS,EACb,CAAG,EACDkD,EAAS2V,CAAS,EAAE,YAAa7Y,EAAO,YAAa,CACnD,QAAS,GACT,QAAA4Y,CACJ,CAAG,EACD1V,EAAS2V,CAAS,EAAE,cAAe7Y,EAAO,YAAa,CACrD,QAAS,GACT,QAAA4Y,CACJ,CAAG,EACD1V,EAAS2V,CAAS,EAAE,WAAY7Y,EAAO,WAAY,CACjD,QAAS,EACb,CAAG,EACDkD,EAAS2V,CAAS,EAAE,YAAa7Y,EAAO,WAAY,CAClD,QAAS,EACb,CAAG,EACDkD,EAAS2V,CAAS,EAAE,gBAAiB7Y,EAAO,WAAY,CACtD,QAAS,EACb,CAAG,EACDkD,EAAS2V,CAAS,EAAE,cAAe7Y,EAAO,WAAY,CACpD,QAAS,EACb,CAAG,EACDkD,EAAS2V,CAAS,EAAE,aAAc7Y,EAAO,WAAY,CACnD,QAAS,EACb,CAAG,EACDkD,EAAS2V,CAAS,EAAE,eAAgB7Y,EAAO,WAAY,CACrD,QAAS,EACb,CAAG,EACDkD,EAAS2V,CAAS,EAAE,cAAe7Y,EAAO,WAAY,CACpD,QAAS,EACb,CAAG,GAGGkI,EAAO,eAAiBA,EAAO,2BACjC3J,EAAGsa,CAAS,EAAE,QAAS7Y,EAAO,QAAS,EAAI,EAEzCkI,EAAO,SACTC,EAAU0Q,CAAS,EAAE,SAAU7Y,EAAO,QAAQ,EAI5CkI,EAAO,qBACTlI,EAAO8Y,CAAY,EAAEpV,EAAO,KAAOA,EAAO,QAAU,0CAA4C,wBAAyB4U,GAAU,EAAI,EAEvItY,EAAO8Y,CAAY,EAAE,iBAAkBR,GAAU,EAAI,EAIvD/Z,EAAGsa,CAAS,EAAE,OAAQ7Y,EAAO,OAAQ,CACnC,QAAS,EACb,CAAG,EACH,EACA,SAAS+Y,IAAe,CACtB,MAAM/Y,EAAS,KACT,CACJ,OAAAkI,CACJ,EAAMlI,EACJA,EAAO,aAAeoW,GAAa,KAAKpW,CAAM,EAC9CA,EAAO,YAAc8W,GAAY,KAAK9W,CAAM,EAC5CA,EAAO,WAAa4X,GAAW,KAAK5X,CAAM,EAC1CA,EAAO,qBAAuB2Y,GAAqB,KAAK3Y,CAAM,EAC1DkI,EAAO,UACTlI,EAAO,SAAWyY,GAAS,KAAKzY,CAAM,GAExCA,EAAO,QAAUwY,GAAQ,KAAKxY,CAAM,EACpCA,EAAO,OAAS0Y,GAAO,KAAK1Y,CAAM,EAClC8G,GAAO9G,EAAQ,IAAI,CACrB,CACA,SAASgZ,IAAe,CAEtBlS,GADe,KACA,KAAK,CACtB,CACA,IAAImS,GAAW,CACb,aAAAF,GACA,aAAAC,EACF,EAEA,MAAME,GAAgB,CAAClZ,EAAQkI,IACtBlI,EAAO,MAAQkI,EAAO,MAAQA,EAAO,KAAK,KAAO,EAE1D,SAASiR,IAAgB,CACvB,MAAMnZ,EAAS,KACT,CACJ,UAAA4O,EACA,YAAAwK,EACA,OAAAlR,EACA,GAAA3J,CACJ,EAAMyB,EACEqZ,EAAcnR,EAAO,YAC3B,GAAI,CAACmR,GAAeA,GAAe,OAAO,KAAKA,CAAW,EAAE,SAAW,EAAG,OAC1E,MAAMnW,EAAW3F,EAAW,EAGtB+b,EAAkBpR,EAAO,kBAAoB,UAAY,CAACA,EAAO,gBAAkBA,EAAO,gBAAkB,YAC5GqR,EAAsB,CAAC,SAAU,WAAW,EAAE,SAASrR,EAAO,eAAe,GAAK,CAACA,EAAO,gBAAkBlI,EAAO,GAAKkD,EAAS,cAAcgF,EAAO,eAAe,EACrKsR,EAAaxZ,EAAO,cAAcqZ,EAAaC,EAAiBC,CAAmB,EACzF,GAAI,CAACC,GAAcxZ,EAAO,oBAAsBwZ,EAAY,OAE5D,MAAMC,GADuBD,KAAcH,EAAcA,EAAYG,CAAU,EAAI,SAClCxZ,EAAO,eAClD0Z,EAAcR,GAAclZ,EAAQkI,CAAM,EAC1CyR,EAAaT,GAAclZ,EAAQyZ,CAAgB,EACnDG,EAAgB5Z,EAAO,OAAO,WAC9B6Z,EAAeJ,EAAiB,WAChCK,EAAa5R,EAAO,QACtBwR,GAAe,CAACC,GAClBpb,EAAG,UAAU,OAAO,GAAG2J,EAAO,sBAAsB,OAAQ,GAAGA,EAAO,sBAAsB,aAAa,EACzGlI,EAAO,qBAAoB,GAClB,CAAC0Z,GAAeC,IACzBpb,EAAG,UAAU,IAAI,GAAG2J,EAAO,sBAAsB,MAAM,GACnDuR,EAAiB,KAAK,MAAQA,EAAiB,KAAK,OAAS,UAAY,CAACA,EAAiB,KAAK,MAAQvR,EAAO,KAAK,OAAS,WAC/H3J,EAAG,UAAU,IAAI,GAAG2J,EAAO,sBAAsB,aAAa,EAEhElI,EAAO,qBAAoB,GAEzB4Z,GAAiB,CAACC,EACpB7Z,EAAO,gBAAe,EACb,CAAC4Z,GAAiBC,GAC3B7Z,EAAO,cAAa,EAItB,CAAC,aAAc,aAAc,WAAW,EAAE,QAAQoC,GAAQ,CACxD,GAAI,OAAOqX,EAAiBrX,CAAI,EAAM,IAAa,OACnD,MAAM2X,EAAmB7R,EAAO9F,CAAI,GAAK8F,EAAO9F,CAAI,EAAE,QAChD4X,EAAkBP,EAAiBrX,CAAI,GAAKqX,EAAiBrX,CAAI,EAAE,QACrE2X,GAAoB,CAACC,GACvBha,EAAOoC,CAAI,EAAE,QAAO,EAElB,CAAC2X,GAAoBC,GACvBha,EAAOoC,CAAI,EAAE,OAAM,CAEvB,CAAC,EACD,MAAM6X,EAAmBR,EAAiB,WAAaA,EAAiB,YAAcvR,EAAO,UACvFgS,EAAchS,EAAO,OAASuR,EAAiB,gBAAkBvR,EAAO,eAAiB+R,GACzFE,EAAUjS,EAAO,KACnB+R,GAAoBb,GACtBpZ,EAAO,gBAAe,EAExB/C,EAAO+C,EAAO,OAAQyZ,CAAgB,EACtC,MAAMW,EAAYpa,EAAO,OAAO,QAC1Bqa,EAAUra,EAAO,OAAO,KAC9B,OAAO,OAAOA,EAAQ,CACpB,eAAgBA,EAAO,OAAO,eAC9B,eAAgBA,EAAO,OAAO,eAC9B,eAAgBA,EAAO,OAAO,cAClC,CAAG,EACG8Z,GAAc,CAACM,EACjBpa,EAAO,QAAO,EACL,CAAC8Z,GAAcM,GACxBpa,EAAO,OAAM,EAEfA,EAAO,kBAAoBwZ,EAC3BxZ,EAAO,KAAK,oBAAqByZ,CAAgB,EAC7CL,IACEc,GACFla,EAAO,YAAW,EAClBA,EAAO,WAAW4O,CAAS,EAC3B5O,EAAO,aAAY,GACV,CAACma,GAAWE,GACrBra,EAAO,WAAW4O,CAAS,EAC3B5O,EAAO,aAAY,GACVma,GAAW,CAACE,GACrBra,EAAO,YAAW,GAGtBA,EAAO,KAAK,aAAcyZ,CAAgB,CAC5C,CAEA,SAASa,GAAcjB,EAAaxD,EAAM0E,EAAa,CAIrD,GAHI1E,IAAS,SACXA,EAAO,UAEL,CAACwD,GAAexD,IAAS,aAAe,CAAC0E,EAAa,OAC1D,IAAIf,EAAa,GACjB,MAAMhb,EAASZ,EAAS,EAClB4c,EAAgB3E,IAAS,SAAWrX,EAAO,YAAc+b,EAAY,aACrEE,EAAS,OAAO,KAAKpB,CAAW,EAAE,IAAIqB,GAAS,CACnD,GAAI,OAAOA,GAAU,UAAYA,EAAM,QAAQ,GAAG,IAAM,EAAG,CACzD,MAAMC,EAAW,WAAWD,EAAM,OAAO,CAAC,CAAC,EAE3C,MAAO,CACL,MAFYF,EAAgBG,EAG5B,MAAAD,CACR,CACI,CACA,MAAO,CACL,MAAOA,EACP,MAAAA,CACN,CACE,CAAC,EACDD,EAAO,KAAK,CAAC,EAAGG,IAAM,SAAS,EAAE,MAAO,EAAE,EAAI,SAASA,EAAE,MAAO,EAAE,CAAC,EACnE,QAASxb,EAAI,EAAGA,EAAIqb,EAAO,OAAQrb,GAAK,EAAG,CACzC,KAAM,CACJ,MAAAsb,EACA,MAAAG,CACN,EAAQJ,EAAOrb,CAAC,EACRyW,IAAS,SACPrX,EAAO,WAAW,eAAeqc,CAAK,KAAK,EAAE,UAC/CrB,EAAakB,GAENG,GAASN,EAAY,cAC9Bf,EAAakB,EAEjB,CACA,OAAOlB,GAAc,KACvB,CAEA,IAAIH,GAAc,CAChB,cAAAF,GACA,cAAAmB,EACF,EAEA,SAASQ,GAAetV,EAASuV,EAAQ,CACvC,MAAMC,EAAgB,CAAA,EACtB,OAAAxV,EAAQ,QAAQyV,GAAQ,CAClB,OAAOA,GAAS,SAClB,OAAO,KAAKA,CAAI,EAAE,QAAQC,GAAc,CAClCD,EAAKC,CAAU,GACjBF,EAAc,KAAKD,EAASG,CAAU,CAE1C,CAAC,EACQ,OAAOD,GAAS,UACzBD,EAAc,KAAKD,EAASE,CAAI,CAEpC,CAAC,EACMD,CACT,CACA,SAASG,IAAa,CACpB,MAAMnb,EAAS,KACT,CACJ,WAAAkb,EACA,OAAAhT,EACA,IAAAI,EACA,GAAA/J,EACA,OAAAmF,CACJ,EAAM1D,EAEEob,EAAWN,GAAe,CAAC,cAAe5S,EAAO,UAAW,CAChE,YAAalI,EAAO,OAAO,UAAYkI,EAAO,SAAS,OAC3D,EAAK,CACD,WAAcA,EAAO,UACzB,EAAK,CACD,IAAOI,CACX,EAAK,CACD,KAAQJ,EAAO,MAAQA,EAAO,KAAK,KAAO,CAC9C,EAAK,CACD,cAAeA,EAAO,MAAQA,EAAO,KAAK,KAAO,GAAKA,EAAO,KAAK,OAAS,QAC/E,EAAK,CACD,QAAWxE,EAAO,OACtB,EAAK,CACD,IAAOA,EAAO,GAClB,EAAK,CACD,WAAYwE,EAAO,OACvB,EAAK,CACD,SAAYA,EAAO,SAAWA,EAAO,cACzC,EAAK,CACD,iBAAkBA,EAAO,mBAC7B,CAAG,EAAGA,EAAO,sBAAsB,EACjCgT,EAAW,KAAK,GAAGE,CAAQ,EAC3B7c,EAAG,UAAU,IAAI,GAAG2c,CAAU,EAC9Blb,EAAO,qBAAoB,CAC7B,CAEA,SAASqb,IAAgB,CACvB,MAAMrb,EAAS,KACT,CACJ,GAAAzB,EACA,WAAA2c,CACJ,EAAMlb,EACA,CAACzB,GAAM,OAAOA,GAAO,WACzBA,EAAG,UAAU,OAAO,GAAG2c,CAAU,EACjClb,EAAO,qBAAoB,EAC7B,CAEA,IAAIjC,GAAU,CACZ,WAAAod,GACA,cAAAE,EACF,EAEA,SAASC,IAAgB,CACvB,MAAMtb,EAAS,KACT,CACJ,SAAUub,EACV,OAAArT,CACJ,EAAMlI,EACE,CACJ,mBAAAwb,CACJ,EAAMtT,EACJ,GAAIsT,EAAoB,CACtB,MAAMnO,EAAiBrN,EAAO,OAAO,OAAS,EACxCyb,EAAqBzb,EAAO,WAAWqN,CAAc,EAAIrN,EAAO,gBAAgBqN,CAAc,EAAImO,EAAqB,EAC7Hxb,EAAO,SAAWA,EAAO,KAAOyb,CAClC,MACEzb,EAAO,SAAWA,EAAO,SAAS,SAAW,EAE3CkI,EAAO,iBAAmB,KAC5BlI,EAAO,eAAiB,CAACA,EAAO,UAE9BkI,EAAO,iBAAmB,KAC5BlI,EAAO,eAAiB,CAACA,EAAO,UAE9Bub,GAAaA,IAAcvb,EAAO,WACpCA,EAAO,MAAQ,IAEbub,IAAcvb,EAAO,UACvBA,EAAO,KAAKA,EAAO,SAAW,OAAS,QAAQ,CAEnD,CACA,IAAI0b,GAAkB,CACpB,cAAAJ,EACF,EAEIK,GAAW,CACb,KAAM,GACN,UAAW,aACX,eAAgB,GAChB,sBAAuB,mBACvB,kBAAmB,UACnB,aAAc,EACd,MAAO,IACP,QAAS,GACT,qBAAsB,GACtB,eAAgB,GAChB,OAAQ,GACR,eAAgB,GAChB,aAAc,SACd,QAAS,GACT,kBAAmB,wDAEnB,MAAO,KACP,OAAQ,KAER,+BAAgC,GAEhC,UAAW,KACX,IAAK,KAEL,mBAAoB,GACpB,mBAAoB,GAEpB,WAAY,GAEZ,eAAgB,GAEhB,iBAAkB,GAElB,OAAQ,QAIR,YAAa,OACb,gBAAiB,SAEjB,aAAc,EACd,cAAe,EACf,eAAgB,EAChB,mBAAoB,EACpB,mBAAoB,GACpB,eAAgB,GAChB,qBAAsB,GACtB,mBAAoB,EAEpB,kBAAmB,EAEnB,oBAAqB,GACrB,yBAA0B,GAE1B,cAAe,GAEf,aAAc,GAEd,WAAY,EACZ,WAAY,GACZ,cAAe,GACf,YAAa,GACb,WAAY,GACZ,gBAAiB,GACjB,aAAc,IACd,aAAc,GACd,eAAgB,GAChB,UAAW,EACX,yBAA0B,GAC1B,yBAA0B,GAC1B,8BAA+B,GAC/B,oBAAqB,GAErB,kBAAmB,GAEnB,WAAY,GACZ,gBAAiB,IAEjB,oBAAqB,GAErB,WAAY,GAEZ,cAAe,GACf,yBAA0B,GAC1B,oBAAqB,GAErB,KAAM,GACN,mBAAoB,GACpB,qBAAsB,EACtB,oBAAqB,GAErB,OAAQ,GAER,eAAgB,GAChB,eAAgB,GAChB,aAAc,KAEd,UAAW,GACX,eAAgB,oBAChB,kBAAmB,KAEnB,iBAAkB,GAClB,wBAAyB,GAEzB,uBAAwB,UAExB,WAAY,eACZ,gBAAiB,qBACjB,iBAAkB,sBAClB,kBAAmB,uBACnB,uBAAwB,6BACxB,eAAgB,oBAChB,eAAgB,oBAChB,aAAc,iBACd,mBAAoB,wBACpB,oBAAqB,EAErB,mBAAoB,GAEpB,aAAc,EAChB,EAEA,SAASC,GAAmB1T,EAAQ2T,EAAkB,CACpD,OAAO,SAAsB7e,EAAK,CAC5BA,IAAQ,SACVA,EAAM,CAAA,GAER,MAAM8e,EAAkB,OAAO,KAAK9e,CAAG,EAAE,CAAC,EACpC+e,EAAe/e,EAAI8e,CAAe,EACxC,GAAI,OAAOC,GAAiB,UAAYA,IAAiB,KAAM,CAC7D9e,EAAO4e,EAAkB7e,CAAG,EAC5B,MACF,CAYA,GAXIkL,EAAO4T,CAAe,IAAM,KAC9B5T,EAAO4T,CAAe,EAAI,CACxB,QAAS,EACjB,GAEQA,IAAoB,cAAgB5T,EAAO4T,CAAe,GAAK5T,EAAO4T,CAAe,EAAE,SAAW,CAAC5T,EAAO4T,CAAe,EAAE,QAAU,CAAC5T,EAAO4T,CAAe,EAAE,SAChK5T,EAAO4T,CAAe,EAAE,KAAO,IAE7B,CAAC,aAAc,WAAW,EAAE,QAAQA,CAAe,GAAK,GAAK5T,EAAO4T,CAAe,GAAK5T,EAAO4T,CAAe,EAAE,SAAW,CAAC5T,EAAO4T,CAAe,EAAE,KACtJ5T,EAAO4T,CAAe,EAAE,KAAO,IAE7B,EAAEA,KAAmB5T,GAAU,YAAa6T,GAAe,CAC7D9e,EAAO4e,EAAkB7e,CAAG,EAC5B,MACF,CACI,OAAOkL,EAAO4T,CAAe,GAAM,UAAY,EAAE,YAAa5T,EAAO4T,CAAe,KACtF5T,EAAO4T,CAAe,EAAE,QAAU,IAE/B5T,EAAO4T,CAAe,IAAG5T,EAAO4T,CAAe,EAAI,CACtD,QAAS,EACf,GACI7e,EAAO4e,EAAkB7e,CAAG,CAC9B,CACF,CAGA,MAAMgf,GAAa,CACjB,cAAAnV,GACA,OAAA+I,GACA,UAAA3D,GACA,WAAAmF,GACA,MAAA1H,GACA,KAAA6L,GACA,WAAAI,GACA,OAAQsD,GACR,YAAAI,GACA,cAAeqC,GACf,QAAA3d,EACF,EACMke,GAAmB,CAAA,EACzB,MAAMC,CAAO,CACX,aAAc,CACZ,IAAI3d,EACA2J,EACJ,QAASb,EAAO,UAAU,OAAQC,EAAO,IAAI,MAAMD,CAAI,EAAGE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,CAAI,EAAI,UAAUA,CAAI,EAEzBD,EAAK,SAAW,GAAKA,EAAK,CAAC,EAAE,aAAe,OAAO,UAAU,SAAS,KAAKA,EAAK,CAAC,CAAC,EAAE,MAAM,EAAG,EAAE,IAAM,SACvGY,EAASZ,EAAK,CAAC,EAEf,CAAC/I,EAAI2J,CAAM,EAAIZ,EAEZY,IAAQA,EAAS,CAAA,GACtBA,EAASjL,EAAO,CAAA,EAAIiL,CAAM,EACtB3J,GAAM,CAAC2J,EAAO,KAAIA,EAAO,GAAK3J,GAClC,MAAM2E,EAAW3F,EAAW,EAC5B,GAAI2K,EAAO,IAAM,OAAOA,EAAO,IAAO,UAAYhF,EAAS,iBAAiBgF,EAAO,EAAE,EAAE,OAAS,EAAG,CACjG,MAAMiU,EAAU,CAAA,EAChB,OAAAjZ,EAAS,iBAAiBgF,EAAO,EAAE,EAAE,QAAQqS,GAAe,CAC1D,MAAM6B,EAAYnf,EAAO,CAAA,EAAIiL,EAAQ,CACnC,GAAIqS,CACd,CAAS,EACD4B,EAAQ,KAAK,IAAID,EAAOE,CAAS,CAAC,CACpC,CAAC,EAEMD,CACT,CAGA,MAAMnc,EAAS,KACfA,EAAO,WAAa,GACpBA,EAAO,QAAUmD,GAAU,EAC3BnD,EAAO,OAASoE,GAAU,CACxB,UAAW8D,EAAO,SACxB,CAAK,EACDlI,EAAO,QAAUgF,GAAU,EAC3BhF,EAAO,gBAAkB,CAAA,EACzBA,EAAO,mBAAqB,CAAA,EAC5BA,EAAO,QAAU,CAAC,GAAGA,EAAO,WAAW,EACnCkI,EAAO,SAAW,MAAM,QAAQA,EAAO,OAAO,GAChDlI,EAAO,QAAQ,KAAK,GAAGkI,EAAO,OAAO,EAEvC,MAAM2T,EAAmB,CAAA,EACzB7b,EAAO,QAAQ,QAAQqc,GAAO,CAC5BA,EAAI,CACF,OAAAnU,EACA,OAAAlI,EACA,aAAc4b,GAAmB1T,EAAQ2T,CAAgB,EACzD,GAAI7b,EAAO,GAAG,KAAKA,CAAM,EACzB,KAAMA,EAAO,KAAK,KAAKA,CAAM,EAC7B,IAAKA,EAAO,IAAI,KAAKA,CAAM,EAC3B,KAAMA,EAAO,KAAK,KAAKA,CAAM,CACrC,CAAO,CACH,CAAC,EAGD,MAAMsc,EAAerf,EAAO,GAAI0e,GAAUE,CAAgB,EAG1D,OAAA7b,EAAO,OAAS/C,EAAO,CAAA,EAAIqf,EAAcL,GAAkB/T,CAAM,EACjElI,EAAO,eAAiB/C,EAAO,CAAA,EAAI+C,EAAO,MAAM,EAChDA,EAAO,aAAe/C,EAAO,CAAA,EAAIiL,CAAM,EAGnClI,EAAO,QAAUA,EAAO,OAAO,IACjC,OAAO,KAAKA,EAAO,OAAO,EAAE,EAAE,QAAQuc,GAAa,CACjDvc,EAAO,GAAGuc,EAAWvc,EAAO,OAAO,GAAGuc,CAAS,CAAC,CAClD,CAAC,EAECvc,EAAO,QAAUA,EAAO,OAAO,OACjCA,EAAO,MAAMA,EAAO,OAAO,KAAK,EAIlC,OAAO,OAAOA,EAAQ,CACpB,QAASA,EAAO,OAAO,QACvB,GAAAzB,EAEA,WAAY,CAAA,EAEZ,OAAQ,CAAA,EACR,WAAY,CAAA,EACZ,SAAU,CAAA,EACV,gBAAiB,CAAA,EAEjB,cAAe,CACb,OAAOyB,EAAO,OAAO,YAAc,YACrC,EACA,YAAa,CACX,OAAOA,EAAO,OAAO,YAAc,UACrC,EAEA,YAAa,EACb,UAAW,EAEX,YAAa,GACb,MAAO,GAEP,UAAW,EACX,kBAAmB,EACnB,SAAU,EACV,SAAU,EACV,UAAW,GACX,uBAAwB,CAGtB,OAAO,KAAK,MAAM,KAAK,UAAY,GAAK,EAAE,EAAI,GAAK,EACrD,EAEA,eAAgBA,EAAO,OAAO,eAC9B,eAAgBA,EAAO,OAAO,eAE9B,gBAAiB,CACf,UAAW,OACX,QAAS,OACT,oBAAqB,OACrB,eAAgB,OAChB,YAAa,OACb,iBAAkB,OAClB,eAAgB,OAChB,mBAAoB,OAEpB,kBAAmBA,EAAO,OAAO,kBAEjC,cAAe,EACf,aAAc,OAEd,WAAY,CAAA,EACZ,oBAAqB,OACrB,YAAa,OACb,UAAW,KACX,QAAS,IACjB,EAEM,WAAY,GAEZ,eAAgBA,EAAO,OAAO,eAC9B,QAAS,CACP,OAAQ,EACR,OAAQ,EACR,SAAU,EACV,SAAU,EACV,KAAM,CACd,EAEM,aAAc,CAAA,EACd,aAAc,CACpB,CAAK,EACDA,EAAO,KAAK,SAAS,EAGjBA,EAAO,OAAO,MAChBA,EAAO,KAAI,EAKNA,CACT,CACA,kBAAkBwc,EAAU,CAC1B,OAAI,KAAK,eACAA,EAGF,CACL,MAAS,SACT,aAAc,cACd,iBAAkB,eAClB,cAAe,aACf,eAAgB,gBAChB,eAAgB,cAChB,gBAAiB,iBACjB,YAAe,cACrB,EAAMA,CAAQ,CACZ,CACA,cAAclT,EAAS,CACrB,KAAM,CACJ,SAAAlB,EACA,OAAAF,CACN,EAAQ,KACEQ,EAAS5H,EAAgBsH,EAAU,IAAIF,EAAO,UAAU,gBAAgB,EACxEkF,EAAkB/K,GAAaqG,EAAO,CAAC,CAAC,EAC9C,OAAOrG,GAAaiH,CAAO,EAAI8D,CACjC,CACA,oBAAoB5F,EAAO,CACzB,OAAO,KAAK,cAAc,KAAK,OAAO,KAAK8B,GAAWA,EAAQ,aAAa,yBAAyB,EAAI,IAAM9B,CAAK,CAAC,CACtH,CACA,sBAAsBA,EAAO,CAC3B,OAAI,KAAK,MAAQ,KAAK,OAAO,MAAQ,KAAK,OAAO,KAAK,KAAO,IACvD,KAAK,OAAO,KAAK,OAAS,SAC5BA,EAAQ,KAAK,MAAMA,EAAQ,KAAK,OAAO,KAAK,IAAI,EACvC,KAAK,OAAO,KAAK,OAAS,QACnCA,EAAQA,EAAQ,KAAK,KAAK,KAAK,OAAO,OAAS,KAAK,OAAO,KAAK,IAAI,IAGjEA,CACT,CACA,cAAe,CACb,MAAMxH,EAAS,KACT,CACJ,SAAAoI,EACA,OAAAF,CACN,EAAQlI,EACJA,EAAO,OAASc,EAAgBsH,EAAU,IAAIF,EAAO,UAAU,gBAAgB,CACjF,CACA,QAAS,CACP,MAAMlI,EAAS,KACXA,EAAO,UACXA,EAAO,QAAU,GACbA,EAAO,OAAO,YAChBA,EAAO,cAAa,EAEtBA,EAAO,KAAK,QAAQ,EACtB,CACA,SAAU,CACR,MAAMA,EAAS,KACVA,EAAO,UACZA,EAAO,QAAU,GACbA,EAAO,OAAO,YAChBA,EAAO,gBAAe,EAExBA,EAAO,KAAK,SAAS,EACvB,CACA,YAAYW,EAAU6K,EAAO,CAC3B,MAAMxL,EAAS,KACfW,EAAW,KAAK,IAAI,KAAK,IAAIA,EAAU,CAAC,EAAG,CAAC,EAC5C,MAAM8b,EAAMzc,EAAO,aAAY,EAEzBS,GADMT,EAAO,aAAY,EACRyc,GAAO9b,EAAW8b,EACzCzc,EAAO,YAAYS,EAAS,OAAO+K,EAAU,IAAc,EAAIA,CAAK,EACpExL,EAAO,kBAAiB,EACxBA,EAAO,oBAAmB,CAC5B,CACA,sBAAuB,CACrB,MAAMA,EAAS,KACf,GAAI,CAACA,EAAO,OAAO,cAAgB,CAACA,EAAO,GAAI,OAC/C,MAAM0c,EAAM1c,EAAO,GAAG,UAAU,MAAM,GAAG,EAAE,OAAO+L,GACzCA,EAAU,QAAQ,QAAQ,IAAM,GAAKA,EAAU,QAAQ/L,EAAO,OAAO,sBAAsB,IAAM,CACzG,EACDA,EAAO,KAAK,oBAAqB0c,EAAI,KAAK,GAAG,CAAC,CAChD,CACA,gBAAgBpT,EAAS,CACvB,MAAMtJ,EAAS,KACf,OAAIA,EAAO,UAAkB,GACtBsJ,EAAQ,UAAU,MAAM,GAAG,EAAE,OAAOyC,GAClCA,EAAU,QAAQ,cAAc,IAAM,GAAKA,EAAU,QAAQ/L,EAAO,OAAO,UAAU,IAAM,CACnG,EAAE,KAAK,GAAG,CACb,CACA,mBAAoB,CAClB,MAAMA,EAAS,KACf,GAAI,CAACA,EAAO,OAAO,cAAgB,CAACA,EAAO,GAAI,OAC/C,MAAM2c,EAAU,CAAA,EAChB3c,EAAO,OAAO,QAAQsJ,GAAW,CAC/B,MAAM4R,EAAalb,EAAO,gBAAgBsJ,CAAO,EACjDqT,EAAQ,KAAK,CACX,QAAArT,EACA,WAAA4R,CACR,CAAO,EACDlb,EAAO,KAAK,cAAesJ,EAAS4R,CAAU,CAChD,CAAC,EACDlb,EAAO,KAAK,gBAAiB2c,CAAO,CACtC,CACA,qBAAqBC,EAAMC,EAAO,CAC5BD,IAAS,SACXA,EAAO,WAELC,IAAU,SACZA,EAAQ,IAEV,MAAM7c,EAAS,KACT,CACJ,OAAAkI,EACA,OAAAQ,EACA,WAAAG,EACA,gBAAAC,EACA,KAAMT,EACN,YAAAuF,CACN,EAAQ5N,EACJ,IAAI8c,EAAM,EACV,GAAI,OAAO5U,EAAO,eAAkB,SAAU,OAAOA,EAAO,cAC5D,GAAIA,EAAO,eAAgB,CACzB,IAAIsB,EAAYd,EAAOkF,CAAW,EAAI,KAAK,KAAKlF,EAAOkF,CAAW,EAAE,eAAe,EAAI,EACnFmP,EACJ,QAAS3d,EAAIwO,EAAc,EAAGxO,EAAIsJ,EAAO,OAAQtJ,GAAK,EAChDsJ,EAAOtJ,CAAC,GAAK,CAAC2d,IAChBvT,GAAa,KAAK,KAAKd,EAAOtJ,CAAC,EAAE,eAAe,EAChD0d,GAAO,EACHtT,EAAYnB,IAAY0U,EAAY,KAG5C,QAAS3d,EAAIwO,EAAc,EAAGxO,GAAK,EAAGA,GAAK,EACrCsJ,EAAOtJ,CAAC,GAAK,CAAC2d,IAChBvT,GAAad,EAAOtJ,CAAC,EAAE,gBACvB0d,GAAO,EACHtT,EAAYnB,IAAY0U,EAAY,IAG9C,SAEMH,IAAS,UACX,QAASxd,EAAIwO,EAAc,EAAGxO,EAAIsJ,EAAO,OAAQtJ,GAAK,GAChCyd,EAAQhU,EAAWzJ,CAAC,EAAI0J,EAAgB1J,CAAC,EAAIyJ,EAAW+E,CAAW,EAAIvF,EAAaQ,EAAWzJ,CAAC,EAAIyJ,EAAW+E,CAAW,EAAIvF,KAEhJyU,GAAO,OAKX,SAAS1d,EAAIwO,EAAc,EAAGxO,GAAK,EAAGA,GAAK,EACrByJ,EAAW+E,CAAW,EAAI/E,EAAWzJ,CAAC,EAAIiJ,IAE5DyU,GAAO,GAKf,OAAOA,CACT,CACA,QAAS,CACP,MAAM9c,EAAS,KACf,GAAI,CAACA,GAAUA,EAAO,UAAW,OACjC,KAAM,CACJ,SAAA4I,EACA,OAAAV,CACN,EAAQlI,EAEAkI,EAAO,aACTlI,EAAO,cAAa,EAEtB,CAAC,GAAGA,EAAO,GAAG,iBAAiB,kBAAkB,CAAC,EAAE,QAAQkO,GAAW,CACjEA,EAAQ,UACVD,GAAqBjO,EAAQkO,CAAO,CAExC,CAAC,EACDlO,EAAO,WAAU,EACjBA,EAAO,aAAY,EACnBA,EAAO,eAAc,EACrBA,EAAO,oBAAmB,EAC1B,SAAS+P,GAAe,CACtB,MAAMiN,EAAiBhd,EAAO,aAAeA,EAAO,UAAY,GAAKA,EAAO,UACtE2Q,EAAe,KAAK,IAAI,KAAK,IAAIqM,EAAgBhd,EAAO,aAAY,CAAE,EAAGA,EAAO,aAAY,CAAE,EACpGA,EAAO,aAAa2Q,CAAY,EAChC3Q,EAAO,kBAAiB,EACxBA,EAAO,oBAAmB,CAC5B,CACA,IAAIid,EACJ,GAAI/U,EAAO,UAAYA,EAAO,SAAS,SAAW,CAACA,EAAO,QACxD6H,EAAY,EACR7H,EAAO,YACTlI,EAAO,iBAAgB,MAEpB,CACL,IAAKkI,EAAO,gBAAkB,QAAUA,EAAO,cAAgB,IAAMlI,EAAO,OAAS,CAACkI,EAAO,eAAgB,CAC3G,MAAMQ,EAAS1I,EAAO,SAAWkI,EAAO,QAAQ,QAAUlI,EAAO,QAAQ,OAASA,EAAO,OACzFid,EAAajd,EAAO,QAAQ0I,EAAO,OAAS,EAAG,EAAG,GAAO,EAAI,CAC/D,MACEuU,EAAajd,EAAO,QAAQA,EAAO,YAAa,EAAG,GAAO,EAAI,EAE3Did,GACHlN,EAAY,CAEhB,CACI7H,EAAO,eAAiBU,IAAa5I,EAAO,UAC9CA,EAAO,cAAa,EAEtBA,EAAO,KAAK,QAAQ,CACtB,CACA,gBAAgBkd,EAAcC,EAAY,CACpCA,IAAe,SACjBA,EAAa,IAEf,MAAMnd,EAAS,KACTod,EAAmBpd,EAAO,OAAO,UAKvC,OAJKkd,IAEHA,EAAeE,IAAqB,aAAe,WAAa,cAE9DF,IAAiBE,GAAoBF,IAAiB,cAAgBA,IAAiB,aAG3Fld,EAAO,GAAG,UAAU,OAAO,GAAGA,EAAO,OAAO,sBAAsB,GAAGod,CAAgB,EAAE,EACvFpd,EAAO,GAAG,UAAU,IAAI,GAAGA,EAAO,OAAO,sBAAsB,GAAGkd,CAAY,EAAE,EAChFld,EAAO,qBAAoB,EAC3BA,EAAO,OAAO,UAAYkd,EAC1Bld,EAAO,OAAO,QAAQsJ,GAAW,CAC3B4T,IAAiB,WACnB5T,EAAQ,MAAM,MAAQ,GAEtBA,EAAQ,MAAM,OAAS,EAE3B,CAAC,EACDtJ,EAAO,KAAK,iBAAiB,EACzBmd,GAAYnd,EAAO,OAAM,GACtBA,CACT,CACA,wBAAwBgR,EAAW,CACjC,MAAMhR,EAAS,KACXA,EAAO,KAAOgR,IAAc,OAAS,CAAChR,EAAO,KAAOgR,IAAc,QACtEhR,EAAO,IAAMgR,IAAc,MAC3BhR,EAAO,aAAeA,EAAO,OAAO,YAAc,cAAgBA,EAAO,IACrEA,EAAO,KACTA,EAAO,GAAG,UAAU,IAAI,GAAGA,EAAO,OAAO,sBAAsB,KAAK,EACpEA,EAAO,GAAG,IAAM,QAEhBA,EAAO,GAAG,UAAU,OAAO,GAAGA,EAAO,OAAO,sBAAsB,KAAK,EACvEA,EAAO,GAAG,IAAM,OAElBA,EAAO,OAAM,EACf,CACA,MAAMe,EAAS,CACb,MAAMf,EAAS,KACf,GAAIA,EAAO,QAAS,MAAO,GAG3B,IAAIzB,EAAKwC,GAAWf,EAAO,OAAO,GAIlC,GAHI,OAAOzB,GAAO,WAChBA,EAAK,SAAS,cAAcA,CAAE,GAE5B,CAACA,EACH,MAAO,GAETA,EAAG,OAASyB,EACRzB,EAAG,YAAcA,EAAG,WAAW,MAAQA,EAAG,WAAW,KAAK,WAAayB,EAAO,OAAO,sBAAsB,YAAW,IACxHA,EAAO,UAAY,IAErB,MAAMqd,EAAqB,IAClB,KAAKrd,EAAO,OAAO,cAAgB,IAAI,KAAI,EAAG,MAAM,GAAG,EAAE,KAAK,GAAG,CAAC,GAW3E,IAAImI,EARE5J,GAAMA,EAAG,YAAcA,EAAG,WAAW,cAC3BA,EAAG,WAAW,cAAc8e,EAAkB,CAAE,EAIvDvc,EAAgBvC,EAAI8e,EAAkB,CAAE,EAAE,CAAC,EAIpD,MAAI,CAAClV,GAAanI,EAAO,OAAO,iBAC9BmI,EAAYxG,GAAc,MAAO3B,EAAO,OAAO,YAAY,EAC3DzB,EAAG,OAAO4J,CAAS,EACnBrH,EAAgBvC,EAAI,IAAIyB,EAAO,OAAO,UAAU,EAAE,EAAE,QAAQsJ,GAAW,CACrEnB,EAAU,OAAOmB,CAAO,CAC1B,CAAC,GAEH,OAAO,OAAOtJ,EAAQ,CACpB,GAAAzB,EACA,UAAA4J,EACA,SAAUnI,EAAO,WAAa,CAACzB,EAAG,WAAW,KAAK,WAAaA,EAAG,WAAW,KAAO4J,EACpF,OAAQnI,EAAO,UAAYzB,EAAG,WAAW,KAAOA,EAChD,QAAS,GAET,IAAKA,EAAG,IAAI,YAAW,IAAO,OAAS4D,EAAa5D,EAAI,WAAW,IAAM,MACzE,aAAcyB,EAAO,OAAO,YAAc,eAAiBzB,EAAG,IAAI,YAAW,IAAO,OAAS4D,EAAa5D,EAAI,WAAW,IAAM,OAC/H,SAAU4D,EAAagG,EAAW,SAAS,IAAM,aACvD,CAAK,EACM,EACT,CACA,KAAK5J,EAAI,CACP,MAAMyB,EAAS,KAGf,GAFIA,EAAO,aACKA,EAAO,MAAMzB,CAAE,IACf,GAAO,OAAOyB,EAC9BA,EAAO,KAAK,YAAY,EAGpBA,EAAO,OAAO,aAChBA,EAAO,cAAa,EAItBA,EAAO,WAAU,EAGjBA,EAAO,WAAU,EAGjBA,EAAO,aAAY,EACfA,EAAO,OAAO,eAChBA,EAAO,cAAa,EAIlBA,EAAO,OAAO,YAAcA,EAAO,SACrCA,EAAO,cAAa,EAIlBA,EAAO,OAAO,MAAQA,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAChEA,EAAO,QAAQA,EAAO,OAAO,aAAeA,EAAO,QAAQ,aAAc,EAAGA,EAAO,OAAO,mBAAoB,GAAO,EAAI,EAEzHA,EAAO,QAAQA,EAAO,OAAO,aAAc,EAAGA,EAAO,OAAO,mBAAoB,GAAO,EAAI,EAIzFA,EAAO,OAAO,MAChBA,EAAO,WAAW,OAAW,EAAI,EAInCA,EAAO,aAAY,EACnB,MAAMsd,EAAe,CAAC,GAAGtd,EAAO,GAAG,iBAAiB,kBAAkB,CAAC,EACvE,OAAIA,EAAO,WACTsd,EAAa,KAAK,GAAGtd,EAAO,OAAO,iBAAiB,kBAAkB,CAAC,EAEzEsd,EAAa,QAAQpP,GAAW,CAC1BA,EAAQ,SACVD,GAAqBjO,EAAQkO,CAAO,EAEpCA,EAAQ,iBAAiB,OAAQ2C,GAAK,CACpC5C,GAAqBjO,EAAQ6Q,EAAE,MAAM,CACvC,CAAC,CAEL,CAAC,EACDvC,GAAQtO,CAAM,EAGdA,EAAO,YAAc,GACrBsO,GAAQtO,CAAM,EAGdA,EAAO,KAAK,MAAM,EAClBA,EAAO,KAAK,WAAW,EAChBA,CACT,CACA,QAAQud,EAAgBC,EAAa,CAC/BD,IAAmB,SACrBA,EAAiB,IAEfC,IAAgB,SAClBA,EAAc,IAEhB,MAAMxd,EAAS,KACT,CACJ,OAAAkI,EACA,GAAA3J,EACA,UAAA4J,EACA,OAAAO,CACN,EAAQ1I,EACJ,OAAI,OAAOA,EAAO,OAAW,KAAeA,EAAO,YAGnDA,EAAO,KAAK,eAAe,EAG3BA,EAAO,YAAc,GAGrBA,EAAO,aAAY,EAGfkI,EAAO,MACTlI,EAAO,YAAW,EAIhBwd,IACFxd,EAAO,cAAa,EAChBzB,GAAM,OAAOA,GAAO,UACtBA,EAAG,gBAAgB,OAAO,EAExB4J,GACFA,EAAU,gBAAgB,OAAO,EAE/BO,GAAUA,EAAO,QACnBA,EAAO,QAAQY,GAAW,CACxBA,EAAQ,UAAU,OAAOpB,EAAO,kBAAmBA,EAAO,uBAAwBA,EAAO,iBAAkBA,EAAO,eAAgBA,EAAO,cAAc,EACvJoB,EAAQ,gBAAgB,OAAO,EAC/BA,EAAQ,gBAAgB,yBAAyB,CACnD,CAAC,GAGLtJ,EAAO,KAAK,SAAS,EAGrB,OAAO,KAAKA,EAAO,eAAe,EAAE,QAAQuc,GAAa,CACvDvc,EAAO,IAAIuc,CAAS,CACtB,CAAC,EACGgB,IAAmB,KACjBvd,EAAO,IAAM,OAAOA,EAAO,IAAO,WACpCA,EAAO,GAAG,OAAS,MAErB/B,GAAY+B,CAAM,GAEpBA,EAAO,UAAY,IACZ,IACT,CACA,OAAO,eAAeyd,EAAa,CACjCxgB,EAAOgf,GAAkBwB,CAAW,CACtC,CACA,WAAW,kBAAmB,CAC5B,OAAOxB,EACT,CACA,WAAW,UAAW,CACpB,OAAON,EACT,CACA,OAAO,cAAcU,EAAK,CACnBH,EAAO,UAAU,cAAaA,EAAO,UAAU,YAAc,CAAA,GAClE,MAAMwB,EAAUxB,EAAO,UAAU,YAC7B,OAAOG,GAAQ,YAAcqB,EAAQ,QAAQrB,CAAG,EAAI,GACtDqB,EAAQ,KAAKrB,CAAG,CAEpB,CACA,OAAO,IAAIsB,EAAQ,CACjB,OAAI,MAAM,QAAQA,CAAM,GACtBA,EAAO,QAAQC,GAAK1B,EAAO,cAAc0B,CAAC,CAAC,EACpC1B,IAETA,EAAO,cAAcyB,CAAM,EACpBzB,EACT,CACF,CACA,OAAO,KAAKF,EAAU,EAAE,QAAQ6B,GAAkB,CAChD,OAAO,KAAK7B,GAAW6B,CAAc,CAAC,EAAE,QAAQC,GAAe,CAC7D5B,EAAO,UAAU4B,CAAW,EAAI9B,GAAW6B,CAAc,EAAEC,CAAW,CACxE,CAAC,CACH,CAAC,EACD5B,EAAO,IAAI,CAACjX,GAAQiB,EAAQ,CAAC,EC32H7B,SAAS6X,GAA0B/d,EAAQge,EAAgB9V,EAAQ+V,EAAY,CAC7E,OAAIje,EAAO,OAAO,gBAChB,OAAO,KAAKie,CAAU,EAAE,QAAQ5gB,GAAO,CACrC,GAAI,CAAC6K,EAAO7K,CAAG,GAAK6K,EAAO,OAAS,GAAM,CACxC,IAAInH,EAAUD,EAAgBd,EAAO,GAAI,IAAIie,EAAW5gB,CAAG,CAAC,EAAE,EAAE,CAAC,EAC5D0D,IACHA,EAAUY,GAAc,MAAOsc,EAAW5gB,CAAG,CAAC,EAC9C0D,EAAQ,UAAYkd,EAAW5gB,CAAG,EAClC2C,EAAO,GAAG,OAAOe,CAAO,GAE1BmH,EAAO7K,CAAG,EAAI0D,EACdid,EAAe3gB,CAAG,EAAI0D,CACxB,CACF,CAAC,EAEImH,CACT,CCfA,SAASgW,GAAWne,EAAM,CACxB,GAAI,CACF,OAAAC,EACA,aAAAmG,EACA,GAAAjB,EACA,KAAAC,CACJ,EAAMpF,EACJoG,EAAa,CACX,WAAY,CACV,OAAQ,KACR,OAAQ,KACR,YAAa,GACb,cAAe,yBACf,YAAa,uBACb,UAAW,qBACX,wBAAyB,4BAC/B,CACA,CAAG,EACDnG,EAAO,WAAa,CAClB,OAAQ,KACR,OAAQ,IACZ,EACE,SAASme,EAAM5f,EAAI,CACjB,IAAI6f,EACJ,OAAI7f,GAAM,OAAOA,GAAO,UAAYyB,EAAO,YACzCoe,EAAMpe,EAAO,GAAG,cAAczB,CAAE,GAAKyB,EAAO,OAAO,cAAczB,CAAE,EAC/D6f,GAAYA,GAEd7f,IACE,OAAOA,GAAO,WAAU6f,EAAM,CAAC,GAAG,SAAS,iBAAiB7f,CAAE,CAAC,GAC/DyB,EAAO,OAAO,mBAAqB,OAAOzB,GAAO,UAAY6f,GAAOA,EAAI,OAAS,GAAKpe,EAAO,GAAG,iBAAiBzB,CAAE,EAAE,SAAW,EAClI6f,EAAMpe,EAAO,GAAG,cAAczB,CAAE,EACvB6f,GAAOA,EAAI,SAAW,IAC/BA,EAAMA,EAAI,CAAC,IAGX7f,GAAM,CAAC6f,EAAY7f,EAEhB6f,EACT,CACA,SAASC,EAAS9f,EAAI+f,EAAU,CAC9B,MAAMpW,EAASlI,EAAO,OAAO,WAC7BzB,EAAKqE,EAAkBrE,CAAE,EACzBA,EAAG,QAAQggB,GAAS,CACdA,IACFA,EAAM,UAAUD,EAAW,MAAQ,QAAQ,EAAE,GAAGpW,EAAO,cAAc,MAAM,GAAG,CAAC,EAC3EqW,EAAM,UAAY,WAAUA,EAAM,SAAWD,GAC7Cte,EAAO,OAAO,eAAiBA,EAAO,SACxCue,EAAM,UAAUve,EAAO,SAAW,MAAQ,QAAQ,EAAEkI,EAAO,SAAS,EAG1E,CAAC,CACH,CACA,SAAS0H,GAAS,CAEhB,KAAM,CACJ,OAAA4O,EACA,OAAAC,CACN,EAAQze,EAAO,WACX,GAAIA,EAAO,OAAO,KAAM,CACtBqe,EAASI,EAAQ,EAAK,EACtBJ,EAASG,EAAQ,EAAK,EACtB,MACF,CACAH,EAASI,EAAQze,EAAO,aAAe,CAACA,EAAO,OAAO,MAAM,EAC5Dqe,EAASG,EAAQxe,EAAO,OAAS,CAACA,EAAO,OAAO,MAAM,CACxD,CACA,SAAS0e,EAAY7N,EAAG,CACtBA,EAAE,eAAc,EACZ,EAAA7Q,EAAO,aAAe,CAACA,EAAO,OAAO,MAAQ,CAACA,EAAO,OAAO,UAChEA,EAAO,UAAS,EAChBmF,EAAK,gBAAgB,EACvB,CACA,SAASwZ,EAAY9N,EAAG,CACtBA,EAAE,eAAc,EACZ,EAAA7Q,EAAO,OAAS,CAACA,EAAO,OAAO,MAAQ,CAACA,EAAO,OAAO,UAC1DA,EAAO,UAAS,EAChBmF,EAAK,gBAAgB,EACvB,CACA,SAASuB,GAAO,CACd,MAAMwB,EAASlI,EAAO,OAAO,WAK7B,GAJAA,EAAO,OAAO,WAAa+d,GAA0B/d,EAAQA,EAAO,eAAe,WAAYA,EAAO,OAAO,WAAY,CACvH,OAAQ,qBACR,OAAQ,oBACd,CAAK,EACG,EAAEkI,EAAO,QAAUA,EAAO,QAAS,OACvC,IAAIsW,EAASL,EAAMjW,EAAO,MAAM,EAC5BuW,EAASN,EAAMjW,EAAO,MAAM,EAChC,OAAO,OAAOlI,EAAO,WAAY,CAC/B,OAAAwe,EACA,OAAAC,CACN,CAAK,EACDD,EAAS5b,EAAkB4b,CAAM,EACjCC,EAAS7b,EAAkB6b,CAAM,EACjC,MAAMG,EAAa,CAACrgB,EAAIgC,IAAQ,CAC1BhC,GACFA,EAAG,iBAAiB,QAASgC,IAAQ,OAASoe,EAAcD,CAAW,EAErE,CAAC1e,EAAO,SAAWzB,GACrBA,EAAG,UAAU,IAAI,GAAG2J,EAAO,UAAU,MAAM,GAAG,CAAC,CAEnD,EACAsW,EAAO,QAAQjgB,GAAMqgB,EAAWrgB,EAAI,MAAM,CAAC,EAC3CkgB,EAAO,QAAQlgB,GAAMqgB,EAAWrgB,EAAI,MAAM,CAAC,CAC7C,CACA,SAASqI,GAAU,CACjB,GAAI,CACF,OAAA4X,EACA,OAAAC,CACN,EAAQze,EAAO,WACXwe,EAAS5b,EAAkB4b,CAAM,EACjCC,EAAS7b,EAAkB6b,CAAM,EACjC,MAAMI,EAAgB,CAACtgB,EAAIgC,IAAQ,CACjChC,EAAG,oBAAoB,QAASgC,IAAQ,OAASoe,EAAcD,CAAW,EAC1EngB,EAAG,UAAU,OAAO,GAAGyB,EAAO,OAAO,WAAW,cAAc,MAAM,GAAG,CAAC,CAC1E,EACAwe,EAAO,QAAQjgB,GAAMsgB,EAActgB,EAAI,MAAM,CAAC,EAC9CkgB,EAAO,QAAQlgB,GAAMsgB,EAActgB,EAAI,MAAM,CAAC,CAChD,CACA2G,EAAG,OAAQ,IAAM,CACXlF,EAAO,OAAO,WAAW,UAAY,GAEvC8e,EAAO,GAEPpY,EAAI,EACJkJ,EAAM,EAEV,CAAC,EACD1K,EAAG,8BAA+B,IAAM,CACtC0K,EAAM,CACR,CAAC,EACD1K,EAAG,UAAW,IAAM,CAClB0B,EAAO,CACT,CAAC,EACD1B,EAAG,iBAAkB,IAAM,CACzB,GAAI,CACF,OAAAsZ,EACA,OAAAC,CACN,EAAQze,EAAO,WAGX,GAFAwe,EAAS5b,EAAkB4b,CAAM,EACjCC,EAAS7b,EAAkB6b,CAAM,EAC7Bze,EAAO,QAAS,CAClB4P,EAAM,EACN,MACF,CACA,CAAC,GAAG4O,EAAQ,GAAGC,CAAM,EAAE,OAAOlgB,GAAM,CAAC,CAACA,CAAE,EAAE,QAAQA,GAAMA,EAAG,UAAU,IAAIyB,EAAO,OAAO,WAAW,SAAS,CAAC,CAC9G,CAAC,EACDkF,EAAG,QAAS,CAAC6Z,EAAIlO,IAAM,CACrB,GAAI,CACF,OAAA2N,EACA,OAAAC,CACN,EAAQze,EAAO,WACXwe,EAAS5b,EAAkB4b,CAAM,EACjCC,EAAS7b,EAAkB6b,CAAM,EACjC,MAAMnI,EAAWzF,EAAE,OACnB,IAAImO,EAAiBP,EAAO,SAASnI,CAAQ,GAAKkI,EAAO,SAASlI,CAAQ,EAC1E,GAAItW,EAAO,WAAa,CAACgf,EAAgB,CACvC,MAAMvP,EAAOoB,EAAE,MAAQA,EAAE,cAAgBA,EAAE,aAAY,EACnDpB,IACFuP,EAAiBvP,EAAK,KAAKC,GAAU8O,EAAO,SAAS9O,CAAM,GAAK+O,EAAO,SAAS/O,CAAM,CAAC,EAE3F,CACA,GAAI1P,EAAO,OAAO,WAAW,aAAe,CAACgf,EAAgB,CAC3D,GAAIhf,EAAO,YAAcA,EAAO,OAAO,YAAcA,EAAO,OAAO,WAAW,YAAcA,EAAO,WAAW,KAAOsW,GAAYtW,EAAO,WAAW,GAAG,SAASsW,CAAQ,GAAI,OAC3K,IAAI2I,EACAT,EAAO,OACTS,EAAWT,EAAO,CAAC,EAAE,UAAU,SAASxe,EAAO,OAAO,WAAW,WAAW,EACnEye,EAAO,SAChBQ,EAAWR,EAAO,CAAC,EAAE,UAAU,SAASze,EAAO,OAAO,WAAW,WAAW,GAG5EmF,EADE8Z,IAAa,GACV,iBAEA,gBAFgB,EAIvB,CAAC,GAAGT,EAAQ,GAAGC,CAAM,EAAE,OAAOlgB,GAAM,CAAC,CAACA,CAAE,EAAE,QAAQA,GAAMA,EAAG,UAAU,OAAOyB,EAAO,OAAO,WAAW,WAAW,CAAC,CACnH,CACF,CAAC,EACD,MAAMkf,EAAS,IAAM,CACnBlf,EAAO,GAAG,UAAU,OAAO,GAAGA,EAAO,OAAO,WAAW,wBAAwB,MAAM,GAAG,CAAC,EACzF0G,EAAI,EACJkJ,EAAM,CACR,EACMkP,EAAU,IAAM,CACpB9e,EAAO,GAAG,UAAU,IAAI,GAAGA,EAAO,OAAO,WAAW,wBAAwB,MAAM,GAAG,CAAC,EACtF4G,EAAO,CACT,EACA,OAAO,OAAO5G,EAAO,WAAY,CAC/B,OAAAkf,EACA,QAAAJ,EACA,OAAAlP,EACA,KAAAlJ,EACA,QAAAE,CACJ,CAAG,CACH,CCrMA,SAASuY,EAAkBphB,EAAS,CAClC,OAAIA,IAAY,SACdA,EAAU,IAEL,IAAIA,EAAQ,KAAI,EAAG,QAAQ,oBAAqB,MAAM,EAC5D,QAAQ,KAAM,GAAG,CAAC,EACrB,CCFA,SAASqhB,GAAWrf,EAAM,CACxB,GAAI,CACF,OAAAC,EACA,aAAAmG,EACA,GAAAjB,EACA,KAAAC,CACJ,EAAMpF,EACJ,MAAMsf,EAAM,oBACZlZ,EAAa,CACX,WAAY,CACV,GAAI,KACJ,cAAe,OACf,UAAW,GACX,YAAa,GACb,aAAc,KACd,kBAAmB,KACnB,eAAgB,KAChB,aAAc,KACd,oBAAqB,GACrB,KAAM,UAEN,eAAgB,GAChB,mBAAoB,EACpB,sBAAuBmZ,GAAUA,EACjC,oBAAqBA,GAAUA,EAC/B,YAAa,GAAGD,CAAG,UACnB,kBAAmB,GAAGA,CAAG,iBACzB,cAAe,GAAGA,CAAG,IACrB,aAAc,GAAGA,CAAG,WACpB,WAAY,GAAGA,CAAG,SAClB,YAAa,GAAGA,CAAG,UACnB,qBAAsB,GAAGA,CAAG,oBAC5B,yBAA0B,GAAGA,CAAG,wBAChC,eAAgB,GAAGA,CAAG,aACtB,UAAW,GAAGA,CAAG,QACjB,gBAAiB,GAAGA,CAAG,cACvB,cAAe,GAAGA,CAAG,YACrB,wBAAyB,GAAGA,CAAG,WACrC,CACA,CAAG,EACDrf,EAAO,WAAa,CAClB,GAAI,KACJ,QAAS,CAAA,CACb,EACE,IAAIuf,EACAC,EAAqB,EACzB,SAASC,GAAuB,CAC9B,MAAO,CAACzf,EAAO,OAAO,WAAW,IAAM,CAACA,EAAO,WAAW,IAAM,MAAM,QAAQA,EAAO,WAAW,EAAE,GAAKA,EAAO,WAAW,GAAG,SAAW,CACzI,CACA,SAAS0f,EAAeC,EAAUC,EAAU,CAC1C,KAAM,CACJ,kBAAAC,CACN,EAAQ7f,EAAO,OAAO,WACb2f,IACLA,EAAWA,EAAS,GAAGC,IAAa,OAAS,WAAa,MAAM,gBAAgB,EAC5ED,IACFA,EAAS,UAAU,IAAI,GAAGE,CAAiB,IAAID,CAAQ,EAAE,EACzDD,EAAWA,EAAS,GAAGC,IAAa,OAAS,WAAa,MAAM,gBAAgB,EAC5ED,GACFA,EAAS,UAAU,IAAI,GAAGE,CAAiB,IAAID,CAAQ,IAAIA,CAAQ,EAAE,GAG3E,CACA,SAASE,EAAiBhN,EAAWvT,EAAWwgB,EAAQ,CAGtD,GAFAjN,EAAYA,EAAYiN,EACxBxgB,EAAYA,EAAYwgB,EACpBxgB,IAAcuT,EAAY,EAC5B,MAAO,OACF,GAAIvT,IAAcuT,EAAY,EACnC,MAAO,UAGX,CACA,SAASkN,EAAcnP,EAAG,CACxB,MAAM8O,EAAW9O,EAAE,OAAO,QAAQsO,EAAkBnf,EAAO,OAAO,WAAW,WAAW,CAAC,EACzF,GAAI,CAAC2f,EACH,OAEF9O,EAAE,eAAc,EAChB,MAAMrJ,EAAQnF,GAAasd,CAAQ,EAAI3f,EAAO,OAAO,eACrD,GAAIA,EAAO,OAAO,KAAM,CACtB,GAAIA,EAAO,YAAcwH,EAAO,OAChC,MAAMyY,EAAgBH,EAAiB9f,EAAO,UAAWwH,EAAOxH,EAAO,OAAO,MAAM,EAChFigB,IAAkB,OACpBjgB,EAAO,UAAS,EACPigB,IAAkB,WAC3BjgB,EAAO,UAAS,EAEhBA,EAAO,YAAYwH,CAAK,CAE5B,MACExH,EAAO,QAAQwH,CAAK,CAExB,CACA,SAASoI,GAAS,CAEhB,MAAMtH,EAAMtI,EAAO,IACbkI,EAASlI,EAAO,OAAO,WAC7B,GAAIyf,EAAoB,EAAI,OAC5B,IAAIlhB,EAAKyB,EAAO,WAAW,GAC3BzB,EAAKqE,EAAkBrE,CAAE,EAEzB,IAAIkC,EACAuO,EACJ,MAAMrG,EAAe3I,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAAUA,EAAO,QAAQ,OAAO,OAASA,EAAO,OAAO,OAC9GkgB,EAAQlgB,EAAO,OAAO,KAAO,KAAK,KAAK2I,EAAe3I,EAAO,OAAO,cAAc,EAAIA,EAAO,SAAS,OAY5G,GAXIA,EAAO,OAAO,MAChBgP,EAAgBhP,EAAO,mBAAqB,EAC5CS,EAAUT,EAAO,OAAO,eAAiB,EAAI,KAAK,MAAMA,EAAO,UAAYA,EAAO,OAAO,cAAc,EAAIA,EAAO,WACzG,OAAOA,EAAO,UAAc,KACrCS,EAAUT,EAAO,UACjBgP,EAAgBhP,EAAO,oBAEvBgP,EAAgBhP,EAAO,eAAiB,EACxCS,EAAUT,EAAO,aAAe,GAG9BkI,EAAO,OAAS,WAAalI,EAAO,WAAW,SAAWA,EAAO,WAAW,QAAQ,OAAS,EAAG,CAClG,MAAMmgB,EAAUngB,EAAO,WAAW,QAClC,IAAIogB,EACArN,EACAsN,EAsBJ,GArBInY,EAAO,iBACTqX,EAAa9c,GAAiB0d,EAAQ,CAAC,EAAGngB,EAAO,aAAY,EAAK,QAAU,QAAc,EAC1FzB,EAAG,QAAQggB,GAAS,CAClBA,EAAM,MAAMve,EAAO,aAAY,EAAK,QAAU,QAAQ,EAAI,GAAGuf,GAAcrX,EAAO,mBAAqB,EAAE,IAC3G,CAAC,EACGA,EAAO,mBAAqB,GAAK8G,IAAkB,SACrDwQ,GAAsB/e,GAAWuO,GAAiB,GAC9CwQ,EAAqBtX,EAAO,mBAAqB,EACnDsX,EAAqBtX,EAAO,mBAAqB,EACxCsX,EAAqB,IAC9BA,EAAqB,IAGzBY,EAAa,KAAK,IAAI3f,EAAU+e,EAAoB,CAAC,EACrDzM,EAAYqN,GAAc,KAAK,IAAID,EAAQ,OAAQjY,EAAO,kBAAkB,EAAI,GAChFmY,GAAYtN,EAAYqN,GAAc,GAExCD,EAAQ,QAAQR,GAAY,CAC1B,MAAMW,EAAkB,CAAC,GAAG,CAAC,GAAI,QAAS,aAAc,QAAS,aAAc,OAAO,EAAE,IAAIC,GAAU,GAAGrY,EAAO,iBAAiB,GAAGqY,CAAM,EAAE,CAAC,EAAE,IAAIxd,GAAK,OAAOA,GAAM,UAAYA,EAAE,SAAS,GAAG,EAAIA,EAAE,MAAM,GAAG,EAAIA,CAAC,EAAE,KAAI,EACzN4c,EAAS,UAAU,OAAO,GAAGW,CAAe,CAC9C,CAAC,EACG/hB,EAAG,OAAS,EACd4hB,EAAQ,QAAQK,GAAU,CACxB,MAAMC,EAAcpe,GAAame,CAAM,EACnCC,IAAgBhgB,EAClB+f,EAAO,UAAU,IAAI,GAAGtY,EAAO,kBAAkB,MAAM,GAAG,CAAC,EAClDlI,EAAO,WAChBwgB,EAAO,aAAa,OAAQ,QAAQ,EAElCtY,EAAO,iBACLuY,GAAeL,GAAcK,GAAe1N,GAC9CyN,EAAO,UAAU,IAAI,GAAG,GAAGtY,EAAO,iBAAiB,QAAQ,MAAM,GAAG,CAAC,EAEnEuY,IAAgBL,GAClBV,EAAec,EAAQ,MAAM,EAE3BC,IAAgB1N,GAClB2M,EAAec,EAAQ,MAAM,EAGnC,CAAC,MACI,CACL,MAAMA,EAASL,EAAQ1f,CAAO,EAS9B,GARI+f,GACFA,EAAO,UAAU,IAAI,GAAGtY,EAAO,kBAAkB,MAAM,GAAG,CAAC,EAEzDlI,EAAO,WACTmgB,EAAQ,QAAQ,CAACR,EAAUc,IAAgB,CACzCd,EAAS,aAAa,OAAQc,IAAgBhgB,EAAU,gBAAkB,QAAQ,CACpF,CAAC,EAECyH,EAAO,eAAgB,CACzB,MAAMwY,EAAuBP,EAAQC,CAAU,EACzCO,EAAsBR,EAAQpN,CAAS,EAC7C,QAAS3T,EAAIghB,EAAYhhB,GAAK2T,EAAW3T,GAAK,EACxC+gB,EAAQ/gB,CAAC,GACX+gB,EAAQ/gB,CAAC,EAAE,UAAU,IAAI,GAAG,GAAG8I,EAAO,iBAAiB,QAAQ,MAAM,GAAG,CAAC,EAG7EwX,EAAegB,EAAsB,MAAM,EAC3ChB,EAAeiB,EAAqB,MAAM,CAC5C,CACF,CACA,GAAIzY,EAAO,eAAgB,CACzB,MAAM0Y,EAAuB,KAAK,IAAIT,EAAQ,OAAQjY,EAAO,mBAAqB,CAAC,EAC7E2Y,GAAiBtB,EAAaqB,EAAuBrB,GAAc,EAAIc,EAAWd,EAClFuB,EAAaxY,EAAM,QAAU,OACnC6X,EAAQ,QAAQK,GAAU,CACxBA,EAAO,MAAMxgB,EAAO,aAAY,EAAK8gB,EAAa,KAAK,EAAI,GAAGD,CAAa,IAC7E,CAAC,CACH,CACF,CACAtiB,EAAG,QAAQ,CAACggB,EAAOwC,IAAe,CAShC,GARI7Y,EAAO,OAAS,aAClBqW,EAAM,iBAAiBY,EAAkBjX,EAAO,YAAY,CAAC,EAAE,QAAQ8Y,GAAc,CACnFA,EAAW,YAAc9Y,EAAO,sBAAsBzH,EAAU,CAAC,CACnE,CAAC,EACD8d,EAAM,iBAAiBY,EAAkBjX,EAAO,UAAU,CAAC,EAAE,QAAQ+Y,GAAW,CAC9EA,EAAQ,YAAc/Y,EAAO,oBAAoBgY,CAAK,CACxD,CAAC,GAEChY,EAAO,OAAS,cAAe,CACjC,IAAIgZ,EACAhZ,EAAO,oBACTgZ,EAAuBlhB,EAAO,aAAY,EAAK,WAAa,aAE5DkhB,EAAuBlhB,EAAO,aAAY,EAAK,aAAe,WAEhE,MAAMmhB,GAAS1gB,EAAU,GAAKyf,EAC9B,IAAIkB,EAAS,EACTC,EAAS,EACTH,IAAyB,aAC3BE,EAASD,EAETE,EAASF,EAEX5C,EAAM,iBAAiBY,EAAkBjX,EAAO,oBAAoB,CAAC,EAAE,QAAQoZ,GAAc,CAC3FA,EAAW,MAAM,UAAY,6BAA6BF,CAAM,YAAYC,CAAM,IAClFC,EAAW,MAAM,mBAAqB,GAAGthB,EAAO,OAAO,KAAK,IAC9D,CAAC,CACH,CACIkI,EAAO,OAAS,UAAYA,EAAO,cACrCrF,GAAa0b,EAAOrW,EAAO,aAAalI,EAAQS,EAAU,EAAGyf,CAAK,CAAC,EAC/Da,IAAe,GAAG5b,EAAK,mBAAoBoZ,CAAK,IAEhDwC,IAAe,GAAG5b,EAAK,mBAAoBoZ,CAAK,EACpDpZ,EAAK,mBAAoBoZ,CAAK,GAE5Bve,EAAO,OAAO,eAAiBA,EAAO,SACxCue,EAAM,UAAUve,EAAO,SAAW,MAAQ,QAAQ,EAAEkI,EAAO,SAAS,CAExE,CAAC,CACH,CACA,SAASqZ,GAAS,CAEhB,MAAMrZ,EAASlI,EAAO,OAAO,WAC7B,GAAIyf,EAAoB,EAAI,OAC5B,MAAM9W,EAAe3I,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAAUA,EAAO,QAAQ,OAAO,OAASA,EAAO,MAAQA,EAAO,OAAO,KAAK,KAAO,EAAIA,EAAO,OAAO,OAAS,KAAK,KAAKA,EAAO,OAAO,KAAK,IAAI,EAAIA,EAAO,OAAO,OAC7N,IAAIzB,EAAKyB,EAAO,WAAW,GAC3BzB,EAAKqE,EAAkBrE,CAAE,EACzB,IAAIijB,EAAiB,GACrB,GAAItZ,EAAO,OAAS,UAAW,CAC7B,IAAIuZ,EAAkBzhB,EAAO,OAAO,KAAO,KAAK,KAAK2I,EAAe3I,EAAO,OAAO,cAAc,EAAIA,EAAO,SAAS,OAChHA,EAAO,OAAO,UAAYA,EAAO,OAAO,SAAS,SAAWyhB,EAAkB9Y,IAChF8Y,EAAkB9Y,GAEpB,QAASvJ,EAAI,EAAGA,EAAIqiB,EAAiBriB,GAAK,EACpC8I,EAAO,aACTsZ,GAAkBtZ,EAAO,aAAa,KAAKlI,EAAQZ,EAAG8I,EAAO,WAAW,EAGxEsZ,GAAkB,IAAItZ,EAAO,aAAa,IAAIlI,EAAO,UAAY,gBAAkB,EAAE,WAAWkI,EAAO,WAAW,OAAOA,EAAO,aAAa,GAGnJ,CACIA,EAAO,OAAS,aACdA,EAAO,eACTsZ,EAAiBtZ,EAAO,eAAe,KAAKlI,EAAQkI,EAAO,aAAcA,EAAO,UAAU,EAE1FsZ,EAAiB,gBAAgBtZ,EAAO,YAAY,4BAAsCA,EAAO,UAAU,aAG3GA,EAAO,OAAS,gBACdA,EAAO,kBACTsZ,EAAiBtZ,EAAO,kBAAkB,KAAKlI,EAAQkI,EAAO,oBAAoB,EAElFsZ,EAAiB,gBAAgBtZ,EAAO,oBAAoB,aAGhElI,EAAO,WAAW,QAAU,CAAA,EAC5BzB,EAAG,QAAQggB,GAAS,CACdrW,EAAO,OAAS,UAClBrF,GAAa0b,EAAOiD,GAAkB,EAAE,EAEtCtZ,EAAO,OAAS,WAClBlI,EAAO,WAAW,QAAQ,KAAK,GAAGue,EAAM,iBAAiBY,EAAkBjX,EAAO,WAAW,CAAC,CAAC,CAEnG,CAAC,EACGA,EAAO,OAAS,UAClB/C,EAAK,mBAAoB5G,EAAG,CAAC,CAAC,CAElC,CACA,SAASmI,GAAO,CACd1G,EAAO,OAAO,WAAa+d,GAA0B/d,EAAQA,EAAO,eAAe,WAAYA,EAAO,OAAO,WAAY,CACvH,GAAI,mBACV,CAAK,EACD,MAAMkI,EAASlI,EAAO,OAAO,WAC7B,GAAI,CAACkI,EAAO,GAAI,OAChB,IAAI3J,EACA,OAAO2J,EAAO,IAAO,UAAYlI,EAAO,YAC1CzB,EAAKyB,EAAO,GAAG,cAAckI,EAAO,EAAE,GAEpC,CAAC3J,GAAM,OAAO2J,EAAO,IAAO,WAC9B3J,EAAK,CAAC,GAAG,SAAS,iBAAiB2J,EAAO,EAAE,CAAC,GAE1C3J,IACHA,EAAK2J,EAAO,IAEV,GAAC3J,GAAMA,EAAG,SAAW,KACrByB,EAAO,OAAO,mBAAqB,OAAOkI,EAAO,IAAO,UAAY,MAAM,QAAQ3J,CAAE,GAAKA,EAAG,OAAS,IACvGA,EAAK,CAAC,GAAGyB,EAAO,GAAG,iBAAiBkI,EAAO,EAAE,CAAC,EAE1C3J,EAAG,OAAS,IACdA,EAAKA,EAAG,KAAKggB,GACPhc,GAAegc,EAAO,SAAS,EAAE,CAAC,IAAMve,EAAO,EAEpD,IAGD,MAAM,QAAQzB,CAAE,GAAKA,EAAG,SAAW,IAAGA,EAAKA,EAAG,CAAC,GACnD,OAAO,OAAOyB,EAAO,WAAY,CAC/B,GAAAzB,CACN,CAAK,EACDA,EAAKqE,EAAkBrE,CAAE,EACzBA,EAAG,QAAQggB,GAAS,CACdrW,EAAO,OAAS,WAAaA,EAAO,WACtCqW,EAAM,UAAU,IAAI,IAAIrW,EAAO,gBAAkB,IAAI,MAAM,GAAG,CAAC,EAEjEqW,EAAM,UAAU,IAAIrW,EAAO,cAAgBA,EAAO,IAAI,EACtDqW,EAAM,UAAU,IAAIve,EAAO,aAAY,EAAKkI,EAAO,gBAAkBA,EAAO,aAAa,EACrFA,EAAO,OAAS,WAAaA,EAAO,iBACtCqW,EAAM,UAAU,IAAI,GAAGrW,EAAO,aAAa,GAAGA,EAAO,IAAI,UAAU,EACnEsX,EAAqB,EACjBtX,EAAO,mBAAqB,IAC9BA,EAAO,mBAAqB,IAG5BA,EAAO,OAAS,eAAiBA,EAAO,qBAC1CqW,EAAM,UAAU,IAAIrW,EAAO,wBAAwB,EAEjDA,EAAO,WACTqW,EAAM,iBAAiB,QAASyB,CAAa,EAE1ChgB,EAAO,SACVue,EAAM,UAAU,IAAIrW,EAAO,SAAS,CAExC,CAAC,EACH,CACA,SAAStB,GAAU,CACjB,MAAMsB,EAASlI,EAAO,OAAO,WAC7B,GAAIyf,EAAoB,EAAI,OAC5B,IAAIlhB,EAAKyB,EAAO,WAAW,GACvBzB,IACFA,EAAKqE,EAAkBrE,CAAE,EACzBA,EAAG,QAAQggB,GAAS,CAClBA,EAAM,UAAU,OAAOrW,EAAO,WAAW,EACzCqW,EAAM,UAAU,OAAOrW,EAAO,cAAgBA,EAAO,IAAI,EACzDqW,EAAM,UAAU,OAAOve,EAAO,aAAY,EAAKkI,EAAO,gBAAkBA,EAAO,aAAa,EACxFA,EAAO,YACTqW,EAAM,UAAU,OAAO,IAAIrW,EAAO,gBAAkB,IAAI,MAAM,GAAG,CAAC,EAClEqW,EAAM,oBAAoB,QAASyB,CAAa,EAEpD,CAAC,GAEChgB,EAAO,WAAW,SAASA,EAAO,WAAW,QAAQ,QAAQue,GAASA,EAAM,UAAU,OAAO,GAAGrW,EAAO,kBAAkB,MAAM,GAAG,CAAC,CAAC,CAC1I,CACAhD,EAAG,kBAAmB,IAAM,CAC1B,GAAI,CAAClF,EAAO,YAAc,CAACA,EAAO,WAAW,GAAI,OACjD,MAAMkI,EAASlI,EAAO,OAAO,WAC7B,GAAI,CACF,GAAAzB,CACN,EAAQyB,EAAO,WACXzB,EAAKqE,EAAkBrE,CAAE,EACzBA,EAAG,QAAQggB,GAAS,CAClBA,EAAM,UAAU,OAAOrW,EAAO,gBAAiBA,EAAO,aAAa,EACnEqW,EAAM,UAAU,IAAIve,EAAO,aAAY,EAAKkI,EAAO,gBAAkBA,EAAO,aAAa,CAC3F,CAAC,CACH,CAAC,EACDhD,EAAG,OAAQ,IAAM,CACXlF,EAAO,OAAO,WAAW,UAAY,GAEvC8e,EAAO,GAEPpY,EAAI,EACJ6a,EAAM,EACN3R,EAAM,EAEV,CAAC,EACD1K,EAAG,oBAAqB,IAAM,CACxB,OAAOlF,EAAO,UAAc,KAC9B4P,EAAM,CAEV,CAAC,EACD1K,EAAG,kBAAmB,IAAM,CAC1B0K,EAAM,CACR,CAAC,EACD1K,EAAG,uBAAwB,IAAM,CAC/Bqc,EAAM,EACN3R,EAAM,CACR,CAAC,EACD1K,EAAG,UAAW,IAAM,CAClB0B,EAAO,CACT,CAAC,EACD1B,EAAG,iBAAkB,IAAM,CACzB,GAAI,CACF,GAAA3G,CACN,EAAQyB,EAAO,WACPzB,IACFA,EAAKqE,EAAkBrE,CAAE,EACzBA,EAAG,QAAQggB,GAASA,EAAM,UAAUve,EAAO,QAAU,SAAW,KAAK,EAAEA,EAAO,OAAO,WAAW,SAAS,CAAC,EAE9G,CAAC,EACDkF,EAAG,cAAe,IAAM,CACtB0K,EAAM,CACR,CAAC,EACD1K,EAAG,QAAS,CAAC6Z,EAAIlO,IAAM,CACrB,MAAMyF,EAAWzF,EAAE,OACbtS,EAAKqE,EAAkB5C,EAAO,WAAW,EAAE,EACjD,GAAIA,EAAO,OAAO,WAAW,IAAMA,EAAO,OAAO,WAAW,aAAezB,GAAMA,EAAG,OAAS,GAAK,CAAC+X,EAAS,UAAU,SAAStW,EAAO,OAAO,WAAW,WAAW,EAAG,CACpK,GAAIA,EAAO,aAAeA,EAAO,WAAW,QAAUsW,IAAatW,EAAO,WAAW,QAAUA,EAAO,WAAW,QAAUsW,IAAatW,EAAO,WAAW,QAAS,OACnK,MAAMif,EAAW1gB,EAAG,CAAC,EAAE,UAAU,SAASyB,EAAO,OAAO,WAAW,WAAW,EAE5EmF,EADE8Z,IAAa,GACV,iBAEA,gBAFgB,EAIvB1gB,EAAG,QAAQggB,GAASA,EAAM,UAAU,OAAOve,EAAO,OAAO,WAAW,WAAW,CAAC,CAClF,CACF,CAAC,EACD,MAAMkf,EAAS,IAAM,CACnBlf,EAAO,GAAG,UAAU,OAAOA,EAAO,OAAO,WAAW,uBAAuB,EAC3E,GAAI,CACF,GAAAzB,CACN,EAAQyB,EAAO,WACPzB,IACFA,EAAKqE,EAAkBrE,CAAE,EACzBA,EAAG,QAAQggB,GAASA,EAAM,UAAU,OAAOve,EAAO,OAAO,WAAW,uBAAuB,CAAC,GAE9F0G,EAAI,EACJ6a,EAAM,EACN3R,EAAM,CACR,EACMkP,EAAU,IAAM,CACpB9e,EAAO,GAAG,UAAU,IAAIA,EAAO,OAAO,WAAW,uBAAuB,EACxE,GAAI,CACF,GAAAzB,CACN,EAAQyB,EAAO,WACPzB,IACFA,EAAKqE,EAAkBrE,CAAE,EACzBA,EAAG,QAAQggB,GAASA,EAAM,UAAU,IAAIve,EAAO,OAAO,WAAW,uBAAuB,CAAC,GAE3F4G,EAAO,CACT,EACA,OAAO,OAAO5G,EAAO,WAAY,CAC/B,OAAAkf,EACA,QAAAJ,EACA,OAAAyC,EACA,OAAA3R,EACA,KAAAlJ,EACA,QAAAE,CACJ,CAAG,CACH,CCrcA,SAAS8a,GAAS3hB,EAAM,CACtB,GAAI,CACF,OAAAC,EACA,aAAAmG,EACA,GAAAjB,EACA,KAAAC,EACA,OAAA+C,CACJ,EAAMnI,EACJC,EAAO,SAAW,CAChB,QAAS,GACT,OAAQ,GACR,SAAU,CACd,EACEmG,EAAa,CACX,SAAU,CACR,QAAS,GACT,MAAO,IACP,kBAAmB,GACnB,qBAAsB,GACtB,gBAAiB,GACjB,iBAAkB,GAClB,kBAAmB,EACzB,CACA,CAAG,EACD,IAAIwb,EACAC,EACAC,EAAqB3Z,GAAUA,EAAO,SAAWA,EAAO,SAAS,MAAQ,IACzE4Z,EAAuB5Z,GAAUA,EAAO,SAAWA,EAAO,SAAS,MAAQ,IAC3E6Z,EACAC,EAAoB,IAAI,KAAI,EAAG,QAAO,EACtCC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACJ,SAASC,EAAgB3R,EAAG,CACtB,CAAC7Q,GAAUA,EAAO,WAAa,CAACA,EAAO,WACvC6Q,EAAE,SAAW7Q,EAAO,YACxBA,EAAO,UAAU,oBAAoB,gBAAiBwiB,CAAe,EACjE,EAAAD,GAAwB1R,EAAE,QAAUA,EAAE,OAAO,oBAGjD4R,EAAM,EACR,CACA,MAAMC,EAAe,IAAM,CACzB,GAAI1iB,EAAO,WAAa,CAACA,EAAO,SAAS,QAAS,OAC9CA,EAAO,SAAS,OAClBiiB,EAAY,GACHA,IACTH,EAAuBC,EACvBE,EAAY,IAEd,MAAMU,EAAW3iB,EAAO,SAAS,OAAS+hB,EAAmBC,EAAoBF,EAAuB,IAAI,KAAI,EAAG,QAAO,EAC1H9hB,EAAO,SAAS,SAAW2iB,EAC3Bxd,EAAK,mBAAoBwd,EAAUA,EAAWd,CAAkB,EAChED,EAAM,sBAAsB,IAAM,CAChCc,EAAY,CACd,CAAC,CACH,EACME,EAAgB,IAAM,CAC1B,IAAIC,EAMJ,OALI7iB,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAC1C6iB,EAAgB7iB,EAAO,OAAO,KAAKsJ,GAAWA,EAAQ,UAAU,SAAS,qBAAqB,CAAC,EAE/FuZ,EAAgB7iB,EAAO,OAAOA,EAAO,WAAW,EAE7C6iB,EACqB,SAASA,EAAc,aAAa,sBAAsB,EAAG,EAAE,EADrE,MAGtB,EACMC,EAAMC,GAAc,CACxB,GAAI/iB,EAAO,WAAa,CAACA,EAAO,SAAS,QAAS,OAClD,qBAAqB4hB,CAAG,EACxBc,EAAY,EACZ,IAAItkB,EAAQ,OAAO2kB,EAAe,IAAc/iB,EAAO,OAAO,SAAS,MAAQ+iB,EAC/ElB,EAAqB7hB,EAAO,OAAO,SAAS,MAC5C8hB,EAAuB9hB,EAAO,OAAO,SAAS,MAC9C,MAAMgjB,EAAoBJ,EAAa,EACnC,CAAC,OAAO,MAAMI,CAAiB,GAAKA,EAAoB,GAAK,OAAOD,EAAe,MACrF3kB,EAAQ4kB,EACRnB,EAAqBmB,EACrBlB,EAAuBkB,GAEzBjB,EAAmB3jB,EACnB,MAAMoN,EAAQxL,EAAO,OAAO,MACtBijB,GAAU,IAAM,CAChB,CAACjjB,GAAUA,EAAO,YAClBA,EAAO,OAAO,SAAS,iBACrB,CAACA,EAAO,aAAeA,EAAO,OAAO,MAAQA,EAAO,OAAO,QAC7DA,EAAO,UAAUwL,EAAO,GAAM,EAAI,EAClCrG,EAAK,UAAU,GACLnF,EAAO,OAAO,SAAS,kBACjCA,EAAO,QAAQA,EAAO,OAAO,OAAS,EAAGwL,EAAO,GAAM,EAAI,EAC1DrG,EAAK,UAAU,GAGb,CAACnF,EAAO,OAASA,EAAO,OAAO,MAAQA,EAAO,OAAO,QACvDA,EAAO,UAAUwL,EAAO,GAAM,EAAI,EAClCrG,EAAK,UAAU,GACLnF,EAAO,OAAO,SAAS,kBACjCA,EAAO,QAAQ,EAAGwL,EAAO,GAAM,EAAI,EACnCrG,EAAK,UAAU,GAGfnF,EAAO,OAAO,UAChBgiB,EAAoB,IAAI,KAAI,EAAG,QAAO,EACtC,sBAAsB,IAAM,CAC1Bc,EAAG,CACL,CAAC,GAEL,EACA,OAAI1kB,EAAQ,GACV,aAAaujB,CAAO,EACpBA,EAAU,WAAW,IAAM,CACzBsB,GAAO,CACT,EAAG7kB,CAAK,GAER,sBAAsB,IAAM,CAC1B6kB,GAAO,CACT,CAAC,EAII7kB,CACT,EACM8kB,EAAQ,IAAM,CAClBlB,EAAoB,IAAI,KAAI,EAAG,QAAO,EACtChiB,EAAO,SAAS,QAAU,GAC1B8iB,EAAG,EACH3d,EAAK,eAAe,CACtB,EACMge,EAAO,IAAM,CACjBnjB,EAAO,SAAS,QAAU,GAC1B,aAAa2hB,CAAO,EACpB,qBAAqBC,CAAG,EACxBzc,EAAK,cAAc,CACrB,EACMie,EAAQ,CAAC1S,EAAU2S,IAAU,CACjC,GAAIrjB,EAAO,WAAa,CAACA,EAAO,SAAS,QAAS,OAClD,aAAa2hB,CAAO,EACfjR,IACH4R,EAAsB,IAExB,MAAMW,EAAU,IAAM,CACpB9d,EAAK,eAAe,EAChBnF,EAAO,OAAO,SAAS,kBACzBA,EAAO,UAAU,iBAAiB,gBAAiBwiB,CAAe,EAElEC,EAAM,CAEV,EAEA,GADAziB,EAAO,SAAS,OAAS,GACrBqjB,EAAO,CACLhB,IACFN,EAAmB/hB,EAAO,OAAO,SAAS,OAE5CqiB,EAAe,GACfY,EAAO,EACP,MACF,CAEAlB,GADcA,GAAoB/hB,EAAO,OAAO,SAAS,QAC7B,IAAI,KAAI,EAAG,QAAO,EAAKgiB,GAC/C,EAAAhiB,EAAO,OAAS+hB,EAAmB,GAAK,CAAC/hB,EAAO,OAAO,QACvD+hB,EAAmB,IAAGA,EAAmB,GAC7CkB,EAAO,EACT,EACMR,EAAS,IAAM,CACfziB,EAAO,OAAS+hB,EAAmB,GAAK,CAAC/hB,EAAO,OAAO,MAAQA,EAAO,WAAa,CAACA,EAAO,SAAS,UACxGgiB,EAAoB,IAAI,KAAI,EAAG,QAAO,EAClCM,GACFA,EAAsB,GACtBQ,EAAIf,CAAgB,GAEpBe,EAAG,EAEL9iB,EAAO,SAAS,OAAS,GACzBmF,EAAK,gBAAgB,EACvB,EACMme,EAAqB,IAAM,CAC/B,GAAItjB,EAAO,WAAa,CAACA,EAAO,SAAS,QAAS,OAClD,MAAMkD,EAAW3F,EAAW,EACxB2F,EAAS,kBAAoB,WAC/Bof,EAAsB,GACtBc,EAAM,EAAI,GAERlgB,EAAS,kBAAoB,WAC/Buf,EAAM,CAEV,EACMc,EAAiB1S,GAAK,CACtBA,EAAE,cAAgB,UACtByR,EAAsB,GACtBC,EAAuB,GACnB,EAAAviB,EAAO,WAAaA,EAAO,SAAS,SACxCojB,EAAM,EAAI,EACZ,EACMI,EAAiB3S,GAAK,CACtBA,EAAE,cAAgB,UACtB0R,EAAuB,GACnBviB,EAAO,SAAS,QAClByiB,EAAM,EAEV,EACMgB,EAAoB,IAAM,CAC1BzjB,EAAO,OAAO,SAAS,oBACzBA,EAAO,GAAG,iBAAiB,eAAgBujB,CAAc,EACzDvjB,EAAO,GAAG,iBAAiB,eAAgBwjB,CAAc,EAE7D,EACME,EAAoB,IAAM,CAC1B1jB,EAAO,IAAM,OAAOA,EAAO,IAAO,WACpCA,EAAO,GAAG,oBAAoB,eAAgBujB,CAAc,EAC5DvjB,EAAO,GAAG,oBAAoB,eAAgBwjB,CAAc,EAEhE,EACMG,EAAuB,IAAM,CAChBpmB,EAAW,EACnB,iBAAiB,mBAAoB+lB,CAAkB,CAClE,EACMM,EAAuB,IAAM,CAChBrmB,EAAW,EACnB,oBAAoB,mBAAoB+lB,CAAkB,CACrE,EACApe,EAAG,OAAQ,IAAM,CACXlF,EAAO,OAAO,SAAS,UACzByjB,EAAiB,EACjBE,EAAoB,EACpBT,EAAK,EAET,CAAC,EACDhe,EAAG,UAAW,IAAM,CAClBwe,EAAiB,EACjBE,EAAoB,EAChB5jB,EAAO,SAAS,SAClBmjB,EAAI,CAER,CAAC,EACDje,EAAG,yBAA0B,IAAM,EAC7Bid,GAAiBG,IACnBG,EAAM,CAEV,CAAC,EACDvd,EAAG,6BAA8B,IAAM,CAChClF,EAAO,OAAO,SAAS,qBAG1BmjB,EAAI,EAFJC,EAAM,GAAM,EAAI,CAIpB,CAAC,EACDle,EAAG,wBAAyB,CAAC6Z,EAAIvT,EAAOkF,IAAa,CAC/C1Q,EAAO,WAAa,CAACA,EAAO,SAAS,UACrC0Q,GAAY,CAAC1Q,EAAO,OAAO,SAAS,qBACtCojB,EAAM,GAAM,EAAI,EAEhBD,EAAI,EAER,CAAC,EACDje,EAAG,kBAAmB,IAAM,CAC1B,GAAI,EAAAlF,EAAO,WAAa,CAACA,EAAO,SAAS,SACzC,IAAIA,EAAO,OAAO,SAAS,qBAAsB,CAC/CmjB,EAAI,EACJ,MACF,CACAjB,EAAY,GACZC,EAAgB,GAChBG,EAAsB,GACtBF,EAAoB,WAAW,IAAM,CACnCE,EAAsB,GACtBH,EAAgB,GAChBiB,EAAM,EAAI,CACZ,EAAG,GAAG,EACR,CAAC,EACDle,EAAG,WAAY,IAAM,CACnB,GAAI,EAAAlF,EAAO,WAAa,CAACA,EAAO,SAAS,SAAW,CAACkiB,GAGrD,IAFA,aAAaE,CAAiB,EAC9B,aAAaT,CAAO,EAChB3hB,EAAO,OAAO,SAAS,qBAAsB,CAC/CmiB,EAAgB,GAChBD,EAAY,GACZ,MACF,CACIC,GAAiBniB,EAAO,OAAO,SAASyiB,EAAM,EAClDN,EAAgB,GAChBD,EAAY,GACd,CAAC,EACDhd,EAAG,cAAe,IAAM,CAClBlF,EAAO,WAAa,CAACA,EAAO,SAAS,UACzCqiB,EAAe,GACjB,CAAC,EACD,OAAO,OAAOriB,EAAO,SAAU,CAC7B,MAAAkjB,EACA,KAAAC,EACA,MAAAC,EACA,OAAAX,CACJ,CAAG,CACH,CCpSO,MAAMoB,GAAiB7iB,GAAsC,CAChE,GAAI,CACA,OAAO,SAAS,cAAcA,CAAQ,GAAK,EAC/C,MAAQ,CACJ,MAAO,EACX,CACJ,EAKa8iB,GAAoB9iB,GAA0C,CACvE,GAAI,CACA,OAAO,SAAS,iBAAiBA,CAAQ,CAC7C,MAAQ,CACJ,OAAO,SAAS,iBAAiB,EAAE,CACvC,CACJ,EAgBaW,EAAgB,CACzBoiB,EACAzd,EAA6B,CAAA,EAC7B0d,EAAY,KACE,CACd,GAAI,CACA,MAAMjjB,EAAU,SAAS,cAAcgjB,CAAO,EAG9C,SAAW,CAAC1mB,EAAKwd,CAAK,IAAK,OAAO,QAAQvU,CAAO,EACzCjJ,IAAQ,YACR0D,EAAQ,UAAY,OAAO8Z,CAAK,EACzBxd,IAAQ,KACf0D,EAAQ,GAAK,OAAO8Z,CAAK,EAEzB9Z,EAAQ,aAAa1D,EAAK,OAAOwd,CAAK,CAAC,EAI/C,OAAImJ,IACAjjB,EAAQ,UAAYijB,GAGjBjjB,CACX,MAAQ,CACJ,OAAO,SAAS,cAAc,KAAK,CACvC,CACJ,EAKakjB,EAAc,CAAC1iB,EAAiBe,IAAyB,CAClE,GAAI,CACAf,EAAO,YAAYe,CAAK,CAC5B,MAAQ,CAER,CACJ,EAKa4hB,GAAe,CAAC3iB,EAAiBe,IAAyB,CACnE,GAAI,CACAf,EAAO,QAAQe,CAAK,CACxB,MAAQ,CAER,CACJ,EAKa6hB,GAAiBpjB,GAA2B,CACrD,GAAI,CACAA,EAAQ,OAAA,CACZ,MAAQ,CAER,CACJ,EAKaqjB,EAAY,CAACrjB,EAAsBsjB,IAA+B,CAC3E,GAAI,CACA,SAAW,CAAC7H,EAAU3B,CAAK,IAAK,OAAO,QAAQwJ,CAAM,EACjDtjB,EAAQ,MAAM,YAAYyb,EAAU,OAAO3B,CAAK,CAAC,CAEzD,MAAQ,CAER,CACJ,EC9GayJ,GAAmB,CAC9B,wBAAyB,EACzB,yBAA0B,CAC5B,EAGaC,GAAiB,CAC5B,sBAAuB,GACvB,kBAAmB,IACnB,iBAAkB,EAClB,iBAAkB,GAClB,oBAAqB,IACrB,oBAAqB,IACrB,sBAAuB,EACvB,sBAAuB,EACzB,EAuBaC,GAAsB,CACjC,gBAAiB,EACjB,oBAAqB,CAEvB,EAYaC,GAAS,CACpB,eAAgB,GAChB,oBAAqB,IACrB,wBAAyB,GAC3B,EAGaC,GAAe,CAC1B,uBAAwB,oBACxB,eAAgB,yBAClB,EAGaC,GAAc,CAOzB,UAAW,UACb,EAGaC,GAAgB,CAI3B,qBAAsB,qBACtB,sBAAuB,sBACvB,sBAAuB,qBACzB,EAKaC,GAAmB,CAC9B,GAAI,kCACJ,mBAAoB,kCACpB,WAAY,GACZ,gBAAiB,0CACnB,EC3FaC,GAAiB,IAAe,CACzC,GAAI,CACA,KAAM,CAAE,UAAAvhB,GAAc,UAKtB,OAJwBA,EAAU,UAC9B+gB,GAAiB,wBACjBA,GAAiB,wBAAA,IAEM,MAC/B,MAAQ,CACJ,MAAO,EACX,CACJ,ECXaS,EAA4B,CACvC,IAAM,aACN,IAAK,CACH,YAAaF,GAAiB,GAC9B,kBAAmBA,GAAiB,kBAAA,EAEtC,OAAQ,CACN,UAAWA,GAAiB,WAC5B,sBAAuBJ,GAAO,wBAC9B,UAAWA,GAAO,eAClB,kBAAmBA,GAAO,oBAC1B,IAAK,CACH,YAAaC,GAAa,uBAC1B,YAAaC,GAAY,SAAA,EAE3B,OAAQ,CACN,aAAc,GACd,OAAQ,QACR,eAAgB,GAChB,cAAe,OACf,WAAY,CACV,GAAIC,GAAc,qBAClB,KAAM,SAAA,EAER,WAAY,CACV,OAAQA,GAAc,sBACtB,OAAQA,GAAc,qBAAA,CACxB,CACF,EAEF,GAAI,CACF,aAAcF,GAAa,eAC3B,cAAeG,GAAiB,eAAA,CAEpC,EC/BMG,GAA0B,CAC5B,yBAA0B,EAC1B,gCAAiC,CACrC,EAOO,MAAMC,EAAiB,CAAvB,aAAA,CAGH,KAAiB,UAAYF,EAAc,OAAO,UAClD,KAAiB,UAAYA,EAAc,OAAO,SAAA,CAK1C,kBAAkB1nB,EAAsB,CAC5C,GAAI,CACA,MAAM6nB,EAAQC,GAAOA,EAAI,MACnBC,EAASF,GAASA,EAAM,UAC9B,OAAI,OAAOE,GAAW,WACXA,EAAO,KAAKF,EAAO7nB,CAAG,EAEjC,MACJ,MAAQ,CACJ,MACJ,CACJ,CAKA,sBAAsBgoB,EAA0B,CAC5C,GAAI,CACA,KAAK,QAAA,EAEL,MAAMC,EAAY,KAAK,gBAAA,EACjBtlB,EAAS,KAAK,oBAAoBslB,CAAS,EAC3CC,EAAU,KAAK,oBAAoBvlB,CAAM,EAEzCwlB,EAAa,KAAK,eAAeD,CAAO,EAC9C,KAAK,iBAAiBvlB,CAAM,EAC5B,KAAK,iBAAiBA,CAAM,EAE5B,KAAK,UAAYslB,EACjB,KAAK,YAAYA,CAAS,EAG1B,WAAW,IAAM,CACb,KAAK,iBAAiB,KAAK,kBAAA,EAAqBE,CAAU,CAC9D,EAAG,KAAK,SAAS,CACrB,MAAQ,CAER,CACJ,CAKQ,0BAAiC,CACrC,MAAMC,EAAoBC,GAAuB,IAAIX,EAAc,OAAO,IAAI,WAAW,EAAE,EACvFU,GACAE,GAAuBF,CAAiB,EAG5C,MAAMG,EAAcC,GAA0B,WAAW,EACzD,UAAW9kB,KAAW6kB,EAClBD,GAAuB5kB,CAAO,CAEtC,CAMQ,iBAA+B,CACnC,KAAK,yBAAA,EAEL,MAAMukB,EAAYQ,EAAuB,MAAO,CAC5C,GAAIf,EAAc,OAAO,IAAI,YAC7B,UAAW,aAAA,CACd,EAGD,OAAAO,EAAU,MAAM,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAWnBA,CACX,CASQ,oBAAoBA,EAAqC,CAC7D,MAAMtlB,EAAS8lB,EAAuB,MAAO,CACzC,UAAW,UAAUf,EAAc,OAAO,IAAI,WAAW,EAAA,CAC5D,EAGD,IAAIrf,EAAS,QACb,OAAIof,OACApf,EAAS,SAIb1F,EAAO,MAAM,QAAU;AAAA;AAAA,sBAET0F,CAAM;AAAA;AAAA;AAAA,0BAGFA,CAAM;AAAA;AAAA;AAAA;AAAA;AAAA,UAOxBqgB,EAAqBT,EAAWtlB,CAAM,EAC/BA,CACX,CAOQ,oBAAoBA,EAAkC,CAC1D,MAAMulB,EAAUO,EAAuB,MAAO,CAC1C,UAAW,gBAAA,CACd,EAGDE,OAAAA,EAAmBT,EAAS,CACxB,MAAS,OACT,OAAU,OACV,QAAW,MAAA,CACd,EAEDQ,EAAqB/lB,EAAQulB,CAAO,EAC7BA,CACX,CAMQ,mBAA4B,CAChC,MAAMU,EAAiB,KAAK,kBAAkB,gDAAgD,EAC9F,OAAIA,EACO,OAAO,SAAS,OAAOA,CAAc,EAAG,EAAE,EAE9ClB,EAAc,OAAO,qBAChC,CAOQ,eAAeQ,EAA8B,CACjD,IAAIC,EAAa,EACjB,QAAS9a,EAAa8Z,GAAoB,oBAAqB9Z,GAAc,KAAK,UAAWA,GAAc8Z,GAAoB,gBAAiB,CAC5I,MAAM0B,EAAW,KAAK,kBAAkB,wCAAwCxb,CAAU,EAAE,EACtFyb,EAAY,KAAK,kBAAkB,uCAAuCzb,CAAU,EAAE,EAE5F,GAAIwb,EAAU,CACV,MAAMxc,EAAQ,KAAK,YAAY,OAAOwc,CAAQ,EAAG,OAAOC,GAAa,EAAE,CAAC,EACxEJ,EAAqBR,EAAS7b,CAAK,EACnC8b,GAAchB,GAAoB,eACtC,CACJ,CACA,OAAOgB,CACX,CAQQ,YAAYU,EAAkBC,EAAgC,CAClE,MAAMzc,EAAQoc,EAAuB,MAAO,CACxC,UAAW,cAAA,CACd,EAGDE,EAAmBtc,EAAO,CACtB,MAAS,OACT,OAAU,OACV,QAAW,OACX,cAAe,SACf,kBAAmB,QAAA,CACtB,EAED,IAAI0c,EAAe,GACnB,OAAID,IACAC,EAAe,yBAAyBD,CAAS,KAIrDzc,EAAM,UAAY,iBAAiB0c,CAAY,UAAUF,CAAQ,6EAE1Dxc,CACX,CAMQ,iBAAiB1J,EAA2B,CAChD,MAAMqmB,EAAaP,EAAuB,MAAO,CAC7C,UAAW,mBAAA,CACd,EACDC,EAAqB/lB,EAAQqmB,CAAU,CAC3C,CAMQ,iBAAiBrmB,EAA2B,CAChD,MAAMsmB,EAAaR,EAAuB,MAAO,CAC7C,UAAW,oBAAA,CACd,EACKS,EAAaT,EAAuB,MAAO,CAC7C,UAAW,oBAAA,CACd,EAGDE,EAAmBM,EAAY,CAC3B,SAAY,WACZ,IAAO,MACP,KAAQ,OACR,UAAa,mBACb,UAAW,MACX,MAAS,OACT,OAAU,OACV,WAAc,wBACd,MAAS,OACT,OAAU,4BACV,gBAAiB,MACjB,OAAU,UACV,QAAW,OACX,cAAe,SACf,kBAAmB,SACnB,YAAa,OACb,cAAe,OACf,aAAc,6BACd,WAAc,eAAA,CACjB,EAEDN,EAAmBO,EAAY,CAC3B,SAAY,WACZ,IAAO,MACP,MAAS,OACT,UAAa,mBACb,UAAW,MACX,MAAS,OACT,OAAU,OACV,WAAc,wBACd,MAAS,OACT,OAAU,4BACV,gBAAiB,MACjB,OAAU,UACV,QAAW,OACX,cAAe,SACf,kBAAmB,SACnB,YAAa,OACb,cAAe,OACf,aAAc,6BACd,WAAc,eAAA,CACjB,EAGDD,EAAW,iBAAiB,aAAc,IAAM,CAC5CN,EAAmBM,EAAY,CAC3B,WAAc,sBACd,UAAa,6BAAA,CAChB,CACL,CAAC,EAEDA,EAAW,iBAAiB,aAAc,IAAM,CAC5CN,EAAmBM,EAAY,CAC3B,WAAc,wBACd,UAAa,2BAAA,CAChB,CACL,CAAC,EAEDC,EAAW,iBAAiB,aAAc,IAAM,CAC5CP,EAAmBO,EAAY,CAC3B,WAAc,sBACd,UAAa,6BAAA,CAChB,CACL,CAAC,EAEDA,EAAW,iBAAiB,aAAc,IAAM,CAC5CP,EAAmBO,EAAY,CAC3B,WAAc,wBACd,UAAa,2BAAA,CAChB,CACL,CAAC,EAEDR,EAAqB/lB,EAAQsmB,CAAU,EACvCP,EAAqB/lB,EAAQumB,CAAU,CAC3C,CAMQ,YAAYjB,EAA8B,CAC9C,MAAMkB,EAAmBd,GAAuB,qBAAqB,EACjEc,GACAC,GAAsBD,EAAkBlB,CAAS,CAEzD,CAOQ,iBAAiBW,EAAwBT,EAA0B,CACvE,GAAI,CAEA,MAAMkB,EAAe,KAAK,sBAAsBlB,CAAU,EAE1D,KAAK,OAAS,IAAItJ,EAAO,IAAI6I,EAAc,OAAO,IAAI,WAAW,GAAI,CACjE,KAAM2B,EAAa,WACnB,SAAU,CACN,MAAOT,EACP,qBAAsB,EAAA,EAE1B,aAAclB,EAAc,OAAO,OAAO,aAC1C,OAAQA,EAAc,OAAO,OAAO,OACpC,eAAgB2B,EAAa,eAC7B,cAAeA,EAAa,cAC5B,WAAY,CACR,GAAI3B,EAAc,OAAO,OAAO,WAAW,GAC3C,KAAMA,EAAc,OAAO,OAAO,WAAW,KAC7C,UAAW,EAAA,EAEf,WAAY,CACR,OAAQA,EAAc,OAAO,OAAO,WAAW,OAC/C,OAAQA,EAAc,OAAO,OAAO,WAAW,MAAA,EAEnD,aAAc,EACd,QAAS,CAAC7G,GAAYkB,GAAYsC,EAAQ,CAAA,CAC7C,CACL,MAAQ,CAER,CACJ,CAOQ,sBAAsB8D,EAI5B,CAKE,OAAIA,GAAcR,GAAwB,yBAE/B,CACH,WAAY,GACZ,cAAe,OACf,eAAgB,EAAA,EAEbQ,GAAcR,GAAwB,gCAEtC,CACH,WAAY,GACZ,cAAe,EACf,eAAgB,EAAA,EAIb,CACH,WAAY,GACZ,cAAe,EACf,eAAgB,EAAA,CAG5B,CAKA,SAAgB,CACR,KAAK,SACL,KAAK,OAAO,QAAQ,GAAM,EAAI,EAC9B,OAAO,KAAK,QAGZ,KAAK,YACLW,GAAuB,KAAK,SAAS,EACrC,OAAO,KAAK,UAEpB,CACJ,CC3aO,MAAMgB,CAAa,CAKd,aAAc,CAHtB,KAAQ,SAA4B,CAAA,EACpC,KAAQ,cAAgB,EAIxB,CAKA,OAAc,aAA4B,CACtC,OAAKA,EAAa,WACdA,EAAa,SAAW,IAAIA,GAEzBA,EAAa,QACxB,CAKO,YAAsB,CACzB,GAAI,CACA,OAAI,KAAK,gBAKT,KAAK,yBAAA,EACL,KAAK,cAAgB,IACd,EACX,MAAQ,CACJ,MAAO,EACX,CACJ,CAKO,WAAoBC,EAAmBjf,EAAkC,CAC5E,GAAI,CACA,OAAOif,EAAA,CACX,OAASC,EAAO,CACZ,YAAK,SAASA,EAAgBlf,CAAO,EAC9B,EACX,CACJ,CAKO,YAAqBif,EAA4Bjf,EAA2C,CAC/F,OAAOif,EAAA,EAAK,MAAOC,IACf,KAAK,SAASA,EAAgBlf,CAAO,EAC9B,GACV,CACL,CAKQ,SAASkf,EAAclf,EAAuB,CAClD,GAAI,CACA,MAAMmf,EAAuB,CACzB,cAAe,KACf,MAAAD,EACA,QAAAlf,CAAA,EAGJ,KAAK,SAAS,KAAKmf,CAAK,EAGpB,KAAK,SAAS,OAASvC,GAAe,uBACtC,KAAK,SAAS,MAAA,CAQtB,MAAQ,CAER,CACJ,CAKQ,0BAAiC,CACrC,GAAI,CAEA,WAAW,iBAAiB,qBAAuBpd,GAAU,CACzD,KAAK,SACD,IAAI,MAAM,OAAOA,EAAM,MAAM,CAAC,EAC9B,6BAAA,CAER,CAAC,CACL,MAAQ,CAER,CACJ,CAKO,aAA+B,CAClC,MAAO,CAAC,GAAG,KAAK,QAAQ,CAC5B,CAKO,eAAsB,CACzB,KAAK,SAAW,CAAA,CACpB,CACJ,CCrHO,MAAM4f,CAAc,CAGf,aAAc,CAEtB,CAKA,OAAc,aAA6B,CACvC,OAAKA,EAAc,WACfA,EAAc,SAAW,IAAIA,GAE1BA,EAAc,QACzB,CAKO,YAAsB,CACzB,GAAI,CAEA,OADqB5B,EAAI,QAAQ,IAAI,WAAW,IACxB,MAC5B,MAAQ,CAEJ,GAAI,CACA,OAAO,WAAW,SAAS,SAAS,SAAS,OAAO,CACxD,MAAQ,CACJ,MAAO,EACX,CACJ,CACJ,CAKO,WAAkC,CACrC,OAAOJ,CACX,CAKO,uBAAiC,CACpC,GAAI,CAIA,QAASra,EAAa,EAAmBA,GAAcqa,EAAc,OAAO,UAAWra,GAAc,EAEjG,GADcya,EAAI,MAAM,UAAU,wCAAwCza,CAAU,EAAE,EAElF,MAAO,GAGf,MAAO,EACX,MAAQ,CACJ,MAAO,EACX,CACJ,CACJ,CCrDAya,EAAI,aAAa,IAAIJ,EAAc,IAAI,YAAa,IAAM,CACtD,MAAMiC,EAAeL,EAAa,YAAA,EAC5BM,EAAgBF,EAAc,YAAA,EAGpC,GAAI,CAACC,EAAa,aACd,OAGJ,MAAME,EAAmB,IAAIjC,GAG7BhoB,GAAAA,OAAOkqB,GAAc,UAAW,OAAQ,SAAoCC,EAAgB,CACxFJ,EAAa,WAAW,IAAM,CACtBC,EAAc,cACdI,GAAoBD,EAAOF,CAA2B,CAE9D,EAAG,8BAA8B,CACrC,CAAC,CACL,CAAC,EAKD,MAAMG,GAAsB,CACxBD,EACAF,EACAI,IACO,CACP,GAAI,CAIA,GAHsBP,EAAc,YAAA,EAGlB,wBACd,GAAI,CACAG,EAAiB,sBAAsBE,CAAK,CAChD,MAAQ,CAER,CAMCjC,EAAI,QAAQ,MACboC,GAAA,CAGR,MAAQ,CAER,CACJ,EAOMA,GAAgB,IAAY,CAC9B,IAAIC,EAAsB,SAAS,eAAezC,EAAc,GAAG,YAAY,EAE/E,GAAIyC,IAAwB,KAAM,CAE9B,MAAMC,EAAgBtC,EAAI,MAAM,UAAU,+CAA+C,GAAKJ,EAAc,GAAG,cAE/GyC,EAAsB,SAAS,cAAc,KAAK,EAClDA,EAAoB,GAAKzC,EAAc,GAAG,aAC1CyC,EAAoB,MAAM,QAAU,eACpCA,EAAoB,MAAM,UAAY,MACtCA,EAAoB,UAAY,aAAaC,CAAa,6BAE1D,MAAMC,EAAc,SAAS,cAAc,kCAAkC,EACzEA,GAAeA,EAAY,YAC3BA,EAAY,WAAW,OAAOF,CAAmB,CAEzD,CACJ", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7]}