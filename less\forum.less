/* Header Advertisement Extension Styles */

/* Advertisement Container */
.adContainer {
  margin: 0 -20% 20px -20%;
  position: relative !important;
}

/* Swiper Advertisement Styles */
.adSwiper {
  width: 100%;
  height: 300px;
  position: relative;
}

.adSwiper .swiper-wrapper {
  height: 100%;
}

.adSwiper .swiper-slide {
  text-align: center;
  font-size: 18px;
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 12px;
  overflow: hidden;
}

.adSwiper .swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.adSwiper .swiper-slide img:hover {
  transform: scale(1.02);
}

/* Swiper Navigation */
.adSwiper .swiper-button-next,
.adSwiper .swiper-button-prev {
  color: #fff;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 40px;
  height: 40px;
}

.adSwiper .swiper-button-next:after,
.adSwiper .swiper-button-prev:after {
  font-size: 16px;
}

/* Swiper Pagination */
.adSwiper .swiper-pagination {
  bottom: 10px;
}

.adSwiper .swiper-pagination-bullet {
  background: rgba(255, 255, 255, 0.7);
  opacity: 1;
}

.adSwiper .swiper-pagination-bullet-active {
  background: #fff;
}

/* Header Icon */
#wusong8899HeaderAdvIcon {
  display: inline-block;
  margin-top: 8px;
}

#wusong8899HeaderAdvIcon img {
  height: 24px;
  transition: opacity 0.2s ease;
}

#wusong8899HeaderAdvIcon img:hover {
  opacity: 0.8;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .adContainer {
    width: 120%; /* More reasonable for mobile */
    margin: 0 -10% 20px -10%; /* Adjusted margins for mobile */
  }

  .adSwiper {
    height: 200px;
  }

  .adSwiper .swiper-button-next,
  .adSwiper .swiper-button-prev {
    width: 30px;
    height: 30px;
  }

  .adSwiper .swiper-button-next:after,
  .adSwiper .swiper-button-prev:after {
    font-size: 12px;
  }

  #wusong8899HeaderAdvIcon img {
    height: 20px;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .adContainer {
    width: 110%; /* Conservative increase for very small screens */
    margin: 0 -5% 20px -5%;
  }
}
