/* Header Advertisement Extension Styles */

/* Advertisement Container */
.adContainer {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto 20px auto;
  position: relative !important;
  overflow: hidden;
}

/* Swiper Advertisement Styles */
.adSwiper {
  width: 100%;
  height: 300px;
  position: relative;
}

.adSwiper .swiper-wrapper {
  height: 100%;
}

.adSwiper .swiper-slide {
  text-align: center;
  font-size: 18px;
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 12px;
  overflow: hidden;
  /* Let Swiper calculate width automatically for partial slide display */
}

.adSwiper .swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.adSwiper .swiper-slide img:hover {
  transform: scale(1.02);
}

/* Partial slide effects for better visual hierarchy */
.adSwiper .swiper-slide {
  transition: opacity 0.3s ease, transform 0.3s ease;
  opacity: 0.7;
  transform: scale(0.95);
}

.adSwiper .swiper-slide-active {
  opacity: 1;
  transform: scale(1);
}

.adSwiper .swiper-slide-next,
.adSwiper .swiper-slide-prev {
  opacity: 0.8;
  transform: scale(0.98);
}

/* Swiper Navigation */
.adSwiper .swiper-button-next,
.adSwiper .swiper-button-prev {
  color: #fff;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 40px;
  height: 40px;
}

.adSwiper .swiper-button-next:after,
.adSwiper .swiper-button-prev:after {
  font-size: 16px;
}

/* Swiper Pagination */
.adSwiper .swiper-pagination {
  bottom: 10px;
}

.adSwiper .swiper-pagination-bullet {
  background: rgba(255, 255, 255, 0.7);
  opacity: 1;
}

.adSwiper .swiper-pagination-bullet-active {
  background: #fff;
}

/* Header Icon */
.HeaderIcon-container {
  display: inline-flex;
  align-items: center;
  margin-left: 12px;
  vertical-align: middle;
}

.HeaderIcon-image {
  height: 24px;
  width: auto;
  transition: opacity 0.2s ease, transform 0.2s ease;
  border-radius: 4px;
}

.HeaderIcon-image:hover {
  opacity: 0.8;
  transform: scale(1.05);
}

/* Ensure proper alignment within navigation */
#app-navigation .App-backControl {
  display: flex;
  align-items: center;
}

/* Legacy support for existing ID */
#wusong8899HeaderAdvIcon {
  display: inline-flex;
  align-items: center;
  margin-left: 12px;
  vertical-align: middle;
}

#wusong8899HeaderAdvIcon img {
  height: 24px;
  width: auto;
  transition: opacity 0.2s ease, transform 0.2s ease;
  border-radius: 4px;
}

#wusong8899HeaderAdvIcon img:hover {
  opacity: 0.8;
  transform: scale(1.05);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .adContainer {
    max-width: 100%;
    width: 95%;
    margin: 0 auto 20px auto;
    padding: 0 10px;
  }

  .adSwiper {
    height: 200px;
  }

  /* Adjust partial slide display for mobile */
  .adSwiper .swiper-slide {
    opacity: 0.6;
    transform: scale(0.9);
  }

  .adSwiper .swiper-slide-active {
    opacity: 1;
    transform: scale(1);
  }

  .adSwiper .swiper-button-next,
  .adSwiper .swiper-button-prev {
    width: 30px;
    height: 30px;
  }

  .adSwiper .swiper-button-next:after,
  .adSwiper .swiper-button-prev:after {
    font-size: 12px;
  }

  #wusong8899HeaderAdvIcon img {
    height: 20px;
  }

  .HeaderIcon-container {
    margin-left: 8px;
  }

  .HeaderIcon-image {
    height: 20px;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .adContainer {
    max-width: 100%;
    width: 90%;
    margin: 0 auto 20px auto;
    padding: 0 5px;
  }
}
