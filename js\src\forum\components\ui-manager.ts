import app from 'flarum/forum/app';

/**
 * UI Manager for handling social media buttons and other UI components
 */
export class UIManager {

    /**
     * Add social media buttons to the page
     */
    addSocialMediaButtons(): void {
        // This method can be used to add social media buttons if needed
        // Currently, social media functionality is handled elsewhere
    }

    /**
     * Create social buttons HTML
     */
    createSocialButtonsHTML(): string {
        const extensionId = 'wusong8899-header-advertisement';

        const socialPlatforms = [
            { name: 'Kick', icon: 'fas fa-play', defaultUrl: 'https://kick.com' },
            { name: 'Facebook', icon: 'fab fa-facebook', defaultUrl: 'https://facebook.com' },
            { name: 'Twitter', icon: 'fab fa-twitter', defaultUrl: 'https://twitter.com' },
            { name: 'YouTube', icon: 'fab fa-youtube', defaultUrl: 'https://youtube.com' },
            { name: 'Instagram', icon: 'fab fa-instagram', defaultUrl: 'https://instagram.com' }
        ];

        const _buttons = socialPlatforms.map(platform => {
            const url = app.forum.attribute(`${extensionId}.Social${platform.name}Url`) || platform.defaultUrl;
            const icon = app.forum.attribute(`${extensionId}.Social${platform.name}Icon`) || platform.icon;

            return `
                <div style="display: inline-block; margin-right: 10px;">
                    <a href="${url}" target="_blank" style="color: #666; text-decoration: none;">
                        <i class="${icon}" style="font-size: 18px;"></i>
                    </a>
                </div>
            `;
        }).join('');

        return `
            <div style="display: flex; align-items: center; font-weight: bold; font-size: 14px; margin-top: 10px;">
                <img style="width:22px;" src="https://i.mji.rip/2025/08/15/102ee6e187aa177ddfe02364dc82208d.png">&nbsp;&nbsp;沙县团饭指定战略合作伙伴托管机构
            </div>
            <div style="padding-top: 10px; position: relative">
                <div class="UserSubmissionApplicationInput" style="position: absolute; height: 37px; width: 100%; z-index: 1;"></div>
                <div style="width:100%" class="Search-input">
                    <input disabled="" style="width: 100%; font-size:12px;" class="FormControl" type="search" placeholder="请输入论坛推荐公司 游戏账号以及收款方式">
                </div>
            </div>
        `;
    }

}
