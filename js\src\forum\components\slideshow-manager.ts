import Swiper from 'swiper';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import app from 'flarum/forum/app';
import * as DOMUtils from '../utils/dom-utils';
import { isMobileDevice } from '../utils/mobile-detection';
import { defaultConfig } from '../../common/config';
import { SLIDESHOW_CONSTANTS } from '../../common/config/constants';
import type { FlarumVnode } from '../../common/config/types';

/**
 * Swiper configuration constants
 */
const SWIPER_CONFIG_CONSTANTS = {
    MIN_SLIDES_FOR_DUAL_VIEW: 8,
    MIN_SLIDES_FOR_SINGLE_VIEW_LOOP: 4,
} as const;



/**
 * Slideshow manager for header advertisements
 */
export class SlideshowManager {
    private swiper: Swiper | undefined;
    private container: HTMLElement | undefined;
    private readonly maxSlides = defaultConfig.slider.maxSlides;
    private readonly checkTime = defaultConfig.slider.checkTime;

    /**
     * Safely read a forum attribute if available
     */
    private getForumAttribute(key: string): unknown {
        try {
            const forum = app && app.forum;
            const attrFn = forum && forum.attribute;
            if (typeof attrFn === 'function') {
                return attrFn.call(forum, key);
            }
            return;
        } catch {
            return;
        }
    }

    /**
     * Initialize and attach slideshow to the DOM
     */
    attachAdvertiseHeader(_vdom: FlarumVnode): void {
        try {
            this.destroy(); // Clean up any existing instance

            const container = this.createContainer();
            const swiper = this.createSwiperElement(container);
            const wrapper = this.createSwiperWrapper(swiper);

            const slideCount = this.populateSlides(wrapper);
            this.createPagination(swiper);
            this.createNavigation(swiper);

            this.container = container;
            this.appendToDOM(container);

            // Initialize Swiper after DOM attachment with slide count
            setTimeout(() => {
                this.initializeSwiper(this.getTransitionTime(), slideCount);
            }, this.checkTime);
        } catch {
            // Silently handle slideshow creation errors
        }
    }

    /**
     * Remove existing navigation elements
     */
    private removeExistingNavigation(): void {
        const existingContainer = DOMUtils.querySelector(`#${defaultConfig.slider.dom.containerId}`);
        if (existingContainer) {
            DOMUtils.removeElement(existingContainer);
        }

        const navElements = DOMUtils.querySelectorAll(".item-nav");
        for (const element of navElements) {
            DOMUtils.removeElement(element);
        }
    }

    /**
     * Create main container element
     * @returns Container element
     */
    private createContainer(): HTMLElement {
        this.removeExistingNavigation();

        const container = DOMUtils.createElement('div', {
            id: defaultConfig.slider.dom.containerId,
            className: 'adContainer'
        });

        // Force essential container styles with !important to ensure visibility
        container.style.cssText = `
            display: block !important;
            min-height: 300px !important;
            background: transparent !important;
            border: none !important;
            padding: 0 !important;
            max-width: 1200px !important;
            width: 100% !important;
            margin: 0 auto 20px auto !important;
            overflow: hidden !important;
        `;
        return container;
    }



    /**
     * Create Swiper element
     * @param {HTMLElement} container - Parent container
     * @returns {HTMLElement} Swiper element
     */
    private createSwiperElement(container: HTMLElement): HTMLElement {
        const swiper = DOMUtils.createElement('div', {
            className: `swiper ${defaultConfig.slider.dom.swiperClass}`
        });

        // Add inline styles to ensure visibility with !important
        let height = '300px';
        if (isMobileDevice()) {
            height = '200px';
        }

        // Force styles with !important to override any conflicting CSS
        swiper.style.cssText = `
            width: 100% !important;
            height: ${height} !important;
            position: relative !important;
            display: block !important;
            min-height: ${height} !important;
            background: transparent !important;
            border: none !important;
            border-radius: 12px !important;
            overflow: hidden !important;
        `;

        DOMUtils.appendChild(container, swiper);
        return swiper;
    }

    /**
     * Create Swiper wrapper
     * @param {HTMLElement} swiper - Swiper element
     * @returns {HTMLElement} Wrapper element
     */
    private createSwiperWrapper(swiper: HTMLElement): HTMLElement {
        const wrapper = DOMUtils.createElement('div', {
            className: 'swiper-wrapper'
        });

        // Add inline styles to ensure wrapper visibility
        DOMUtils.setStyles(wrapper, {
            'width': '100%',
            'height': '100%',
            'display': 'flex'
        });

        DOMUtils.appendChild(swiper, wrapper);
        return wrapper;
    }

    /**
     * Get transition time from forum settings
     * @returns Transition time in milliseconds
     */
    private getTransitionTime(): number {
        const transitionTime = this.getForumAttribute('wusong8899-header-advertisement.TransitionTime');
        if (transitionTime) {
            return Number.parseInt(String(transitionTime), 10);
        }
        return defaultConfig.slider.defaultTransitionTime;
    }

    /**
     * Populate slides with data from forum settings
     * @param {HTMLElement} wrapper - Swiper wrapper element
     * @returns {number} Number of slides created
     */
    private populateSlides(wrapper: HTMLElement): number {
        let slideCount = 0;
        for (let slideIndex = SLIDESHOW_CONSTANTS.INITIAL_SLIDE_INDEX; slideIndex <= this.maxSlides; slideIndex += SLIDESHOW_CONSTANTS.SLIDE_INCREMENT) {
            const imageSrc = this.getForumAttribute(`wusong8899-header-advertisement.Image${slideIndex}`);
            const imageLink = this.getForumAttribute(`wusong8899-header-advertisement.Link${slideIndex}`);

            if (imageSrc) {
                const slide = this.createSlide(String(imageSrc), String(imageLink || ''));
                DOMUtils.appendChild(wrapper, slide);
                slideCount += SLIDESHOW_CONSTANTS.SLIDE_INCREMENT;
            }
        }
        return slideCount;
    }

    /**
     * Create individual slide
     * @param {string} imageSrc - Image source URL
     * @param {string} imageLink - Link URL
     * @returns {HTMLElement} Slide element
     */
    private createSlide(imageSrc: string, imageLink: string): HTMLElement {
        const slide = DOMUtils.createElement('div', {
            className: 'swiper-slide'
        });

        // Add inline styles to ensure slide visibility
        DOMUtils.setStyles(slide, {
            'width': '100%',
            'height': '100%',
            'display': 'flex',
            'align-items': 'center',
            'justify-content': 'center'
        });

        let clickHandler = '';
        if (imageLink) {
            clickHandler = `window.location.href="${imageLink}"`;
        }

        // Create image with proper styling
        slide.innerHTML = `<img onclick='${clickHandler}' src='${imageSrc}' style='width: 100%; height: 100%; object-fit: cover; display: block;' />`;

        return slide;
    }

    /**
     * Create pagination element
     * @param {HTMLElement} swiper - Swiper element
     */
    private createPagination(swiper: HTMLElement): void {
        const pagination = DOMUtils.createElement('div', {
            className: 'swiper-pagination'
        });
        DOMUtils.appendChild(swiper, pagination);
    }

    /**
     * Create navigation elements
     * @param {HTMLElement} swiper - Swiper element
     */
    private createNavigation(swiper: HTMLElement): void {
        const prevButton = DOMUtils.createElement('div', {
            className: 'swiper-button-prev'
        });
        const nextButton = DOMUtils.createElement('div', {
            className: 'swiper-button-next'
        });

        // Style navigation buttons
        DOMUtils.setStyles(prevButton, {
            'position': 'absolute',
            'top': '50%',
            'left': '15px',
            'transform': 'translateY(-50%)',
            'z-index': '100',
            'width': '40px',
            'height': '40px',
            'background': 'rgba(255,255,255,0.9)',
            'color': '#333',
            'border': '1px solid rgba(0,0,0,0.1)',
            'border-radius': '50%',
            'cursor': 'pointer',
            'display': 'flex',
            'align-items': 'center',
            'justify-content': 'center',
            'font-size': '18px',
            'font-weight': 'bold',
            'box-shadow': '0 2px 8px rgba(0,0,0,0.15)',
            'transition': 'all 0.3s ease'
        });

        DOMUtils.setStyles(nextButton, {
            'position': 'absolute',
            'top': '50%',
            'right': '15px',
            'transform': 'translateY(-50%)',
            'z-index': '100',
            'width': '40px',
            'height': '40px',
            'background': 'rgba(255,255,255,0.9)',
            'color': '#333',
            'border': '1px solid rgba(0,0,0,0.1)',
            'border-radius': '50%',
            'cursor': 'pointer',
            'display': 'flex',
            'align-items': 'center',
            'justify-content': 'center',
            'font-size': '18px',
            'font-weight': 'bold',
            'box-shadow': '0 2px 8px rgba(0,0,0,0.15)',
            'transition': 'all 0.3s ease'
        });

        // Add hover effects
        prevButton.addEventListener('mouseenter', () => {
            DOMUtils.setStyles(prevButton, {
                'background': 'rgba(255,255,255,1)',
                'transform': 'translateY(-50%) scale(1.1)'
            });
        });

        prevButton.addEventListener('mouseleave', () => {
            DOMUtils.setStyles(prevButton, {
                'background': 'rgba(255,255,255,0.9)',
                'transform': 'translateY(-50%) scale(1)'
            });
        });

        nextButton.addEventListener('mouseenter', () => {
            DOMUtils.setStyles(nextButton, {
                'background': 'rgba(255,255,255,1)',
                'transform': 'translateY(-50%) scale(1.1)'
            });
        });

        nextButton.addEventListener('mouseleave', () => {
            DOMUtils.setStyles(nextButton, {
                'background': 'rgba(255,255,255,0.9)',
                'transform': 'translateY(-50%) scale(1)'
            });
        });

        DOMUtils.appendChild(swiper, prevButton);
        DOMUtils.appendChild(swiper, nextButton);
    }

    /**
     * Append slideshow to DOM
     * @param {HTMLElement} container - Container element
     */
    private appendToDOM(container: HTMLElement): void {
        const contentContainer = DOMUtils.querySelector("#content .container");
        if (contentContainer) {
            DOMUtils.prependChild(contentContainer, container);
        }
    }

    /**
     * Initialize Swiper instance
     * @param {number} transitionTime - Transition time in milliseconds
     * @param {number} slideCount - Number of slides available
     */
    private initializeSwiper(transitionTime: number, slideCount: number): void {
        try {
            // Calculate optimal configuration based on slide count
            const swiperConfig = this.calculateSwiperConfig(slideCount);

            this.swiper = new Swiper(`.${defaultConfig.slider.dom.swiperClass}`, {
                loop: swiperConfig.enableLoop,
                autoplay: {
                    delay: transitionTime,
                    disableOnInteraction: false,
                },
                spaceBetween: defaultConfig.slider.swiper.spaceBetween,
                effect: defaultConfig.slider.swiper.effect,
                centeredSlides: swiperConfig.centeredSlides,
                slidesPerView: swiperConfig.slidesPerView,
                // Responsive breakpoints for partial slide display
                breakpoints: {
                    // Mobile devices
                    320: {
                        slidesPerView: 1.2,
                        spaceBetween: 15,
                    },
                    // Tablets
                    768: {
                        slidesPerView: 1.3,
                        spaceBetween: 18,
                    },
                    // Desktop
                    1024: {
                        slidesPerView: swiperConfig.slidesPerView,
                        spaceBetween: defaultConfig.slider.swiper.spaceBetween,
                    },
                },
                pagination: {
                    el: defaultConfig.slider.swiper.pagination.el,
                    type: defaultConfig.slider.swiper.pagination.type,
                    clickable: true,
                },
                navigation: {
                    nextEl: defaultConfig.slider.swiper.navigation.nextEl,
                    prevEl: defaultConfig.slider.swiper.navigation.prevEl,
                },
                initialSlide: 0,
                modules: [Navigation, Pagination, Autoplay]
            });
        } catch {
            // Silently handle Swiper initialization errors
        }
    }

    /**
     * Calculate optimal Swiper configuration based on slide count
     * @param {number} slideCount - Number of available slides
     * @returns {object} Swiper configuration object
     */
    private calculateSwiperConfig(slideCount: number): {
        enableLoop: boolean;
        slidesPerView: number | 'auto';
        centeredSlides: boolean;
    } {
        // Swiper loop mode requirements:
        // - For slidesPerView: 1.5, need at least 5-6 slides for smooth loop
        // - For slidesPerView: 1, need at least 3-4 slides for smooth loop

        if (slideCount >= SWIPER_CONFIG_CONSTANTS.MIN_SLIDES_FOR_DUAL_VIEW) {
            // Enough slides for 1.5 view with loop - shows partial prev/next slides
            return {
                enableLoop: true,
                slidesPerView: 1.5,
                centeredSlides: true
            };
        } else if (slideCount >= SWIPER_CONFIG_CONSTANTS.MIN_SLIDES_FOR_SINGLE_VIEW_LOOP) {
            // Use single slide view with loop for better control
            return {
                enableLoop: true,
                slidesPerView: 1,
                centeredSlides: true
            };
        } else {
            // Too few slides for loop mode
            return {
                enableLoop: false,
                slidesPerView: 1,
                centeredSlides: true
            };
        }
    }

    /**
     * Destroy slideshow instance
     */
    destroy(): void {
        if (this.swiper) {
            this.swiper.destroy(true, true);
            delete this.swiper;
        }

        if (this.container) {
            DOMUtils.removeElement(this.container);
            delete this.container;
        }
    }
}
