import { extend } from 'flarum/common/extend';
import app from 'flarum/forum/app';
import HeaderPrimary from 'flarum/forum/components/HeaderPrimary';

import { SlideshowManager } from './components/slideshow-manager';
import { UIManager } from './components/ui-manager';
import { ErrorHandler } from './utils/error-handler';
import { ConfigManager } from './utils/config-manager';
import { defaultConfig } from '../common/config';

/**
 * Main extension initializer for Header Advertisement
 */
app.initializers.add(defaultConfig.app.extensionId, () => {
    const errorHandler = ErrorHandler.getInstance();
    const configManager = ConfigManager.getInstance();

    // Initialize error handling
    if (!errorHandler.initialize()) {
        return;
    }

    const slideshowManager = new SlideshowManager();
    const uiManager = new UIManager();

    extend(HeaderPrimary.prototype, 'view', function headerPrimaryViewExtension(vnode: unknown) {
        errorHandler.handleSync(() => {
            if (configManager.isTagsPage()) {
                initializeExtension(vnode, slideshowManager, uiManager);
            }
        }, 'HeaderPrimary view extension');
    });
});

/**
 * Initialize extension components
 */
const initializeExtension = (
    vnode: unknown,
    slideshowManager: SlideshowManager,
    _uiManager: UIManager
): void => {
    try {
        const configManager = ConfigManager.getInstance();

        // Setup slideshow (only if configured)
        if (configManager.isSlideshowConfigured()) {
            try {
                slideshowManager.attachAdvertiseHeader(vnode);
            } catch {
                // Slideshow setup failed, but continue with other features
            }
        }

        // UI Manager is available for future use if needed

        // Add header icon for non-logged users
        if (!app.session.user) {
            addHeaderIcon();
        }

    } catch {
        // Silently handle initialization errors
    }
}



/**
 * Add header icon for branding
 */
const addHeaderIcon = (): void => {
    let headerIconContainer = document.getElementById(defaultConfig.ui.headerIconId);

    if (headerIconContainer === null) {
        // Get header icon URL from settings, fallback to default config
        const headerIconUrl = app.forum.attribute('wusong8899-header-advertisement.HeaderIconUrl') || defaultConfig.ui.headerIconUrl;

        headerIconContainer = document.createElement("div");
        headerIconContainer.id = defaultConfig.ui.headerIconId;
        headerIconContainer.className = 'HeaderIcon-container';
        headerIconContainer.innerHTML = `<img src="${headerIconUrl}" alt="Header Icon" class="HeaderIcon-image" />`;

        // Find the navigation drawer button and insert the icon after it
        const navigationButton = document.querySelector("#app-navigation .App-backControl .Button.Button--icon.Navigation-drawer");
        if (navigationButton && navigationButton.parentNode) {
            // Insert after the navigation button
            navigationButton.parentNode.insertBefore(headerIconContainer, navigationButton.nextSibling);
        }
    }
}
