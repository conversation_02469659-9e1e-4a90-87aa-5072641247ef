import { extend } from 'flarum/common/extend';
import app from 'flarum/forum/app';
import HeaderPrimary from 'flarum/forum/components/HeaderPrimary';

import { SlideshowManager } from './components/slideshow-manager';
import { UIManager } from './components/ui-manager';
import { ErrorHandler } from './utils/error-handler';
import { ConfigManager } from './utils/config-manager';
import { isMobileDevice } from './utils/mobile-detection';
import { defaultConfig } from '../common/config';

/**
 * Main extension initializer for Header Advertisement
 */
app.initializers.add(defaultConfig.app.extensionId, () => {
    const errorHandler = ErrorHandler.getInstance();
    const configManager = ConfigManager.getInstance();

    // Initialize error handling
    if (!errorHandler.initialize()) {
        return;
    }

    const slideshowManager = new SlideshowManager();
    const uiManager = new UIManager();

    extend(HeaderPrimary.prototype, 'view', function headerPrimaryViewExtension(vnode: unknown) {
        errorHandler.handleSync(() => {
            if (configManager.isTagsPage()) {
                initializeExtension(vnode, slideshowManager, uiManager);
            }
        }, 'HeaderPrimary view extension');
    });
});

/**
 * Initialize extension components
 */
const initializeExtension = (
    vnode: unknown,
    slideshowManager: SlideshowManager,
    _uiManager: UIManager
): void => {
    try {
        const configManager = ConfigManager.getInstance();

        // Setup slideshow (only if configured)
        if (configManager.isSlideshowConfigured()) {
            try {
                slideshowManager.attachAdvertiseHeader(vnode);
            } catch {
                // Slideshow setup failed, but continue with other features
            }
        }

        // UI Manager is available for future use if needed

        // Add header icon for non-logged users on mobile devices only
        if (!app.session.user && isMobileDevice()) {
            addHeaderIcon();
        }

    } catch {
        // Silently handle initialization errors
    }
}



/**
 * Add header icon for branding (mobile only)
 */
const addHeaderIcon = (): void => {
    let headerIconContainer = document.getElementById(defaultConfig.ui.headerIconId);

    if (headerIconContainer === null) {
        // Get header icon URL from settings, fallback to default config
        const headerIconUrl = app.forum.attribute('wusong8899-header-advertisement.HeaderIconUrl') || defaultConfig.ui.headerIconUrl;

        headerIconContainer = document.createElement("div");
        headerIconContainer.id = defaultConfig.ui.headerIconId;
        headerIconContainer.className = 'HeaderIcon-container mobile-only';
        headerIconContainer.innerHTML = `<img src="${headerIconUrl}" alt="Header Icon" class="HeaderIcon-image" />`;

        // Find the navigation container (.App-backControl) and insert the icon as the last child
        const navigationContainer = document.querySelector("#app-navigation .Navigation.ButtonGroup.App-backControl");
        if (navigationContainer) {
            // Insert as the last child of the navigation container (to the right of the drawer button)
            navigationContainer.appendChild(headerIconContainer);
        }
    }
}
