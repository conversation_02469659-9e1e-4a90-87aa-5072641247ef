(function(He,q,We){"use strict";function be(i){return i!==null&&typeof i=="object"&&"constructor"in i&&i.constructor===Object}function oe(i,e){i===void 0&&(i={}),e===void 0&&(e={});const t=["__proto__","constructor","prototype"];Object.keys(e).filter(s=>t.indexOf(s)<0).forEach(s=>{typeof i[s]>"u"?i[s]=e[s]:be(e[s])&&be(i[s])&&Object.keys(e[s]).length>0&&oe(i[s],e[s])})}const Ee={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function F(){const i=typeof document<"u"?document:{};return oe(i,Ee),i}const je={document:Ee,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(i){return typeof setTimeout>"u"?(i(),null):setTimeout(i,0)},cancelAnimationFrame(i){typeof setTimeout>"u"||clearTimeout(i)}};function B(){const i=typeof window<"u"?window:{};return oe(i,je),i}function qe(i){return i===void 0&&(i=""),i.trim().split(" ").filter(e=>!!e.trim())}function Xe(i){const e=i;Object.keys(e).forEach(t=>{try{e[t]=null}catch{}try{delete e[t]}catch{}})}function xe(i,e){return e===void 0&&(e=0),setTimeout(i,e)}function Z(){return Date.now()}function Ye(i){const e=B();let t;return e.getComputedStyle&&(t=e.getComputedStyle(i,null)),!t&&i.currentStyle&&(t=i.currentStyle),t||(t=i.style),t}function Ue(i,e){e===void 0&&(e="x");const t=B();let s,n,r;const l=Ye(i);return t.WebKitCSSMatrix?(n=l.transform||l.webkitTransform,n.split(",").length>6&&(n=n.split(", ").map(a=>a.replace(",",".")).join(", ")),r=new t.WebKitCSSMatrix(n==="none"?"":n)):(r=l.MozTransform||l.OTransform||l.MsTransform||l.msTransform||l.transform||l.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),s=r.toString().split(",")),e==="x"&&(t.WebKitCSSMatrix?n=r.m41:s.length===16?n=parseFloat(s[12]):n=parseFloat(s[4])),e==="y"&&(t.WebKitCSSMatrix?n=r.m42:s.length===16?n=parseFloat(s[13]):n=parseFloat(s[5])),n||0}function ee(i){return typeof i=="object"&&i!==null&&i.constructor&&Object.prototype.toString.call(i).slice(8,-1)==="Object"}function Ke(i){return typeof window<"u"&&typeof window.HTMLElement<"u"?i instanceof HTMLElement:i&&(i.nodeType===1||i.nodeType===11)}function V(){const i=Object(arguments.length<=0?void 0:arguments[0]),e=["__proto__","constructor","prototype"];for(let t=1;t<arguments.length;t+=1){const s=t<0||arguments.length<=t?void 0:arguments[t];if(s!=null&&!Ke(s)){const n=Object.keys(Object(s)).filter(r=>e.indexOf(r)<0);for(let r=0,l=n.length;r<l;r+=1){const a=n[r],d=Object.getOwnPropertyDescriptor(s,a);d!==void 0&&d.enumerable&&(ee(i[a])&&ee(s[a])?s[a].__swiper__?i[a]=s[a]:V(i[a],s[a]):!ee(i[a])&&ee(s[a])?(i[a]={},s[a].__swiper__?i[a]=s[a]:V(i[a],s[a])):i[a]=s[a])}}}return i}function te(i,e,t){i.style.setProperty(e,t)}function Ie(i){let{swiper:e,targetPosition:t,side:s}=i;const n=B(),r=-e.translate;let l=null,a;const d=e.params.speed;e.wrapperEl.style.scrollSnapType="none",n.cancelAnimationFrame(e.cssModeFrameID);const o=t>r?"next":"prev",u=(h,g)=>o==="next"&&h>=g||o==="prev"&&h<=g,m=()=>{a=new Date().getTime(),l===null&&(l=a);const h=Math.max(Math.min((a-l)/d,1),0),g=.5-Math.cos(h*Math.PI)/2;let p=r+g*(t-r);if(u(p,t)&&(p=t),e.wrapperEl.scrollTo({[s]:p}),u(p,t)){e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.scrollSnapType="",setTimeout(()=>{e.wrapperEl.style.overflow="",e.wrapperEl.scrollTo({[s]:p})}),n.cancelAnimationFrame(e.cssModeFrameID);return}e.cssModeFrameID=n.requestAnimationFrame(m)};m()}function H(i,e){e===void 0&&(e="");const t=B(),s=[...i.children];return t.HTMLSlotElement&&i instanceof HTMLSlotElement&&s.push(...i.assignedElements()),e?s.filter(n=>n.matches(e)):s}function Qe(i,e){const t=[e];for(;t.length>0;){const s=t.shift();if(i===s)return!0;t.push(...s.children,...s.shadowRoot?s.shadowRoot.children:[],...s.assignedElements?s.assignedElements():[])}}function Je(i,e){const t=B();let s=e.contains(i);return!s&&t.HTMLSlotElement&&e instanceof HTMLSlotElement&&(s=[...e.assignedElements()].includes(i),s||(s=Qe(i,e))),s}function ie(i){try{console.warn(i);return}catch{}}function se(i,e){e===void 0&&(e=[]);const t=document.createElement(i);return t.classList.add(...Array.isArray(e)?e:qe(e)),t}function Ze(i,e){const t=[];for(;i.previousElementSibling;){const s=i.previousElementSibling;e?s.matches(e)&&t.push(s):t.push(s),i=s}return t}function et(i,e){const t=[];for(;i.nextElementSibling;){const s=i.nextElementSibling;e?s.matches(e)&&t.push(s):t.push(s),i=s}return t}function W(i,e){return B().getComputedStyle(i,null).getPropertyValue(e)}function re(i){let e=i,t;if(e){for(t=0;(e=e.previousSibling)!==null;)e.nodeType===1&&(t+=1);return t}}function Ce(i,e){const t=[];let s=i.parentElement;for(;s;)e?s.matches(e)&&t.push(s):t.push(s),s=s.parentElement;return t}function de(i,e,t){const s=B();return i[e==="width"?"offsetWidth":"offsetHeight"]+parseFloat(s.getComputedStyle(i,null).getPropertyValue(e==="width"?"margin-right":"margin-top"))+parseFloat(s.getComputedStyle(i,null).getPropertyValue(e==="width"?"margin-left":"margin-bottom"))}function k(i){return(Array.isArray(i)?i:[i]).filter(e=>!!e)}function Me(i,e){e===void 0&&(e=""),typeof trustedTypes<"u"?i.innerHTML=trustedTypes.createPolicy("html",{createHTML:t=>t}).createHTML(e):i.innerHTML=e}let ce;function tt(){const i=B(),e=F();return{smoothScroll:e.documentElement&&e.documentElement.style&&"scrollBehavior"in e.documentElement.style,touch:!!("ontouchstart"in i||i.DocumentTouch&&e instanceof i.DocumentTouch)}}function Le(){return ce||(ce=tt()),ce}let ue;function it(i){let{userAgent:e}=i===void 0?{}:i;const t=Le(),s=B(),n=s.navigator.platform,r=e||s.navigator.userAgent,l={ios:!1,android:!1},a=s.screen.width,d=s.screen.height,o=r.match(/(Android);?[\s\/]+([\d.]+)?/);let u=r.match(/(iPad).*OS\s([\d_]+)/);const m=r.match(/(iPod)(.*OS\s([\d_]+))?/),h=!u&&r.match(/(iPhone\sOS|iOS)\s([\d_]+)/),g=n==="Win32";let p=n==="MacIntel";const v=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"];return!u&&p&&t.touch&&v.indexOf(`${a}x${d}`)>=0&&(u=r.match(/(Version)\/([\d.]+)/),u||(u=[0,1,"13_0_0"]),p=!1),o&&!g&&(l.os="android",l.android=!0),(u||h||m)&&(l.os="ios",l.ios=!0),l}function Pe(i){return i===void 0&&(i={}),ue||(ue=it(i)),ue}let fe;function st(){const i=B(),e=Pe();let t=!1;function s(){const a=i.navigator.userAgent.toLowerCase();return a.indexOf("safari")>=0&&a.indexOf("chrome")<0&&a.indexOf("android")<0}if(s()){const a=String(i.navigator.userAgent);if(a.includes("Version/")){const[d,o]=a.split("Version/")[1].split(" ")[0].split(".").map(u=>Number(u));t=d<16||d===16&&o<2}}const n=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(i.navigator.userAgent),r=s(),l=r||n&&e.ios;return{isSafari:t||r,needPerspectiveFix:t,need3dFix:l,isWebView:n}}function Ae(){return fe||(fe=st()),fe}function rt(i){let{swiper:e,on:t,emit:s}=i;const n=B();let r=null,l=null;const a=()=>{!e||e.destroyed||!e.initialized||(s("beforeResize"),s("resize"))},d=()=>{!e||e.destroyed||!e.initialized||(r=new ResizeObserver(m=>{l=n.requestAnimationFrame(()=>{const{width:h,height:g}=e;let p=h,v=g;m.forEach(b=>{let{contentBoxSize:S,contentRect:c,target:f}=b;f&&f!==e.el||(p=c?c.width:(S[0]||S).inlineSize,v=c?c.height:(S[0]||S).blockSize)}),(p!==h||v!==g)&&a()})}),r.observe(e.el))},o=()=>{l&&n.cancelAnimationFrame(l),r&&r.unobserve&&e.el&&(r.unobserve(e.el),r=null)},u=()=>{!e||e.destroyed||!e.initialized||s("orientationchange")};t("init",()=>{if(e.params.resizeObserver&&typeof n.ResizeObserver<"u"){d();return}n.addEventListener("resize",a),n.addEventListener("orientationchange",u)}),t("destroy",()=>{o(),n.removeEventListener("resize",a),n.removeEventListener("orientationchange",u)})}function nt(i){let{swiper:e,extendParams:t,on:s,emit:n}=i;const r=[],l=B(),a=function(u,m){m===void 0&&(m={});const h=l.MutationObserver||l.WebkitMutationObserver,g=new h(p=>{if(e.__preventObserver__)return;if(p.length===1){n("observerUpdate",p[0]);return}const v=function(){n("observerUpdate",p[0])};l.requestAnimationFrame?l.requestAnimationFrame(v):l.setTimeout(v,0)});g.observe(u,{attributes:typeof m.attributes>"u"?!0:m.attributes,childList:e.isElement||(typeof m.childList>"u"?!0:m).childList,characterData:typeof m.characterData>"u"?!0:m.characterData}),r.push(g)},d=()=>{if(e.params.observer){if(e.params.observeParents){const u=Ce(e.hostEl);for(let m=0;m<u.length;m+=1)a(u[m])}a(e.hostEl,{childList:e.params.observeSlideChildren}),a(e.wrapperEl,{attributes:!1})}},o=()=>{r.forEach(u=>{u.disconnect()}),r.splice(0,r.length)};t({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",d),s("destroy",o)}var at={on(i,e,t){const s=this;if(!s.eventsListeners||s.destroyed||typeof e!="function")return s;const n=t?"unshift":"push";return i.split(" ").forEach(r=>{s.eventsListeners[r]||(s.eventsListeners[r]=[]),s.eventsListeners[r][n](e)}),s},once(i,e,t){const s=this;if(!s.eventsListeners||s.destroyed||typeof e!="function")return s;function n(){s.off(i,n),n.__emitterProxy&&delete n.__emitterProxy;for(var r=arguments.length,l=new Array(r),a=0;a<r;a++)l[a]=arguments[a];e.apply(s,l)}return n.__emitterProxy=e,s.on(i,n,t)},onAny(i,e){const t=this;if(!t.eventsListeners||t.destroyed||typeof i!="function")return t;const s=e?"unshift":"push";return t.eventsAnyListeners.indexOf(i)<0&&t.eventsAnyListeners[s](i),t},offAny(i){const e=this;if(!e.eventsListeners||e.destroyed||!e.eventsAnyListeners)return e;const t=e.eventsAnyListeners.indexOf(i);return t>=0&&e.eventsAnyListeners.splice(t,1),e},off(i,e){const t=this;return!t.eventsListeners||t.destroyed||!t.eventsListeners||i.split(" ").forEach(s=>{typeof e>"u"?t.eventsListeners[s]=[]:t.eventsListeners[s]&&t.eventsListeners[s].forEach((n,r)=>{(n===e||n.__emitterProxy&&n.__emitterProxy===e)&&t.eventsListeners[s].splice(r,1)})}),t},emit(){const i=this;if(!i.eventsListeners||i.destroyed||!i.eventsListeners)return i;let e,t,s;for(var n=arguments.length,r=new Array(n),l=0;l<n;l++)r[l]=arguments[l];return typeof r[0]=="string"||Array.isArray(r[0])?(e=r[0],t=r.slice(1,r.length),s=i):(e=r[0].events,t=r[0].data,s=r[0].context||i),t.unshift(s),(Array.isArray(e)?e:e.split(" ")).forEach(d=>{i.eventsAnyListeners&&i.eventsAnyListeners.length&&i.eventsAnyListeners.forEach(o=>{o.apply(s,[d,...t])}),i.eventsListeners&&i.eventsListeners[d]&&i.eventsListeners[d].forEach(o=>{o.apply(s,t)})}),i}};function lt(){const i=this;let e,t;const s=i.el;typeof i.params.width<"u"&&i.params.width!==null?e=i.params.width:e=s.clientWidth,typeof i.params.height<"u"&&i.params.height!==null?t=i.params.height:t=s.clientHeight,!(e===0&&i.isHorizontal()||t===0&&i.isVertical())&&(e=e-parseInt(W(s,"padding-left")||0,10)-parseInt(W(s,"padding-right")||0,10),t=t-parseInt(W(s,"padding-top")||0,10)-parseInt(W(s,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),Object.assign(i,{width:e,height:t,size:i.isHorizontal()?e:t}))}function ot(){const i=this;function e(y,E){return parseFloat(y.getPropertyValue(i.getDirectionLabel(E))||0)}const t=i.params,{wrapperEl:s,slidesEl:n,size:r,rtlTranslate:l,wrongRTL:a}=i,d=i.virtual&&t.virtual.enabled,o=d?i.virtual.slides.length:i.slides.length,u=H(n,`.${i.params.slideClass}, swiper-slide`),m=d?i.virtual.slides.length:u.length;let h=[];const g=[],p=[];let v=t.slidesOffsetBefore;typeof v=="function"&&(v=t.slidesOffsetBefore.call(i));let b=t.slidesOffsetAfter;typeof b=="function"&&(b=t.slidesOffsetAfter.call(i));const S=i.snapGrid.length,c=i.slidesGrid.length;let f=t.spaceBetween,w=-v,T=0,x=0;if(typeof r>"u")return;typeof f=="string"&&f.indexOf("%")>=0?f=parseFloat(f.replace("%",""))/100*r:typeof f=="string"&&(f=parseFloat(f)),i.virtualSize=-f,u.forEach(y=>{l?y.style.marginLeft="":y.style.marginRight="",y.style.marginBottom="",y.style.marginTop=""}),t.centeredSlides&&t.cssMode&&(te(s,"--swiper-centered-offset-before",""),te(s,"--swiper-centered-offset-after",""));const C=t.grid&&t.grid.rows>1&&i.grid;C?i.grid.initSlides(u):i.grid&&i.grid.unsetSlides();let I;const M=t.slidesPerView==="auto"&&t.breakpoints&&Object.keys(t.breakpoints).filter(y=>typeof t.breakpoints[y].slidesPerView<"u").length>0;for(let y=0;y<m;y+=1){I=0;let E;if(u[y]&&(E=u[y]),C&&i.grid.updateSlide(y,E,u),!(u[y]&&W(E,"display")==="none")){if(t.slidesPerView==="auto"){M&&(u[y].style[i.getDirectionLabel("width")]="");const A=getComputedStyle(E),O=E.style.transform,_=E.style.webkitTransform;if(O&&(E.style.transform="none"),_&&(E.style.webkitTransform="none"),t.roundLengths)I=i.isHorizontal()?de(E,"width"):de(E,"height");else{const D=e(A,"width"),L=e(A,"padding-left"),G=e(A,"padding-right"),P=e(A,"margin-left"),z=e(A,"margin-right"),R=A.getPropertyValue("box-sizing");if(R&&R==="border-box")I=D+P+z;else{const{clientWidth:U,offsetWidth:le}=E;I=D+L+G+P+z+(le-U)}}O&&(E.style.transform=O),_&&(E.style.webkitTransform=_),t.roundLengths&&(I=Math.floor(I))}else I=(r-(t.slidesPerView-1)*f)/t.slidesPerView,t.roundLengths&&(I=Math.floor(I)),u[y]&&(u[y].style[i.getDirectionLabel("width")]=`${I}px`);u[y]&&(u[y].swiperSlideSize=I),p.push(I),t.centeredSlides?(w=w+I/2+T/2+f,T===0&&y!==0&&(w=w-r/2-f),y===0&&(w=w-r/2-f),Math.abs(w)<1/1e3&&(w=0),t.roundLengths&&(w=Math.floor(w)),x%t.slidesPerGroup===0&&h.push(w),g.push(w)):(t.roundLengths&&(w=Math.floor(w)),(x-Math.min(i.params.slidesPerGroupSkip,x))%i.params.slidesPerGroup===0&&h.push(w),g.push(w),w=w+I+f),i.virtualSize+=I+f,T=I,x+=1}}if(i.virtualSize=Math.max(i.virtualSize,r)+b,l&&a&&(t.effect==="slide"||t.effect==="coverflow")&&(s.style.width=`${i.virtualSize+f}px`),t.setWrapperSize&&(s.style[i.getDirectionLabel("width")]=`${i.virtualSize+f}px`),C&&i.grid.updateWrapperSize(I,h),!t.centeredSlides){const y=[];for(let E=0;E<h.length;E+=1){let A=h[E];t.roundLengths&&(A=Math.floor(A)),h[E]<=i.virtualSize-r&&y.push(A)}h=y,Math.floor(i.virtualSize-r)-Math.floor(h[h.length-1])>1&&h.push(i.virtualSize-r)}if(d&&t.loop){const y=p[0]+f;if(t.slidesPerGroup>1){const E=Math.ceil((i.virtual.slidesBefore+i.virtual.slidesAfter)/t.slidesPerGroup),A=y*t.slidesPerGroup;for(let O=0;O<E;O+=1)h.push(h[h.length-1]+A)}for(let E=0;E<i.virtual.slidesBefore+i.virtual.slidesAfter;E+=1)t.slidesPerGroup===1&&h.push(h[h.length-1]+y),g.push(g[g.length-1]+y),i.virtualSize+=y}if(h.length===0&&(h=[0]),f!==0){const y=i.isHorizontal()&&l?"marginLeft":i.getDirectionLabel("marginRight");u.filter((E,A)=>!t.cssMode||t.loop?!0:A!==u.length-1).forEach(E=>{E.style[y]=`${f}px`})}if(t.centeredSlides&&t.centeredSlidesBounds){let y=0;p.forEach(A=>{y+=A+(f||0)}),y-=f;const E=y>r?y-r:0;h=h.map(A=>A<=0?-v:A>E?E+b:A)}if(t.centerInsufficientSlides){let y=0;p.forEach(A=>{y+=A+(f||0)}),y-=f;const E=(t.slidesOffsetBefore||0)+(t.slidesOffsetAfter||0);if(y+E<r){const A=(r-y-E)/2;h.forEach((O,_)=>{h[_]=O-A}),g.forEach((O,_)=>{g[_]=O+A})}}if(Object.assign(i,{slides:u,snapGrid:h,slidesGrid:g,slidesSizesGrid:p}),t.centeredSlides&&t.cssMode&&!t.centeredSlidesBounds){te(s,"--swiper-centered-offset-before",`${-h[0]}px`),te(s,"--swiper-centered-offset-after",`${i.size/2-p[p.length-1]/2}px`);const y=-i.snapGrid[0],E=-i.slidesGrid[0];i.snapGrid=i.snapGrid.map(A=>A+y),i.slidesGrid=i.slidesGrid.map(A=>A+E)}if(m!==o&&i.emit("slidesLengthChange"),h.length!==S&&(i.params.watchOverflow&&i.checkOverflow(),i.emit("snapGridLengthChange")),g.length!==c&&i.emit("slidesGridLengthChange"),t.watchSlidesProgress&&i.updateSlidesOffset(),i.emit("slidesUpdated"),!d&&!t.cssMode&&(t.effect==="slide"||t.effect==="fade")){const y=`${t.containerModifierClass}backface-hidden`,E=i.el.classList.contains(y);m<=t.maxBackfaceHiddenSlides?E||i.el.classList.add(y):E&&i.el.classList.remove(y)}}function dt(i){const e=this,t=[],s=e.virtual&&e.params.virtual.enabled;let n=0,r;typeof i=="number"?e.setTransition(i):i===!0&&e.setTransition(e.params.speed);const l=a=>s?e.slides[e.getSlideIndexByData(a)]:e.slides[a];if(e.params.slidesPerView!=="auto"&&e.params.slidesPerView>1)if(e.params.centeredSlides)(e.visibleSlides||[]).forEach(a=>{t.push(a)});else for(r=0;r<Math.ceil(e.params.slidesPerView);r+=1){const a=e.activeIndex+r;if(a>e.slides.length&&!s)break;t.push(l(a))}else t.push(l(e.activeIndex));for(r=0;r<t.length;r+=1)if(typeof t[r]<"u"){const a=t[r].offsetHeight;n=a>n?a:n}(n||n===0)&&(e.wrapperEl.style.height=`${n}px`)}function ct(){const i=this,e=i.slides,t=i.isElement?i.isHorizontal()?i.wrapperEl.offsetLeft:i.wrapperEl.offsetTop:0;for(let s=0;s<e.length;s+=1)e[s].swiperSlideOffset=(i.isHorizontal()?e[s].offsetLeft:e[s].offsetTop)-t-i.cssOverflowAdjustment()}const Oe=(i,e,t)=>{e&&!i.classList.contains(t)?i.classList.add(t):!e&&i.classList.contains(t)&&i.classList.remove(t)};function ut(i){i===void 0&&(i=this&&this.translate||0);const e=this,t=e.params,{slides:s,rtlTranslate:n,snapGrid:r}=e;if(s.length===0)return;typeof s[0].swiperSlideOffset>"u"&&e.updateSlidesOffset();let l=-i;n&&(l=i),e.visibleSlidesIndexes=[],e.visibleSlides=[];let a=t.spaceBetween;typeof a=="string"&&a.indexOf("%")>=0?a=parseFloat(a.replace("%",""))/100*e.size:typeof a=="string"&&(a=parseFloat(a));for(let d=0;d<s.length;d+=1){const o=s[d];let u=o.swiperSlideOffset;t.cssMode&&t.centeredSlides&&(u-=s[0].swiperSlideOffset);const m=(l+(t.centeredSlides?e.minTranslate():0)-u)/(o.swiperSlideSize+a),h=(l-r[0]+(t.centeredSlides?e.minTranslate():0)-u)/(o.swiperSlideSize+a),g=-(l-u),p=g+e.slidesSizesGrid[d],v=g>=0&&g<=e.size-e.slidesSizesGrid[d],b=g>=0&&g<e.size-1||p>1&&p<=e.size||g<=0&&p>=e.size;b&&(e.visibleSlides.push(o),e.visibleSlidesIndexes.push(d)),Oe(o,b,t.slideVisibleClass),Oe(o,v,t.slideFullyVisibleClass),o.progress=n?-m:m,o.originalProgress=n?-h:h}}function ft(i){const e=this;if(typeof i>"u"){const u=e.rtlTranslate?-1:1;i=e&&e.translate&&e.translate*u||0}const t=e.params,s=e.maxTranslate()-e.minTranslate();let{progress:n,isBeginning:r,isEnd:l,progressLoop:a}=e;const d=r,o=l;if(s===0)n=0,r=!0,l=!0;else{n=(i-e.minTranslate())/s;const u=Math.abs(i-e.minTranslate())<1,m=Math.abs(i-e.maxTranslate())<1;r=u||n<=0,l=m||n>=1,u&&(n=0),m&&(n=1)}if(t.loop){const u=e.getSlideIndexByData(0),m=e.getSlideIndexByData(e.slides.length-1),h=e.slidesGrid[u],g=e.slidesGrid[m],p=e.slidesGrid[e.slidesGrid.length-1],v=Math.abs(i);v>=h?a=(v-h)/p:a=(v+p-g)/p,a>1&&(a-=1)}Object.assign(e,{progress:n,progressLoop:a,isBeginning:r,isEnd:l}),(t.watchSlidesProgress||t.centeredSlides&&t.autoHeight)&&e.updateSlidesProgress(i),r&&!d&&e.emit("reachBeginning toEdge"),l&&!o&&e.emit("reachEnd toEdge"),(d&&!r||o&&!l)&&e.emit("fromEdge"),e.emit("progress",n)}const pe=(i,e,t)=>{e&&!i.classList.contains(t)?i.classList.add(t):!e&&i.classList.contains(t)&&i.classList.remove(t)};function pt(){const i=this,{slides:e,params:t,slidesEl:s,activeIndex:n}=i,r=i.virtual&&t.virtual.enabled,l=i.grid&&t.grid&&t.grid.rows>1,a=m=>H(s,`.${t.slideClass}${m}, swiper-slide${m}`)[0];let d,o,u;if(r)if(t.loop){let m=n-i.virtual.slidesBefore;m<0&&(m=i.virtual.slides.length+m),m>=i.virtual.slides.length&&(m-=i.virtual.slides.length),d=a(`[data-swiper-slide-index="${m}"]`)}else d=a(`[data-swiper-slide-index="${n}"]`);else l?(d=e.find(m=>m.column===n),u=e.find(m=>m.column===n+1),o=e.find(m=>m.column===n-1)):d=e[n];d&&(l||(u=et(d,`.${t.slideClass}, swiper-slide`)[0],t.loop&&!u&&(u=e[0]),o=Ze(d,`.${t.slideClass}, swiper-slide`)[0],t.loop&&!o===0&&(o=e[e.length-1]))),e.forEach(m=>{pe(m,m===d,t.slideActiveClass),pe(m,m===u,t.slideNextClass),pe(m,m===o,t.slidePrevClass)}),i.emitSlidesClasses()}const ne=(i,e)=>{if(!i||i.destroyed||!i.params)return;const t=()=>i.isElement?"swiper-slide":`.${i.params.slideClass}`,s=e.closest(t());if(s){let n=s.querySelector(`.${i.params.lazyPreloaderClass}`);!n&&i.isElement&&(s.shadowRoot?n=s.shadowRoot.querySelector(`.${i.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{s.shadowRoot&&(n=s.shadowRoot.querySelector(`.${i.params.lazyPreloaderClass}`),n&&n.remove())})),n&&n.remove()}},me=(i,e)=>{if(!i.slides[e])return;const t=i.slides[e].querySelector('[loading="lazy"]');t&&t.removeAttribute("loading")},he=i=>{if(!i||i.destroyed||!i.params)return;let e=i.params.lazyPreloadPrevNext;const t=i.slides.length;if(!t||!e||e<0)return;e=Math.min(e,t);const s=i.params.slidesPerView==="auto"?i.slidesPerViewDynamic():Math.ceil(i.params.slidesPerView),n=i.activeIndex;if(i.params.grid&&i.params.grid.rows>1){const l=n,a=[l-e];a.push(...Array.from({length:e}).map((d,o)=>l+s+o)),i.slides.forEach((d,o)=>{a.includes(d.column)&&me(i,o)});return}const r=n+s-1;if(i.params.rewind||i.params.loop)for(let l=n-e;l<=r+e;l+=1){const a=(l%t+t)%t;(a<n||a>r)&&me(i,a)}else for(let l=Math.max(n-e,0);l<=Math.min(r+e,t-1);l+=1)l!==n&&(l>r||l<n)&&me(i,l)};function mt(i){const{slidesGrid:e,params:t}=i,s=i.rtlTranslate?i.translate:-i.translate;let n;for(let r=0;r<e.length;r+=1)typeof e[r+1]<"u"?s>=e[r]&&s<e[r+1]-(e[r+1]-e[r])/2?n=r:s>=e[r]&&s<e[r+1]&&(n=r+1):s>=e[r]&&(n=r);return t.normalizeSlideIndex&&(n<0||typeof n>"u")&&(n=0),n}function ht(i){const e=this,t=e.rtlTranslate?e.translate:-e.translate,{snapGrid:s,params:n,activeIndex:r,realIndex:l,snapIndex:a}=e;let d=i,o;const u=g=>{let p=g-e.virtual.slidesBefore;return p<0&&(p=e.virtual.slides.length+p),p>=e.virtual.slides.length&&(p-=e.virtual.slides.length),p};if(typeof d>"u"&&(d=mt(e)),s.indexOf(t)>=0)o=s.indexOf(t);else{const g=Math.min(n.slidesPerGroupSkip,d);o=g+Math.floor((d-g)/n.slidesPerGroup)}if(o>=s.length&&(o=s.length-1),d===r&&!e.params.loop){o!==a&&(e.snapIndex=o,e.emit("snapIndexChange"));return}if(d===r&&e.params.loop&&e.virtual&&e.params.virtual.enabled){e.realIndex=u(d);return}const m=e.grid&&n.grid&&n.grid.rows>1;let h;if(e.virtual&&n.virtual.enabled&&n.loop)h=u(d);else if(m){const g=e.slides.find(v=>v.column===d);let p=parseInt(g.getAttribute("data-swiper-slide-index"),10);Number.isNaN(p)&&(p=Math.max(e.slides.indexOf(g),0)),h=Math.floor(p/n.grid.rows)}else if(e.slides[d]){const g=e.slides[d].getAttribute("data-swiper-slide-index");g?h=parseInt(g,10):h=d}else h=d;Object.assign(e,{previousSnapIndex:a,snapIndex:o,previousRealIndex:l,realIndex:h,previousIndex:r,activeIndex:d}),e.initialized&&he(e),e.emit("activeIndexChange"),e.emit("snapIndexChange"),(e.initialized||e.params.runCallbacksOnInit)&&(l!==h&&e.emit("realIndexChange"),e.emit("slideChange"))}function gt(i,e){const t=this,s=t.params;let n=i.closest(`.${s.slideClass}, swiper-slide`);!n&&t.isElement&&e&&e.length>1&&e.includes(i)&&[...e.slice(e.indexOf(i)+1,e.length)].forEach(a=>{!n&&a.matches&&a.matches(`.${s.slideClass}, swiper-slide`)&&(n=a)});let r=!1,l;if(n){for(let a=0;a<t.slides.length;a+=1)if(t.slides[a]===n){r=!0,l=a;break}}if(n&&r)t.clickedSlide=n,t.virtual&&t.params.virtual.enabled?t.clickedIndex=parseInt(n.getAttribute("data-swiper-slide-index"),10):t.clickedIndex=l;else{t.clickedSlide=void 0,t.clickedIndex=void 0;return}s.slideToClickedSlide&&t.clickedIndex!==void 0&&t.clickedIndex!==t.activeIndex&&t.slideToClickedSlide()}var vt={updateSize:lt,updateSlides:ot,updateAutoHeight:dt,updateSlidesOffset:ct,updateSlidesProgress:ut,updateProgress:ft,updateSlidesClasses:pt,updateActiveIndex:ht,updateClickedSlide:gt};function wt(i){i===void 0&&(i=this.isHorizontal()?"x":"y");const e=this,{params:t,rtlTranslate:s,translate:n,wrapperEl:r}=e;if(t.virtualTranslate)return s?-n:n;if(t.cssMode)return n;let l=Ue(r,i);return l+=e.cssOverflowAdjustment(),s&&(l=-l),l||0}function St(i,e){const t=this,{rtlTranslate:s,params:n,wrapperEl:r,progress:l}=t;let a=0,d=0;const o=0;t.isHorizontal()?a=s?-i:i:d=i,n.roundLengths&&(a=Math.floor(a),d=Math.floor(d)),t.previousTranslate=t.translate,t.translate=t.isHorizontal()?a:d,n.cssMode?r[t.isHorizontal()?"scrollLeft":"scrollTop"]=t.isHorizontal()?-a:-d:n.virtualTranslate||(t.isHorizontal()?a-=t.cssOverflowAdjustment():d-=t.cssOverflowAdjustment(),r.style.transform=`translate3d(${a}px, ${d}px, ${o}px)`);let u;const m=t.maxTranslate()-t.minTranslate();m===0?u=0:u=(i-t.minTranslate())/m,u!==l&&t.updateProgress(i),t.emit("setTranslate",t.translate,e)}function Tt(){return-this.snapGrid[0]}function yt(){return-this.snapGrid[this.snapGrid.length-1]}function bt(i,e,t,s,n){i===void 0&&(i=0),e===void 0&&(e=this.params.speed),t===void 0&&(t=!0),s===void 0&&(s=!0);const r=this,{params:l,wrapperEl:a}=r;if(r.animating&&l.preventInteractionOnTransition)return!1;const d=r.minTranslate(),o=r.maxTranslate();let u;if(s&&i>d?u=d:s&&i<o?u=o:u=i,r.updateProgress(u),l.cssMode){const m=r.isHorizontal();if(e===0)a[m?"scrollLeft":"scrollTop"]=-u;else{if(!r.support.smoothScroll)return Ie({swiper:r,targetPosition:-u,side:m?"left":"top"}),!0;a.scrollTo({[m?"left":"top"]:-u,behavior:"smooth"})}return!0}return e===0?(r.setTransition(0),r.setTranslate(u),t&&(r.emit("beforeTransitionStart",e,n),r.emit("transitionEnd"))):(r.setTransition(e),r.setTranslate(u),t&&(r.emit("beforeTransitionStart",e,n),r.emit("transitionStart")),r.animating||(r.animating=!0,r.onTranslateToWrapperTransitionEnd||(r.onTranslateToWrapperTransitionEnd=function(h){!r||r.destroyed||h.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onTranslateToWrapperTransitionEnd),r.onTranslateToWrapperTransitionEnd=null,delete r.onTranslateToWrapperTransitionEnd,r.animating=!1,t&&r.emit("transitionEnd"))}),r.wrapperEl.addEventListener("transitionend",r.onTranslateToWrapperTransitionEnd))),!0}var Et={getTranslate:wt,setTranslate:St,minTranslate:Tt,maxTranslate:yt,translateTo:bt};function xt(i,e){const t=this;t.params.cssMode||(t.wrapperEl.style.transitionDuration=`${i}ms`,t.wrapperEl.style.transitionDelay=i===0?"0ms":""),t.emit("setTransition",i,e)}function _e(i){let{swiper:e,runCallbacks:t,direction:s,step:n}=i;const{activeIndex:r,previousIndex:l}=e;let a=s;a||(r>l?a="next":r<l?a="prev":a="reset"),e.emit(`transition${n}`),t&&a==="reset"?e.emit(`slideResetTransition${n}`):t&&r!==l&&(e.emit(`slideChangeTransition${n}`),a==="next"?e.emit(`slideNextTransition${n}`):e.emit(`slidePrevTransition${n}`))}function It(i,e){i===void 0&&(i=!0);const t=this,{params:s}=t;s.cssMode||(s.autoHeight&&t.updateAutoHeight(),_e({swiper:t,runCallbacks:i,direction:e,step:"Start"}))}function Ct(i,e){i===void 0&&(i=!0);const t=this,{params:s}=t;t.animating=!1,!s.cssMode&&(t.setTransition(0),_e({swiper:t,runCallbacks:i,direction:e,step:"End"}))}var Mt={setTransition:xt,transitionStart:It,transitionEnd:Ct};function Lt(i,e,t,s,n){i===void 0&&(i=0),t===void 0&&(t=!0),typeof i=="string"&&(i=parseInt(i,10));const r=this;let l=i;l<0&&(l=0);const{params:a,snapGrid:d,slidesGrid:o,previousIndex:u,activeIndex:m,rtlTranslate:h,wrapperEl:g,enabled:p}=r;if(!p&&!s&&!n||r.destroyed||r.animating&&a.preventInteractionOnTransition)return!1;typeof e>"u"&&(e=r.params.speed);const v=Math.min(r.params.slidesPerGroupSkip,l);let b=v+Math.floor((l-v)/r.params.slidesPerGroup);b>=d.length&&(b=d.length-1);const S=-d[b];if(a.normalizeSlideIndex)for(let C=0;C<o.length;C+=1){const I=-Math.floor(S*100),M=Math.floor(o[C]*100),y=Math.floor(o[C+1]*100);typeof o[C+1]<"u"?I>=M&&I<y-(y-M)/2?l=C:I>=M&&I<y&&(l=C+1):I>=M&&(l=C)}if(r.initialized&&l!==m&&(!r.allowSlideNext&&(h?S>r.translate&&S>r.minTranslate():S<r.translate&&S<r.minTranslate())||!r.allowSlidePrev&&S>r.translate&&S>r.maxTranslate()&&(m||0)!==l))return!1;l!==(u||0)&&t&&r.emit("beforeSlideChangeStart"),r.updateProgress(S);let c;l>m?c="next":l<m?c="prev":c="reset";const f=r.virtual&&r.params.virtual.enabled;if(!(f&&n)&&(h&&-S===r.translate||!h&&S===r.translate))return r.updateActiveIndex(l),a.autoHeight&&r.updateAutoHeight(),r.updateSlidesClasses(),a.effect!=="slide"&&r.setTranslate(S),c!=="reset"&&(r.transitionStart(t,c),r.transitionEnd(t,c)),!1;if(a.cssMode){const C=r.isHorizontal(),I=h?S:-S;if(e===0)f&&(r.wrapperEl.style.scrollSnapType="none",r._immediateVirtual=!0),f&&!r._cssModeVirtualInitialSet&&r.params.initialSlide>0?(r._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{g[C?"scrollLeft":"scrollTop"]=I})):g[C?"scrollLeft":"scrollTop"]=I,f&&requestAnimationFrame(()=>{r.wrapperEl.style.scrollSnapType="",r._immediateVirtual=!1});else{if(!r.support.smoothScroll)return Ie({swiper:r,targetPosition:I,side:C?"left":"top"}),!0;g.scrollTo({[C?"left":"top"]:I,behavior:"smooth"})}return!0}const x=Ae().isSafari;return f&&!n&&x&&r.isElement&&r.virtual.update(!1,!1,l),r.setTransition(e),r.setTranslate(S),r.updateActiveIndex(l),r.updateSlidesClasses(),r.emit("beforeTransitionStart",e,s),r.transitionStart(t,c),e===0?r.transitionEnd(t,c):r.animating||(r.animating=!0,r.onSlideToWrapperTransitionEnd||(r.onSlideToWrapperTransitionEnd=function(I){!r||r.destroyed||I.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.onSlideToWrapperTransitionEnd=null,delete r.onSlideToWrapperTransitionEnd,r.transitionEnd(t,c))}),r.wrapperEl.addEventListener("transitionend",r.onSlideToWrapperTransitionEnd)),!0}function Pt(i,e,t,s){i===void 0&&(i=0),t===void 0&&(t=!0),typeof i=="string"&&(i=parseInt(i,10));const n=this;if(n.destroyed)return;typeof e>"u"&&(e=n.params.speed);const r=n.grid&&n.params.grid&&n.params.grid.rows>1;let l=i;if(n.params.loop)if(n.virtual&&n.params.virtual.enabled)l=l+n.virtual.slidesBefore;else{let a;if(r){const h=l*n.params.grid.rows;a=n.slides.find(g=>g.getAttribute("data-swiper-slide-index")*1===h).column}else a=n.getSlideIndexByData(l);const d=r?Math.ceil(n.slides.length/n.params.grid.rows):n.slides.length,{centeredSlides:o}=n.params;let u=n.params.slidesPerView;u==="auto"?u=n.slidesPerViewDynamic():(u=Math.ceil(parseFloat(n.params.slidesPerView,10)),o&&u%2===0&&(u=u+1));let m=d-a<u;if(o&&(m=m||a<Math.ceil(u/2)),s&&o&&n.params.slidesPerView!=="auto"&&!r&&(m=!1),m){const h=o?a<n.activeIndex?"prev":"next":a-n.activeIndex-1<n.params.slidesPerView?"next":"prev";n.loopFix({direction:h,slideTo:!0,activeSlideIndex:h==="next"?a+1:a-d+1,slideRealIndex:h==="next"?n.realIndex:void 0})}if(r){const h=l*n.params.grid.rows;l=n.slides.find(g=>g.getAttribute("data-swiper-slide-index")*1===h).column}else l=n.getSlideIndexByData(l)}return requestAnimationFrame(()=>{n.slideTo(l,e,t,s)}),n}function At(i,e,t){e===void 0&&(e=!0);const s=this,{enabled:n,params:r,animating:l}=s;if(!n||s.destroyed)return s;typeof i>"u"&&(i=s.params.speed);let a=r.slidesPerGroup;r.slidesPerView==="auto"&&r.slidesPerGroup===1&&r.slidesPerGroupAuto&&(a=Math.max(s.slidesPerViewDynamic("current",!0),1));const d=s.activeIndex<r.slidesPerGroupSkip?1:a,o=s.virtual&&r.virtual.enabled;if(r.loop){if(l&&!o&&r.loopPreventsSliding)return!1;if(s.loopFix({direction:"next"}),s._clientLeft=s.wrapperEl.clientLeft,s.activeIndex===s.slides.length-1&&r.cssMode)return requestAnimationFrame(()=>{s.slideTo(s.activeIndex+d,i,e,t)}),!0}return r.rewind&&s.isEnd?s.slideTo(0,i,e,t):s.slideTo(s.activeIndex+d,i,e,t)}function Ot(i,e,t){e===void 0&&(e=!0);const s=this,{params:n,snapGrid:r,slidesGrid:l,rtlTranslate:a,enabled:d,animating:o}=s;if(!d||s.destroyed)return s;typeof i>"u"&&(i=s.params.speed);const u=s.virtual&&n.virtual.enabled;if(n.loop){if(o&&!u&&n.loopPreventsSliding)return!1;s.loopFix({direction:"prev"}),s._clientLeft=s.wrapperEl.clientLeft}const m=a?s.translate:-s.translate;function h(c){return c<0?-Math.floor(Math.abs(c)):Math.floor(c)}const g=h(m),p=r.map(c=>h(c)),v=n.freeMode&&n.freeMode.enabled;let b=r[p.indexOf(g)-1];if(typeof b>"u"&&(n.cssMode||v)){let c;r.forEach((f,w)=>{g>=f&&(c=w)}),typeof c<"u"&&(b=v?r[c]:r[c>0?c-1:c])}let S=0;if(typeof b<"u"&&(S=l.indexOf(b),S<0&&(S=s.activeIndex-1),n.slidesPerView==="auto"&&n.slidesPerGroup===1&&n.slidesPerGroupAuto&&(S=S-s.slidesPerViewDynamic("previous",!0)+1,S=Math.max(S,0))),n.rewind&&s.isBeginning){const c=s.params.virtual&&s.params.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1;return s.slideTo(c,i,e,t)}else if(n.loop&&s.activeIndex===0&&n.cssMode)return requestAnimationFrame(()=>{s.slideTo(S,i,e,t)}),!0;return s.slideTo(S,i,e,t)}function _t(i,e,t){e===void 0&&(e=!0);const s=this;if(!s.destroyed)return typeof i>"u"&&(i=s.params.speed),s.slideTo(s.activeIndex,i,e,t)}function Dt(i,e,t,s){e===void 0&&(e=!0),s===void 0&&(s=.5);const n=this;if(n.destroyed)return;typeof i>"u"&&(i=n.params.speed);let r=n.activeIndex;const l=Math.min(n.params.slidesPerGroupSkip,r),a=l+Math.floor((r-l)/n.params.slidesPerGroup),d=n.rtlTranslate?n.translate:-n.translate;if(d>=n.snapGrid[a]){const o=n.snapGrid[a],u=n.snapGrid[a+1];d-o>(u-o)*s&&(r+=n.params.slidesPerGroup)}else{const o=n.snapGrid[a-1],u=n.snapGrid[a];d-o<=(u-o)*s&&(r-=n.params.slidesPerGroup)}return r=Math.max(r,0),r=Math.min(r,n.slidesGrid.length-1),n.slideTo(r,i,e,t)}function Nt(){const i=this;if(i.destroyed)return;const{params:e,slidesEl:t}=i,s=e.slidesPerView==="auto"?i.slidesPerViewDynamic():e.slidesPerView;let n=i.getSlideIndexWhenGrid(i.clickedIndex),r;const l=i.isElement?"swiper-slide":`.${e.slideClass}`,a=i.grid&&i.params.grid&&i.params.grid.rows>1;if(e.loop){if(i.animating)return;r=parseInt(i.clickedSlide.getAttribute("data-swiper-slide-index"),10),e.centeredSlides?i.slideToLoop(r):n>(a?(i.slides.length-s)/2-(i.params.grid.rows-1):i.slides.length-s)?(i.loopFix(),n=i.getSlideIndex(H(t,`${l}[data-swiper-slide-index="${r}"]`)[0]),xe(()=>{i.slideTo(n)})):i.slideTo(n)}else i.slideTo(n)}var zt={slideTo:Lt,slideToLoop:Pt,slideNext:At,slidePrev:Ot,slideReset:_t,slideToClosest:Dt,slideToClickedSlide:Nt};function kt(i,e){const t=this,{params:s,slidesEl:n}=t;if(!s.loop||t.virtual&&t.params.virtual.enabled)return;const r=()=>{H(n,`.${s.slideClass}, swiper-slide`).forEach((g,p)=>{g.setAttribute("data-swiper-slide-index",p)})},l=()=>{const h=H(n,`.${s.slideBlankClass}`);h.forEach(g=>{g.remove()}),h.length>0&&(t.recalcSlides(),t.updateSlides())},a=t.grid&&s.grid&&s.grid.rows>1;s.loopAddBlankSlides&&(s.slidesPerGroup>1||a)&&l();const d=s.slidesPerGroup*(a?s.grid.rows:1),o=t.slides.length%d!==0,u=a&&t.slides.length%s.grid.rows!==0,m=h=>{for(let g=0;g<h;g+=1){const p=t.isElement?se("swiper-slide",[s.slideBlankClass]):se("div",[s.slideClass,s.slideBlankClass]);t.slidesEl.append(p)}};if(o){if(s.loopAddBlankSlides){const h=d-t.slides.length%d;m(h),t.recalcSlides(),t.updateSlides()}else ie("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");r()}else if(u){if(s.loopAddBlankSlides){const h=s.grid.rows-t.slides.length%s.grid.rows;m(h),t.recalcSlides(),t.updateSlides()}else ie("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");r()}else r();t.loopFix({slideRealIndex:i,direction:s.centeredSlides?void 0:"next",initial:e})}function Gt(i){let{slideRealIndex:e,slideTo:t=!0,direction:s,setTranslate:n,activeSlideIndex:r,initial:l,byController:a,byMousewheel:d}=i===void 0?{}:i;const o=this;if(!o.params.loop)return;o.emit("beforeLoopFix");const{slides:u,allowSlidePrev:m,allowSlideNext:h,slidesEl:g,params:p}=o,{centeredSlides:v,initialSlide:b}=p;if(o.allowSlidePrev=!0,o.allowSlideNext=!0,o.virtual&&p.virtual.enabled){t&&(!p.centeredSlides&&o.snapIndex===0?o.slideTo(o.virtual.slides.length,0,!1,!0):p.centeredSlides&&o.snapIndex<p.slidesPerView?o.slideTo(o.virtual.slides.length+o.snapIndex,0,!1,!0):o.snapIndex===o.snapGrid.length-1&&o.slideTo(o.virtual.slidesBefore,0,!1,!0)),o.allowSlidePrev=m,o.allowSlideNext=h,o.emit("loopFix");return}let S=p.slidesPerView;S==="auto"?S=o.slidesPerViewDynamic():(S=Math.ceil(parseFloat(p.slidesPerView,10)),v&&S%2===0&&(S=S+1));const c=p.slidesPerGroupAuto?S:p.slidesPerGroup;let f=v?Math.max(c,Math.ceil(S/2)):c;f%c!==0&&(f+=c-f%c),f+=p.loopAdditionalSlides,o.loopedSlides=f;const w=o.grid&&p.grid&&p.grid.rows>1;u.length<S+f||o.params.effect==="cards"&&u.length<S+f*2?ie("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):w&&p.grid.fill==="row"&&ie("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const T=[],x=[],C=w?Math.ceil(u.length/p.grid.rows):u.length,I=l&&C-b<S&&!v;let M=I?b:o.activeIndex;typeof r>"u"?r=o.getSlideIndex(u.find(L=>L.classList.contains(p.slideActiveClass))):M=r;const y=s==="next"||!s,E=s==="prev"||!s;let A=0,O=0;const D=(w?u[r].column:r)+(v&&typeof n>"u"?-S/2+.5:0);if(D<f){A=Math.max(f-D,c);for(let L=0;L<f-D;L+=1){const G=L-Math.floor(L/C)*C;if(w){const P=C-G-1;for(let z=u.length-1;z>=0;z-=1)u[z].column===P&&T.push(z)}else T.push(C-G-1)}}else if(D+S>C-f){O=Math.max(D-(C-f*2),c),I&&(O=Math.max(O,S-C+b+1));for(let L=0;L<O;L+=1){const G=L-Math.floor(L/C)*C;w?u.forEach((P,z)=>{P.column===G&&x.push(z)}):x.push(G)}}if(o.__preventObserver__=!0,requestAnimationFrame(()=>{o.__preventObserver__=!1}),o.params.effect==="cards"&&u.length<S+f*2&&(x.includes(r)&&x.splice(x.indexOf(r),1),T.includes(r)&&T.splice(T.indexOf(r),1)),E&&T.forEach(L=>{u[L].swiperLoopMoveDOM=!0,g.prepend(u[L]),u[L].swiperLoopMoveDOM=!1}),y&&x.forEach(L=>{u[L].swiperLoopMoveDOM=!0,g.append(u[L]),u[L].swiperLoopMoveDOM=!1}),o.recalcSlides(),p.slidesPerView==="auto"?o.updateSlides():w&&(T.length>0&&E||x.length>0&&y)&&o.slides.forEach((L,G)=>{o.grid.updateSlide(G,L,o.slides)}),p.watchSlidesProgress&&o.updateSlidesOffset(),t){if(T.length>0&&E){if(typeof e>"u"){const L=o.slidesGrid[M],P=o.slidesGrid[M+A]-L;d?o.setTranslate(o.translate-P):(o.slideTo(M+Math.ceil(A),0,!1,!0),n&&(o.touchEventsData.startTranslate=o.touchEventsData.startTranslate-P,o.touchEventsData.currentTranslate=o.touchEventsData.currentTranslate-P))}else if(n){const L=w?T.length/p.grid.rows:T.length;o.slideTo(o.activeIndex+L,0,!1,!0),o.touchEventsData.currentTranslate=o.translate}}else if(x.length>0&&y)if(typeof e>"u"){const L=o.slidesGrid[M],P=o.slidesGrid[M-O]-L;d?o.setTranslate(o.translate-P):(o.slideTo(M-O,0,!1,!0),n&&(o.touchEventsData.startTranslate=o.touchEventsData.startTranslate-P,o.touchEventsData.currentTranslate=o.touchEventsData.currentTranslate-P))}else{const L=w?x.length/p.grid.rows:x.length;o.slideTo(o.activeIndex-L,0,!1,!0)}}if(o.allowSlidePrev=m,o.allowSlideNext=h,o.controller&&o.controller.control&&!a){const L={slideRealIndex:e,direction:s,setTranslate:n,activeSlideIndex:r,byController:!0};Array.isArray(o.controller.control)?o.controller.control.forEach(G=>{!G.destroyed&&G.params.loop&&G.loopFix({...L,slideTo:G.params.slidesPerView===p.slidesPerView?t:!1})}):o.controller.control instanceof o.constructor&&o.controller.control.params.loop&&o.controller.control.loopFix({...L,slideTo:o.controller.control.params.slidesPerView===p.slidesPerView?t:!1})}o.emit("loopFix")}function Bt(){const i=this,{params:e,slidesEl:t}=i;if(!e.loop||!t||i.virtual&&i.params.virtual.enabled)return;i.recalcSlides();const s=[];i.slides.forEach(n=>{const r=typeof n.swiperSlideIndex>"u"?n.getAttribute("data-swiper-slide-index")*1:n.swiperSlideIndex;s[r]=n}),i.slides.forEach(n=>{n.removeAttribute("data-swiper-slide-index")}),s.forEach(n=>{t.append(n)}),i.recalcSlides(),i.slideTo(i.realIndex,0)}var Rt={loopCreate:kt,loopFix:Gt,loopDestroy:Bt};function Vt(i){const e=this;if(!e.params.simulateTouch||e.params.watchOverflow&&e.isLocked||e.params.cssMode)return;const t=e.params.touchEventsTarget==="container"?e.el:e.wrapperEl;e.isElement&&(e.__preventObserver__=!0),t.style.cursor="move",t.style.cursor=i?"grabbing":"grab",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1})}function $t(){const i=this;i.params.watchOverflow&&i.isLocked||i.params.cssMode||(i.isElement&&(i.__preventObserver__=!0),i[i.params.touchEventsTarget==="container"?"el":"wrapperEl"].style.cursor="",i.isElement&&requestAnimationFrame(()=>{i.__preventObserver__=!1}))}var Ft={setGrabCursor:Vt,unsetGrabCursor:$t};function Ht(i,e){e===void 0&&(e=this);function t(s){if(!s||s===F()||s===B())return null;s.assignedSlot&&(s=s.assignedSlot);const n=s.closest(i);return!n&&!s.getRootNode?null:n||t(s.getRootNode().host)}return t(e)}function De(i,e,t){const s=B(),{params:n}=i,r=n.edgeSwipeDetection,l=n.edgeSwipeThreshold;return r&&(t<=l||t>=s.innerWidth-l)?r==="prevent"?(e.preventDefault(),!0):!1:!0}function Wt(i){const e=this,t=F();let s=i;s.originalEvent&&(s=s.originalEvent);const n=e.touchEventsData;if(s.type==="pointerdown"){if(n.pointerId!==null&&n.pointerId!==s.pointerId)return;n.pointerId=s.pointerId}else s.type==="touchstart"&&s.targetTouches.length===1&&(n.touchId=s.targetTouches[0].identifier);if(s.type==="touchstart"){De(e,s,s.targetTouches[0].pageX);return}const{params:r,touches:l,enabled:a}=e;if(!a||!r.simulateTouch&&s.pointerType==="mouse"||e.animating&&r.preventInteractionOnTransition)return;!e.animating&&r.cssMode&&r.loop&&e.loopFix();let d=s.target;if(r.touchEventsTarget==="wrapper"&&!Je(d,e.wrapperEl)||"which"in s&&s.which===3||"button"in s&&s.button>0||n.isTouched&&n.isMoved)return;const o=!!r.noSwipingClass&&r.noSwipingClass!=="",u=s.composedPath?s.composedPath():s.path;o&&s.target&&s.target.shadowRoot&&u&&(d=u[0]);const m=r.noSwipingSelector?r.noSwipingSelector:`.${r.noSwipingClass}`,h=!!(s.target&&s.target.shadowRoot);if(r.noSwiping&&(h?Ht(m,d):d.closest(m))){e.allowClick=!0;return}if(r.swipeHandler&&!d.closest(r.swipeHandler))return;l.currentX=s.pageX,l.currentY=s.pageY;const g=l.currentX,p=l.currentY;if(!De(e,s,g))return;Object.assign(n,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),l.startX=g,l.startY=p,n.touchStartTime=Z(),e.allowClick=!0,e.updateSize(),e.swipeDirection=void 0,r.threshold>0&&(n.allowThresholdMove=!1);let v=!0;d.matches(n.focusableElements)&&(v=!1,d.nodeName==="SELECT"&&(n.isTouched=!1)),t.activeElement&&t.activeElement.matches(n.focusableElements)&&t.activeElement!==d&&(s.pointerType==="mouse"||s.pointerType!=="mouse"&&!d.matches(n.focusableElements))&&t.activeElement.blur();const b=v&&e.allowTouchMove&&r.touchStartPreventDefault;(r.touchStartForcePreventDefault||b)&&!d.isContentEditable&&s.preventDefault(),r.freeMode&&r.freeMode.enabled&&e.freeMode&&e.animating&&!r.cssMode&&e.freeMode.onTouchStart(),e.emit("touchStart",s)}function jt(i){const e=F(),t=this,s=t.touchEventsData,{params:n,touches:r,rtlTranslate:l,enabled:a}=t;if(!a||!n.simulateTouch&&i.pointerType==="mouse")return;let d=i;if(d.originalEvent&&(d=d.originalEvent),d.type==="pointermove"&&(s.touchId!==null||d.pointerId!==s.pointerId))return;let o;if(d.type==="touchmove"){if(o=[...d.changedTouches].find(T=>T.identifier===s.touchId),!o||o.identifier!==s.touchId)return}else o=d;if(!s.isTouched){s.startMoving&&s.isScrolling&&t.emit("touchMoveOpposite",d);return}const u=o.pageX,m=o.pageY;if(d.preventedByNestedSwiper){r.startX=u,r.startY=m;return}if(!t.allowTouchMove){d.target.matches(s.focusableElements)||(t.allowClick=!1),s.isTouched&&(Object.assign(r,{startX:u,startY:m,currentX:u,currentY:m}),s.touchStartTime=Z());return}if(n.touchReleaseOnEdges&&!n.loop)if(t.isVertical()){if(m<r.startY&&t.translate<=t.maxTranslate()||m>r.startY&&t.translate>=t.minTranslate()){s.isTouched=!1,s.isMoved=!1;return}}else{if(l&&(u>r.startX&&-t.translate<=t.maxTranslate()||u<r.startX&&-t.translate>=t.minTranslate()))return;if(!l&&(u<r.startX&&t.translate<=t.maxTranslate()||u>r.startX&&t.translate>=t.minTranslate()))return}if(e.activeElement&&e.activeElement.matches(s.focusableElements)&&e.activeElement!==d.target&&d.pointerType!=="mouse"&&e.activeElement.blur(),e.activeElement&&d.target===e.activeElement&&d.target.matches(s.focusableElements)){s.isMoved=!0,t.allowClick=!1;return}s.allowTouchCallbacks&&t.emit("touchMove",d),r.previousX=r.currentX,r.previousY=r.currentY,r.currentX=u,r.currentY=m;const h=r.currentX-r.startX,g=r.currentY-r.startY;if(t.params.threshold&&Math.sqrt(h**2+g**2)<t.params.threshold)return;if(typeof s.isScrolling>"u"){let T;t.isHorizontal()&&r.currentY===r.startY||t.isVertical()&&r.currentX===r.startX?s.isScrolling=!1:h*h+g*g>=25&&(T=Math.atan2(Math.abs(g),Math.abs(h))*180/Math.PI,s.isScrolling=t.isHorizontal()?T>n.touchAngle:90-T>n.touchAngle)}if(s.isScrolling&&t.emit("touchMoveOpposite",d),typeof s.startMoving>"u"&&(r.currentX!==r.startX||r.currentY!==r.startY)&&(s.startMoving=!0),s.isScrolling||d.type==="touchmove"&&s.preventTouchMoveFromPointerMove){s.isTouched=!1;return}if(!s.startMoving)return;t.allowClick=!1,!n.cssMode&&d.cancelable&&d.preventDefault(),n.touchMoveStopPropagation&&!n.nested&&d.stopPropagation();let p=t.isHorizontal()?h:g,v=t.isHorizontal()?r.currentX-r.previousX:r.currentY-r.previousY;n.oneWayMovement&&(p=Math.abs(p)*(l?1:-1),v=Math.abs(v)*(l?1:-1)),r.diff=p,p*=n.touchRatio,l&&(p=-p,v=-v);const b=t.touchesDirection;t.swipeDirection=p>0?"prev":"next",t.touchesDirection=v>0?"prev":"next";const S=t.params.loop&&!n.cssMode,c=t.touchesDirection==="next"&&t.allowSlideNext||t.touchesDirection==="prev"&&t.allowSlidePrev;if(!s.isMoved){if(S&&c&&t.loopFix({direction:t.swipeDirection}),s.startTranslate=t.getTranslate(),t.setTransition(0),t.animating){const T=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});t.wrapperEl.dispatchEvent(T)}s.allowMomentumBounce=!1,n.grabCursor&&(t.allowSlideNext===!0||t.allowSlidePrev===!0)&&t.setGrabCursor(!0),t.emit("sliderFirstMove",d)}if(new Date().getTime(),n._loopSwapReset!==!1&&s.isMoved&&s.allowThresholdMove&&b!==t.touchesDirection&&S&&c&&Math.abs(p)>=1){Object.assign(r,{startX:u,startY:m,currentX:u,currentY:m,startTranslate:s.currentTranslate}),s.loopSwapReset=!0,s.startTranslate=s.currentTranslate;return}t.emit("sliderMove",d),s.isMoved=!0,s.currentTranslate=p+s.startTranslate;let f=!0,w=n.resistanceRatio;if(n.touchReleaseOnEdges&&(w=0),p>0?(S&&c&&s.allowThresholdMove&&s.currentTranslate>(n.centeredSlides?t.minTranslate()-t.slidesSizesGrid[t.activeIndex+1]-(n.slidesPerView!=="auto"&&t.slides.length-n.slidesPerView>=2?t.slidesSizesGrid[t.activeIndex+1]+t.params.spaceBetween:0)-t.params.spaceBetween:t.minTranslate())&&t.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),s.currentTranslate>t.minTranslate()&&(f=!1,n.resistance&&(s.currentTranslate=t.minTranslate()-1+(-t.minTranslate()+s.startTranslate+p)**w))):p<0&&(S&&c&&s.allowThresholdMove&&s.currentTranslate<(n.centeredSlides?t.maxTranslate()+t.slidesSizesGrid[t.slidesSizesGrid.length-1]+t.params.spaceBetween+(n.slidesPerView!=="auto"&&t.slides.length-n.slidesPerView>=2?t.slidesSizesGrid[t.slidesSizesGrid.length-1]+t.params.spaceBetween:0):t.maxTranslate())&&t.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:t.slides.length-(n.slidesPerView==="auto"?t.slidesPerViewDynamic():Math.ceil(parseFloat(n.slidesPerView,10)))}),s.currentTranslate<t.maxTranslate()&&(f=!1,n.resistance&&(s.currentTranslate=t.maxTranslate()+1-(t.maxTranslate()-s.startTranslate-p)**w))),f&&(d.preventedByNestedSwiper=!0),!t.allowSlideNext&&t.swipeDirection==="next"&&s.currentTranslate<s.startTranslate&&(s.currentTranslate=s.startTranslate),!t.allowSlidePrev&&t.swipeDirection==="prev"&&s.currentTranslate>s.startTranslate&&(s.currentTranslate=s.startTranslate),!t.allowSlidePrev&&!t.allowSlideNext&&(s.currentTranslate=s.startTranslate),n.threshold>0)if(Math.abs(p)>n.threshold||s.allowThresholdMove){if(!s.allowThresholdMove){s.allowThresholdMove=!0,r.startX=r.currentX,r.startY=r.currentY,s.currentTranslate=s.startTranslate,r.diff=t.isHorizontal()?r.currentX-r.startX:r.currentY-r.startY;return}}else{s.currentTranslate=s.startTranslate;return}!n.followFinger||n.cssMode||((n.freeMode&&n.freeMode.enabled&&t.freeMode||n.watchSlidesProgress)&&(t.updateActiveIndex(),t.updateSlidesClasses()),n.freeMode&&n.freeMode.enabled&&t.freeMode&&t.freeMode.onTouchMove(),t.updateProgress(s.currentTranslate),t.setTranslate(s.currentTranslate))}function qt(i){const e=this,t=e.touchEventsData;let s=i;s.originalEvent&&(s=s.originalEvent);let n;if(s.type==="touchend"||s.type==="touchcancel"){if(n=[...s.changedTouches].find(T=>T.identifier===t.touchId),!n||n.identifier!==t.touchId)return}else{if(t.touchId!==null||s.pointerId!==t.pointerId)return;n=s}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(s.type)&&!(["pointercancel","contextmenu"].includes(s.type)&&(e.browser.isSafari||e.browser.isWebView)))return;t.pointerId=null,t.touchId=null;const{params:l,touches:a,rtlTranslate:d,slidesGrid:o,enabled:u}=e;if(!u||!l.simulateTouch&&s.pointerType==="mouse")return;if(t.allowTouchCallbacks&&e.emit("touchEnd",s),t.allowTouchCallbacks=!1,!t.isTouched){t.isMoved&&l.grabCursor&&e.setGrabCursor(!1),t.isMoved=!1,t.startMoving=!1;return}l.grabCursor&&t.isMoved&&t.isTouched&&(e.allowSlideNext===!0||e.allowSlidePrev===!0)&&e.setGrabCursor(!1);const m=Z(),h=m-t.touchStartTime;if(e.allowClick){const T=s.path||s.composedPath&&s.composedPath();e.updateClickedSlide(T&&T[0]||s.target,T),e.emit("tap click",s),h<300&&m-t.lastClickTime<300&&e.emit("doubleTap doubleClick",s)}if(t.lastClickTime=Z(),xe(()=>{e.destroyed||(e.allowClick=!0)}),!t.isTouched||!t.isMoved||!e.swipeDirection||a.diff===0&&!t.loopSwapReset||t.currentTranslate===t.startTranslate&&!t.loopSwapReset){t.isTouched=!1,t.isMoved=!1,t.startMoving=!1;return}t.isTouched=!1,t.isMoved=!1,t.startMoving=!1;let g;if(l.followFinger?g=d?e.translate:-e.translate:g=-t.currentTranslate,l.cssMode)return;if(l.freeMode&&l.freeMode.enabled){e.freeMode.onTouchEnd({currentPos:g});return}const p=g>=-e.maxTranslate()&&!e.params.loop;let v=0,b=e.slidesSizesGrid[0];for(let T=0;T<o.length;T+=T<l.slidesPerGroupSkip?1:l.slidesPerGroup){const x=T<l.slidesPerGroupSkip-1?1:l.slidesPerGroup;typeof o[T+x]<"u"?(p||g>=o[T]&&g<o[T+x])&&(v=T,b=o[T+x]-o[T]):(p||g>=o[T])&&(v=T,b=o[o.length-1]-o[o.length-2])}let S=null,c=null;l.rewind&&(e.isBeginning?c=l.virtual&&l.virtual.enabled&&e.virtual?e.virtual.slides.length-1:e.slides.length-1:e.isEnd&&(S=0));const f=(g-o[v])/b,w=v<l.slidesPerGroupSkip-1?1:l.slidesPerGroup;if(h>l.longSwipesMs){if(!l.longSwipes){e.slideTo(e.activeIndex);return}e.swipeDirection==="next"&&(f>=l.longSwipesRatio?e.slideTo(l.rewind&&e.isEnd?S:v+w):e.slideTo(v)),e.swipeDirection==="prev"&&(f>1-l.longSwipesRatio?e.slideTo(v+w):c!==null&&f<0&&Math.abs(f)>l.longSwipesRatio?e.slideTo(c):e.slideTo(v))}else{if(!l.shortSwipes){e.slideTo(e.activeIndex);return}e.navigation&&(s.target===e.navigation.nextEl||s.target===e.navigation.prevEl)?s.target===e.navigation.nextEl?e.slideTo(v+w):e.slideTo(v):(e.swipeDirection==="next"&&e.slideTo(S!==null?S:v+w),e.swipeDirection==="prev"&&e.slideTo(c!==null?c:v))}}function Ne(){const i=this,{params:e,el:t}=i;if(t&&t.offsetWidth===0)return;e.breakpoints&&i.setBreakpoint();const{allowSlideNext:s,allowSlidePrev:n,snapGrid:r}=i,l=i.virtual&&i.params.virtual.enabled;i.allowSlideNext=!0,i.allowSlidePrev=!0,i.updateSize(),i.updateSlides(),i.updateSlidesClasses();const a=l&&e.loop;(e.slidesPerView==="auto"||e.slidesPerView>1)&&i.isEnd&&!i.isBeginning&&!i.params.centeredSlides&&!a?i.slideTo(i.slides.length-1,0,!1,!0):i.params.loop&&!l?i.slideToLoop(i.realIndex,0,!1,!0):i.slideTo(i.activeIndex,0,!1,!0),i.autoplay&&i.autoplay.running&&i.autoplay.paused&&(clearTimeout(i.autoplay.resizeTimeout),i.autoplay.resizeTimeout=setTimeout(()=>{i.autoplay&&i.autoplay.running&&i.autoplay.paused&&i.autoplay.resume()},500)),i.allowSlidePrev=n,i.allowSlideNext=s,i.params.watchOverflow&&r!==i.snapGrid&&i.checkOverflow()}function Xt(i){const e=this;e.enabled&&(e.allowClick||(e.params.preventClicks&&i.preventDefault(),e.params.preventClicksPropagation&&e.animating&&(i.stopPropagation(),i.stopImmediatePropagation())))}function Yt(){const i=this,{wrapperEl:e,rtlTranslate:t,enabled:s}=i;if(!s)return;i.previousTranslate=i.translate,i.isHorizontal()?i.translate=-e.scrollLeft:i.translate=-e.scrollTop,i.translate===0&&(i.translate=0),i.updateActiveIndex(),i.updateSlidesClasses();let n;const r=i.maxTranslate()-i.minTranslate();r===0?n=0:n=(i.translate-i.minTranslate())/r,n!==i.progress&&i.updateProgress(t?-i.translate:i.translate),i.emit("setTranslate",i.translate,!1)}function Ut(i){const e=this;ne(e,i.target),!(e.params.cssMode||e.params.slidesPerView!=="auto"&&!e.params.autoHeight)&&e.update()}function Kt(){const i=this;i.documentTouchHandlerProceeded||(i.documentTouchHandlerProceeded=!0,i.params.touchReleaseOnEdges&&(i.el.style.touchAction="auto"))}const ze=(i,e)=>{const t=F(),{params:s,el:n,wrapperEl:r,device:l}=i,a=!!s.nested,d=e==="on"?"addEventListener":"removeEventListener",o=e;!n||typeof n=="string"||(t[d]("touchstart",i.onDocumentTouchStart,{passive:!1,capture:a}),n[d]("touchstart",i.onTouchStart,{passive:!1}),n[d]("pointerdown",i.onTouchStart,{passive:!1}),t[d]("touchmove",i.onTouchMove,{passive:!1,capture:a}),t[d]("pointermove",i.onTouchMove,{passive:!1,capture:a}),t[d]("touchend",i.onTouchEnd,{passive:!0}),t[d]("pointerup",i.onTouchEnd,{passive:!0}),t[d]("pointercancel",i.onTouchEnd,{passive:!0}),t[d]("touchcancel",i.onTouchEnd,{passive:!0}),t[d]("pointerout",i.onTouchEnd,{passive:!0}),t[d]("pointerleave",i.onTouchEnd,{passive:!0}),t[d]("contextmenu",i.onTouchEnd,{passive:!0}),(s.preventClicks||s.preventClicksPropagation)&&n[d]("click",i.onClick,!0),s.cssMode&&r[d]("scroll",i.onScroll),s.updateOnWindowResize?i[o](l.ios||l.android?"resize orientationchange observerUpdate":"resize observerUpdate",Ne,!0):i[o]("observerUpdate",Ne,!0),n[d]("load",i.onLoad,{capture:!0}))};function Qt(){const i=this,{params:e}=i;i.onTouchStart=Wt.bind(i),i.onTouchMove=jt.bind(i),i.onTouchEnd=qt.bind(i),i.onDocumentTouchStart=Kt.bind(i),e.cssMode&&(i.onScroll=Yt.bind(i)),i.onClick=Xt.bind(i),i.onLoad=Ut.bind(i),ze(i,"on")}function Jt(){ze(this,"off")}var Zt={attachEvents:Qt,detachEvents:Jt};const ke=(i,e)=>i.grid&&e.grid&&e.grid.rows>1;function ei(){const i=this,{realIndex:e,initialized:t,params:s,el:n}=i,r=s.breakpoints;if(!r||r&&Object.keys(r).length===0)return;const l=F(),a=s.breakpointsBase==="window"||!s.breakpointsBase?s.breakpointsBase:"container",d=["window","container"].includes(s.breakpointsBase)||!s.breakpointsBase?i.el:l.querySelector(s.breakpointsBase),o=i.getBreakpoint(r,a,d);if(!o||i.currentBreakpoint===o)return;const m=(o in r?r[o]:void 0)||i.originalParams,h=ke(i,s),g=ke(i,m),p=i.params.grabCursor,v=m.grabCursor,b=s.enabled;h&&!g?(n.classList.remove(`${s.containerModifierClass}grid`,`${s.containerModifierClass}grid-column`),i.emitContainerClasses()):!h&&g&&(n.classList.add(`${s.containerModifierClass}grid`),(m.grid.fill&&m.grid.fill==="column"||!m.grid.fill&&s.grid.fill==="column")&&n.classList.add(`${s.containerModifierClass}grid-column`),i.emitContainerClasses()),p&&!v?i.unsetGrabCursor():!p&&v&&i.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(x=>{if(typeof m[x]>"u")return;const C=s[x]&&s[x].enabled,I=m[x]&&m[x].enabled;C&&!I&&i[x].disable(),!C&&I&&i[x].enable()});const S=m.direction&&m.direction!==s.direction,c=s.loop&&(m.slidesPerView!==s.slidesPerView||S),f=s.loop;S&&t&&i.changeDirection(),V(i.params,m);const w=i.params.enabled,T=i.params.loop;Object.assign(i,{allowTouchMove:i.params.allowTouchMove,allowSlideNext:i.params.allowSlideNext,allowSlidePrev:i.params.allowSlidePrev}),b&&!w?i.disable():!b&&w&&i.enable(),i.currentBreakpoint=o,i.emit("_beforeBreakpoint",m),t&&(c?(i.loopDestroy(),i.loopCreate(e),i.updateSlides()):!f&&T?(i.loopCreate(e),i.updateSlides()):f&&!T&&i.loopDestroy()),i.emit("breakpoint",m)}function ti(i,e,t){if(e===void 0&&(e="window"),!i||e==="container"&&!t)return;let s=!1;const n=B(),r=e==="window"?n.innerHeight:t.clientHeight,l=Object.keys(i).map(a=>{if(typeof a=="string"&&a.indexOf("@")===0){const d=parseFloat(a.substr(1));return{value:r*d,point:a}}return{value:a,point:a}});l.sort((a,d)=>parseInt(a.value,10)-parseInt(d.value,10));for(let a=0;a<l.length;a+=1){const{point:d,value:o}=l[a];e==="window"?n.matchMedia(`(min-width: ${o}px)`).matches&&(s=d):o<=t.clientWidth&&(s=d)}return s||"max"}var ii={setBreakpoint:ei,getBreakpoint:ti};function si(i,e){const t=[];return i.forEach(s=>{typeof s=="object"?Object.keys(s).forEach(n=>{s[n]&&t.push(e+n)}):typeof s=="string"&&t.push(e+s)}),t}function ri(){const i=this,{classNames:e,params:t,rtl:s,el:n,device:r}=i,l=si(["initialized",t.direction,{"free-mode":i.params.freeMode&&t.freeMode.enabled},{autoheight:t.autoHeight},{rtl:s},{grid:t.grid&&t.grid.rows>1},{"grid-column":t.grid&&t.grid.rows>1&&t.grid.fill==="column"},{android:r.android},{ios:r.ios},{"css-mode":t.cssMode},{centered:t.cssMode&&t.centeredSlides},{"watch-progress":t.watchSlidesProgress}],t.containerModifierClass);e.push(...l),n.classList.add(...e),i.emitContainerClasses()}function ni(){const i=this,{el:e,classNames:t}=i;!e||typeof e=="string"||(e.classList.remove(...t),i.emitContainerClasses())}var ai={addClasses:ri,removeClasses:ni};function li(){const i=this,{isLocked:e,params:t}=i,{slidesOffsetBefore:s}=t;if(s){const n=i.slides.length-1,r=i.slidesGrid[n]+i.slidesSizesGrid[n]+s*2;i.isLocked=i.size>r}else i.isLocked=i.snapGrid.length===1;t.allowSlideNext===!0&&(i.allowSlideNext=!i.isLocked),t.allowSlidePrev===!0&&(i.allowSlidePrev=!i.isLocked),e&&e!==i.isLocked&&(i.isEnd=!1),e!==i.isLocked&&i.emit(i.isLocked?"lock":"unlock")}var oi={checkOverflow:li},Ge={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function di(i,e){return function(s){s===void 0&&(s={});const n=Object.keys(s)[0],r=s[n];if(typeof r!="object"||r===null){V(e,s);return}if(i[n]===!0&&(i[n]={enabled:!0}),n==="navigation"&&i[n]&&i[n].enabled&&!i[n].prevEl&&!i[n].nextEl&&(i[n].auto=!0),["pagination","scrollbar"].indexOf(n)>=0&&i[n]&&i[n].enabled&&!i[n].el&&(i[n].auto=!0),!(n in i&&"enabled"in r)){V(e,s);return}typeof i[n]=="object"&&!("enabled"in i[n])&&(i[n].enabled=!0),i[n]||(i[n]={enabled:!1}),V(e,s)}}const ge={eventsEmitter:at,update:vt,translate:Et,transition:Mt,slide:zt,loop:Rt,grabCursor:Ft,events:Zt,breakpoints:ii,checkOverflow:oi,classes:ai},ve={};class ${constructor(){let e,t;for(var s=arguments.length,n=new Array(s),r=0;r<s;r++)n[r]=arguments[r];n.length===1&&n[0].constructor&&Object.prototype.toString.call(n[0]).slice(8,-1)==="Object"?t=n[0]:[e,t]=n,t||(t={}),t=V({},t),e&&!t.el&&(t.el=e);const l=F();if(t.el&&typeof t.el=="string"&&l.querySelectorAll(t.el).length>1){const u=[];return l.querySelectorAll(t.el).forEach(m=>{const h=V({},t,{el:m});u.push(new $(h))}),u}const a=this;a.__swiper__=!0,a.support=Le(),a.device=Pe({userAgent:t.userAgent}),a.browser=Ae(),a.eventsListeners={},a.eventsAnyListeners=[],a.modules=[...a.__modules__],t.modules&&Array.isArray(t.modules)&&a.modules.push(...t.modules);const d={};a.modules.forEach(u=>{u({params:t,swiper:a,extendParams:di(t,d),on:a.on.bind(a),once:a.once.bind(a),off:a.off.bind(a),emit:a.emit.bind(a)})});const o=V({},Ge,d);return a.params=V({},o,ve,t),a.originalParams=V({},a.params),a.passedParams=V({},t),a.params&&a.params.on&&Object.keys(a.params.on).forEach(u=>{a.on(u,a.params.on[u])}),a.params&&a.params.onAny&&a.onAny(a.params.onAny),Object.assign(a,{enabled:a.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return a.params.direction==="horizontal"},isVertical(){return a.params.direction==="vertical"},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:a.params.allowSlideNext,allowSlidePrev:a.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:a.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:a.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),a.emit("_swiper"),a.params.init&&a.init(),a}getDirectionLabel(e){return this.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}getSlideIndex(e){const{slidesEl:t,params:s}=this,n=H(t,`.${s.slideClass}, swiper-slide`),r=re(n[0]);return re(e)-r}getSlideIndexByData(e){return this.getSlideIndex(this.slides.find(t=>t.getAttribute("data-swiper-slide-index")*1===e))}getSlideIndexWhenGrid(e){return this.grid&&this.params.grid&&this.params.grid.rows>1&&(this.params.grid.fill==="column"?e=Math.floor(e/this.params.grid.rows):this.params.grid.fill==="row"&&(e=e%Math.ceil(this.slides.length/this.params.grid.rows))),e}recalcSlides(){const e=this,{slidesEl:t,params:s}=e;e.slides=H(t,`.${s.slideClass}, swiper-slide`)}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){const s=this;e=Math.min(Math.max(e,0),1);const n=s.minTranslate(),l=(s.maxTranslate()-n)*e+n;s.translateTo(l,typeof t>"u"?0:t),s.updateActiveIndex(),s.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=e.el.className.split(" ").filter(s=>s.indexOf("swiper")===0||s.indexOf(e.params.containerModifierClass)===0);e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){const t=this;return t.destroyed?"":e.className.split(" ").filter(s=>s.indexOf("swiper-slide")===0||s.indexOf(t.params.slideClass)===0).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=[];e.slides.forEach(s=>{const n=e.getSlideClasses(s);t.push({slideEl:s,classNames:n}),e.emit("_slideClass",s,n)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){e===void 0&&(e="current"),t===void 0&&(t=!1);const s=this,{params:n,slides:r,slidesGrid:l,slidesSizesGrid:a,size:d,activeIndex:o}=s;let u=1;if(typeof n.slidesPerView=="number")return n.slidesPerView;if(n.centeredSlides){let m=r[o]?Math.ceil(r[o].swiperSlideSize):0,h;for(let g=o+1;g<r.length;g+=1)r[g]&&!h&&(m+=Math.ceil(r[g].swiperSlideSize),u+=1,m>d&&(h=!0));for(let g=o-1;g>=0;g-=1)r[g]&&!h&&(m+=r[g].swiperSlideSize,u+=1,m>d&&(h=!0))}else if(e==="current")for(let m=o+1;m<r.length;m+=1)(t?l[m]+a[m]-l[o]<d:l[m]-l[o]<d)&&(u+=1);else for(let m=o-1;m>=0;m-=1)l[o]-l[m]<d&&(u+=1);return u}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:t,params:s}=e;s.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach(l=>{l.complete&&ne(e,l)}),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses();function n(){const l=e.rtlTranslate?e.translate*-1:e.translate,a=Math.min(Math.max(l,e.maxTranslate()),e.minTranslate());e.setTranslate(a),e.updateActiveIndex(),e.updateSlidesClasses()}let r;if(s.freeMode&&s.freeMode.enabled&&!s.cssMode)n(),s.autoHeight&&e.updateAutoHeight();else{if((s.slidesPerView==="auto"||s.slidesPerView>1)&&e.isEnd&&!s.centeredSlides){const l=e.virtual&&s.virtual.enabled?e.virtual.slides:e.slides;r=e.slideTo(l.length-1,0,!1,!0)}else r=e.slideTo(e.activeIndex,0,!1,!0);r||n()}s.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,t){t===void 0&&(t=!0);const s=this,n=s.params.direction;return e||(e=n==="horizontal"?"vertical":"horizontal"),e===n||e!=="horizontal"&&e!=="vertical"||(s.el.classList.remove(`${s.params.containerModifierClass}${n}`),s.el.classList.add(`${s.params.containerModifierClass}${e}`),s.emitContainerClasses(),s.params.direction=e,s.slides.forEach(r=>{e==="vertical"?r.style.width="":r.style.height=""}),s.emit("changeDirection"),t&&s.update()),s}changeLanguageDirection(e){const t=this;t.rtl&&e==="rtl"||!t.rtl&&e==="ltr"||(t.rtl=e==="rtl",t.rtlTranslate=t.params.direction==="horizontal"&&t.rtl,t.rtl?(t.el.classList.add(`${t.params.containerModifierClass}rtl`),t.el.dir="rtl"):(t.el.classList.remove(`${t.params.containerModifierClass}rtl`),t.el.dir="ltr"),t.update())}mount(e){const t=this;if(t.mounted)return!0;let s=e||t.params.el;if(typeof s=="string"&&(s=document.querySelector(s)),!s)return!1;s.swiper=t,s.parentNode&&s.parentNode.host&&s.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);const n=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`;let l=s&&s.shadowRoot&&s.shadowRoot.querySelector?s.shadowRoot.querySelector(n()):H(s,n())[0];return!l&&t.params.createElements&&(l=se("div",t.params.wrapperClass),s.append(l),H(s,`.${t.params.slideClass}`).forEach(a=>{l.append(a)})),Object.assign(t,{el:s,wrapperEl:l,slidesEl:t.isElement&&!s.parentNode.host.slideSlots?s.parentNode.host:l,hostEl:t.isElement?s.parentNode.host:s,mounted:!0,rtl:s.dir.toLowerCase()==="rtl"||W(s,"direction")==="rtl",rtlTranslate:t.params.direction==="horizontal"&&(s.dir.toLowerCase()==="rtl"||W(s,"direction")==="rtl"),wrongRTL:W(l,"display")==="-webkit-box"}),!0}init(e){const t=this;if(t.initialized||t.mount(e)===!1)return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(void 0,!0),t.attachEvents();const n=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&n.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),n.forEach(r=>{r.complete?ne(t,r):r.addEventListener("load",l=>{ne(t,l.target)})}),he(t),t.initialized=!0,he(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){e===void 0&&(e=!0),t===void 0&&(t=!0);const s=this,{params:n,el:r,wrapperEl:l,slides:a}=s;return typeof s.params>"u"||s.destroyed||(s.emit("beforeDestroy"),s.initialized=!1,s.detachEvents(),n.loop&&s.loopDestroy(),t&&(s.removeClasses(),r&&typeof r!="string"&&r.removeAttribute("style"),l&&l.removeAttribute("style"),a&&a.length&&a.forEach(d=>{d.classList.remove(n.slideVisibleClass,n.slideFullyVisibleClass,n.slideActiveClass,n.slideNextClass,n.slidePrevClass),d.removeAttribute("style"),d.removeAttribute("data-swiper-slide-index")})),s.emit("destroy"),Object.keys(s.eventsListeners).forEach(d=>{s.off(d)}),e!==!1&&(s.el&&typeof s.el!="string"&&(s.el.swiper=null),Xe(s)),s.destroyed=!0),null}static extendDefaults(e){V(ve,e)}static get extendedDefaults(){return ve}static get defaults(){return Ge}static installModule(e){$.prototype.__modules__||($.prototype.__modules__=[]);const t=$.prototype.__modules__;typeof e=="function"&&t.indexOf(e)<0&&t.push(e)}static use(e){return Array.isArray(e)?(e.forEach(t=>$.installModule(t)),$):($.installModule(e),$)}}Object.keys(ge).forEach(i=>{Object.keys(ge[i]).forEach(e=>{$.prototype[e]=ge[i][e]})}),$.use([rt,nt]);function Be(i,e,t,s){return i.params.createElements&&Object.keys(s).forEach(n=>{if(!t[n]&&t.auto===!0){let r=H(i.el,`.${s[n]}`)[0];r||(r=se("div",s[n]),r.className=s[n],i.el.append(r)),t[n]=r,e[n]=r}}),t}function ci(i){let{swiper:e,extendParams:t,on:s,emit:n}=i;t({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),e.navigation={nextEl:null,prevEl:null};function r(p){let v;return p&&typeof p=="string"&&e.isElement&&(v=e.el.querySelector(p)||e.hostEl.querySelector(p),v)?v:(p&&(typeof p=="string"&&(v=[...document.querySelectorAll(p)]),e.params.uniqueNavElements&&typeof p=="string"&&v&&v.length>1&&e.el.querySelectorAll(p).length===1?v=e.el.querySelector(p):v&&v.length===1&&(v=v[0])),p&&!v?p:v)}function l(p,v){const b=e.params.navigation;p=k(p),p.forEach(S=>{S&&(S.classList[v?"add":"remove"](...b.disabledClass.split(" ")),S.tagName==="BUTTON"&&(S.disabled=v),e.params.watchOverflow&&e.enabled&&S.classList[e.isLocked?"add":"remove"](b.lockClass))})}function a(){const{nextEl:p,prevEl:v}=e.navigation;if(e.params.loop){l(v,!1),l(p,!1);return}l(v,e.isBeginning&&!e.params.rewind),l(p,e.isEnd&&!e.params.rewind)}function d(p){p.preventDefault(),!(e.isBeginning&&!e.params.loop&&!e.params.rewind)&&(e.slidePrev(),n("navigationPrev"))}function o(p){p.preventDefault(),!(e.isEnd&&!e.params.loop&&!e.params.rewind)&&(e.slideNext(),n("navigationNext"))}function u(){const p=e.params.navigation;if(e.params.navigation=Be(e,e.originalParams.navigation,e.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(p.nextEl||p.prevEl))return;let v=r(p.nextEl),b=r(p.prevEl);Object.assign(e.navigation,{nextEl:v,prevEl:b}),v=k(v),b=k(b);const S=(c,f)=>{c&&c.addEventListener("click",f==="next"?o:d),!e.enabled&&c&&c.classList.add(...p.lockClass.split(" "))};v.forEach(c=>S(c,"next")),b.forEach(c=>S(c,"prev"))}function m(){let{nextEl:p,prevEl:v}=e.navigation;p=k(p),v=k(v);const b=(S,c)=>{S.removeEventListener("click",c==="next"?o:d),S.classList.remove(...e.params.navigation.disabledClass.split(" "))};p.forEach(S=>b(S,"next")),v.forEach(S=>b(S,"prev"))}s("init",()=>{e.params.navigation.enabled===!1?g():(u(),a())}),s("toEdge fromEdge lock unlock",()=>{a()}),s("destroy",()=>{m()}),s("enable disable",()=>{let{nextEl:p,prevEl:v}=e.navigation;if(p=k(p),v=k(v),e.enabled){a();return}[...p,...v].filter(b=>!!b).forEach(b=>b.classList.add(e.params.navigation.lockClass))}),s("click",(p,v)=>{let{nextEl:b,prevEl:S}=e.navigation;b=k(b),S=k(S);const c=v.target;let f=S.includes(c)||b.includes(c);if(e.isElement&&!f){const w=v.path||v.composedPath&&v.composedPath();w&&(f=w.find(T=>b.includes(T)||S.includes(T)))}if(e.params.navigation.hideOnClick&&!f){if(e.pagination&&e.params.pagination&&e.params.pagination.clickable&&(e.pagination.el===c||e.pagination.el.contains(c)))return;let w;b.length?w=b[0].classList.contains(e.params.navigation.hiddenClass):S.length&&(w=S[0].classList.contains(e.params.navigation.hiddenClass)),n(w===!0?"navigationShow":"navigationHide"),[...b,...S].filter(T=>!!T).forEach(T=>T.classList.toggle(e.params.navigation.hiddenClass))}});const h=()=>{e.el.classList.remove(...e.params.navigation.navigationDisabledClass.split(" ")),u(),a()},g=()=>{e.el.classList.add(...e.params.navigation.navigationDisabledClass.split(" ")),m()};Object.assign(e.navigation,{enable:h,disable:g,update:a,init:u,destroy:m})}function J(i){return i===void 0&&(i=""),`.${i.trim().replace(/([\.:!+\/()[\]])/g,"\\$1").replace(/ /g,".")}`}function ui(i){let{swiper:e,extendParams:t,on:s,emit:n}=i;const r="swiper-pagination";t({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:c=>c,formatFractionTotal:c=>c,bulletClass:`${r}-bullet`,bulletActiveClass:`${r}-bullet-active`,modifierClass:`${r}-`,currentClass:`${r}-current`,totalClass:`${r}-total`,hiddenClass:`${r}-hidden`,progressbarFillClass:`${r}-progressbar-fill`,progressbarOppositeClass:`${r}-progressbar-opposite`,clickableClass:`${r}-clickable`,lockClass:`${r}-lock`,horizontalClass:`${r}-horizontal`,verticalClass:`${r}-vertical`,paginationDisabledClass:`${r}-disabled`}}),e.pagination={el:null,bullets:[]};let l,a=0;function d(){return!e.params.pagination.el||!e.pagination.el||Array.isArray(e.pagination.el)&&e.pagination.el.length===0}function o(c,f){const{bulletActiveClass:w}=e.params.pagination;c&&(c=c[`${f==="prev"?"previous":"next"}ElementSibling`],c&&(c.classList.add(`${w}-${f}`),c=c[`${f==="prev"?"previous":"next"}ElementSibling`],c&&c.classList.add(`${w}-${f}-${f}`)))}function u(c,f,w){if(c=c%w,f=f%w,f===c+1)return"next";if(f===c-1)return"previous"}function m(c){const f=c.target.closest(J(e.params.pagination.bulletClass));if(!f)return;c.preventDefault();const w=re(f)*e.params.slidesPerGroup;if(e.params.loop){if(e.realIndex===w)return;const T=u(e.realIndex,w,e.slides.length);T==="next"?e.slideNext():T==="previous"?e.slidePrev():e.slideToLoop(w)}else e.slideTo(w)}function h(){const c=e.rtl,f=e.params.pagination;if(d())return;let w=e.pagination.el;w=k(w);let T,x;const C=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,I=e.params.loop?Math.ceil(C/e.params.slidesPerGroup):e.snapGrid.length;if(e.params.loop?(x=e.previousRealIndex||0,T=e.params.slidesPerGroup>1?Math.floor(e.realIndex/e.params.slidesPerGroup):e.realIndex):typeof e.snapIndex<"u"?(T=e.snapIndex,x=e.previousSnapIndex):(x=e.previousIndex||0,T=e.activeIndex||0),f.type==="bullets"&&e.pagination.bullets&&e.pagination.bullets.length>0){const M=e.pagination.bullets;let y,E,A;if(f.dynamicBullets&&(l=de(M[0],e.isHorizontal()?"width":"height"),w.forEach(O=>{O.style[e.isHorizontal()?"width":"height"]=`${l*(f.dynamicMainBullets+4)}px`}),f.dynamicMainBullets>1&&x!==void 0&&(a+=T-(x||0),a>f.dynamicMainBullets-1?a=f.dynamicMainBullets-1:a<0&&(a=0)),y=Math.max(T-a,0),E=y+(Math.min(M.length,f.dynamicMainBullets)-1),A=(E+y)/2),M.forEach(O=>{const _=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(D=>`${f.bulletActiveClass}${D}`)].map(D=>typeof D=="string"&&D.includes(" ")?D.split(" "):D).flat();O.classList.remove(..._)}),w.length>1)M.forEach(O=>{const _=re(O);_===T?O.classList.add(...f.bulletActiveClass.split(" ")):e.isElement&&O.setAttribute("part","bullet"),f.dynamicBullets&&(_>=y&&_<=E&&O.classList.add(...`${f.bulletActiveClass}-main`.split(" ")),_===y&&o(O,"prev"),_===E&&o(O,"next"))});else{const O=M[T];if(O&&O.classList.add(...f.bulletActiveClass.split(" ")),e.isElement&&M.forEach((_,D)=>{_.setAttribute("part",D===T?"bullet-active":"bullet")}),f.dynamicBullets){const _=M[y],D=M[E];for(let L=y;L<=E;L+=1)M[L]&&M[L].classList.add(...`${f.bulletActiveClass}-main`.split(" "));o(_,"prev"),o(D,"next")}}if(f.dynamicBullets){const O=Math.min(M.length,f.dynamicMainBullets+4),_=(l*O-l)/2-A*l,D=c?"right":"left";M.forEach(L=>{L.style[e.isHorizontal()?D:"top"]=`${_}px`})}}w.forEach((M,y)=>{if(f.type==="fraction"&&(M.querySelectorAll(J(f.currentClass)).forEach(E=>{E.textContent=f.formatFractionCurrent(T+1)}),M.querySelectorAll(J(f.totalClass)).forEach(E=>{E.textContent=f.formatFractionTotal(I)})),f.type==="progressbar"){let E;f.progressbarOpposite?E=e.isHorizontal()?"vertical":"horizontal":E=e.isHorizontal()?"horizontal":"vertical";const A=(T+1)/I;let O=1,_=1;E==="horizontal"?O=A:_=A,M.querySelectorAll(J(f.progressbarFillClass)).forEach(D=>{D.style.transform=`translate3d(0,0,0) scaleX(${O}) scaleY(${_})`,D.style.transitionDuration=`${e.params.speed}ms`})}f.type==="custom"&&f.renderCustom?(Me(M,f.renderCustom(e,T+1,I)),y===0&&n("paginationRender",M)):(y===0&&n("paginationRender",M),n("paginationUpdate",M)),e.params.watchOverflow&&e.enabled&&M.classList[e.isLocked?"add":"remove"](f.lockClass)})}function g(){const c=e.params.pagination;if(d())return;const f=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.grid&&e.params.grid.rows>1?e.slides.length/Math.ceil(e.params.grid.rows):e.slides.length;let w=e.pagination.el;w=k(w);let T="";if(c.type==="bullets"){let x=e.params.loop?Math.ceil(f/e.params.slidesPerGroup):e.snapGrid.length;e.params.freeMode&&e.params.freeMode.enabled&&x>f&&(x=f);for(let C=0;C<x;C+=1)c.renderBullet?T+=c.renderBullet.call(e,C,c.bulletClass):T+=`<${c.bulletElement} ${e.isElement?'part="bullet"':""} class="${c.bulletClass}"></${c.bulletElement}>`}c.type==="fraction"&&(c.renderFraction?T=c.renderFraction.call(e,c.currentClass,c.totalClass):T=`<span class="${c.currentClass}"></span> / <span class="${c.totalClass}"></span>`),c.type==="progressbar"&&(c.renderProgressbar?T=c.renderProgressbar.call(e,c.progressbarFillClass):T=`<span class="${c.progressbarFillClass}"></span>`),e.pagination.bullets=[],w.forEach(x=>{c.type!=="custom"&&Me(x,T||""),c.type==="bullets"&&e.pagination.bullets.push(...x.querySelectorAll(J(c.bulletClass)))}),c.type!=="custom"&&n("paginationRender",w[0])}function p(){e.params.pagination=Be(e,e.originalParams.pagination,e.params.pagination,{el:"swiper-pagination"});const c=e.params.pagination;if(!c.el)return;let f;typeof c.el=="string"&&e.isElement&&(f=e.el.querySelector(c.el)),!f&&typeof c.el=="string"&&(f=[...document.querySelectorAll(c.el)]),f||(f=c.el),!(!f||f.length===0)&&(e.params.uniqueNavElements&&typeof c.el=="string"&&Array.isArray(f)&&f.length>1&&(f=[...e.el.querySelectorAll(c.el)],f.length>1&&(f=f.find(w=>Ce(w,".swiper")[0]===e.el))),Array.isArray(f)&&f.length===1&&(f=f[0]),Object.assign(e.pagination,{el:f}),f=k(f),f.forEach(w=>{c.type==="bullets"&&c.clickable&&w.classList.add(...(c.clickableClass||"").split(" ")),w.classList.add(c.modifierClass+c.type),w.classList.add(e.isHorizontal()?c.horizontalClass:c.verticalClass),c.type==="bullets"&&c.dynamicBullets&&(w.classList.add(`${c.modifierClass}${c.type}-dynamic`),a=0,c.dynamicMainBullets<1&&(c.dynamicMainBullets=1)),c.type==="progressbar"&&c.progressbarOpposite&&w.classList.add(c.progressbarOppositeClass),c.clickable&&w.addEventListener("click",m),e.enabled||w.classList.add(c.lockClass)}))}function v(){const c=e.params.pagination;if(d())return;let f=e.pagination.el;f&&(f=k(f),f.forEach(w=>{w.classList.remove(c.hiddenClass),w.classList.remove(c.modifierClass+c.type),w.classList.remove(e.isHorizontal()?c.horizontalClass:c.verticalClass),c.clickable&&(w.classList.remove(...(c.clickableClass||"").split(" ")),w.removeEventListener("click",m))})),e.pagination.bullets&&e.pagination.bullets.forEach(w=>w.classList.remove(...c.bulletActiveClass.split(" ")))}s("changeDirection",()=>{if(!e.pagination||!e.pagination.el)return;const c=e.params.pagination;let{el:f}=e.pagination;f=k(f),f.forEach(w=>{w.classList.remove(c.horizontalClass,c.verticalClass),w.classList.add(e.isHorizontal()?c.horizontalClass:c.verticalClass)})}),s("init",()=>{e.params.pagination.enabled===!1?S():(p(),g(),h())}),s("activeIndexChange",()=>{typeof e.snapIndex>"u"&&h()}),s("snapIndexChange",()=>{h()}),s("snapGridLengthChange",()=>{g(),h()}),s("destroy",()=>{v()}),s("enable disable",()=>{let{el:c}=e.pagination;c&&(c=k(c),c.forEach(f=>f.classList[e.enabled?"remove":"add"](e.params.pagination.lockClass)))}),s("lock unlock",()=>{h()}),s("click",(c,f)=>{const w=f.target,T=k(e.pagination.el);if(e.params.pagination.el&&e.params.pagination.hideOnClick&&T&&T.length>0&&!w.classList.contains(e.params.pagination.bulletClass)){if(e.navigation&&(e.navigation.nextEl&&w===e.navigation.nextEl||e.navigation.prevEl&&w===e.navigation.prevEl))return;const x=T[0].classList.contains(e.params.pagination.hiddenClass);n(x===!0?"paginationShow":"paginationHide"),T.forEach(C=>C.classList.toggle(e.params.pagination.hiddenClass))}});const b=()=>{e.el.classList.remove(e.params.pagination.paginationDisabledClass);let{el:c}=e.pagination;c&&(c=k(c),c.forEach(f=>f.classList.remove(e.params.pagination.paginationDisabledClass))),p(),g(),h()},S=()=>{e.el.classList.add(e.params.pagination.paginationDisabledClass);let{el:c}=e.pagination;c&&(c=k(c),c.forEach(f=>f.classList.add(e.params.pagination.paginationDisabledClass))),v()};Object.assign(e.pagination,{enable:b,disable:S,render:g,update:h,init:p,destroy:v})}function fi(i){let{swiper:e,extendParams:t,on:s,emit:n,params:r}=i;e.autoplay={running:!1,paused:!1,timeLeft:0},t({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let l,a,d=r&&r.autoplay?r.autoplay.delay:3e3,o=r&&r.autoplay?r.autoplay.delay:3e3,u,m=new Date().getTime(),h,g,p,v,b,S,c;function f(P){!e||e.destroyed||!e.wrapperEl||P.target===e.wrapperEl&&(e.wrapperEl.removeEventListener("transitionend",f),!(c||P.detail&&P.detail.bySwiperTouchMove)&&y())}const w=()=>{if(e.destroyed||!e.autoplay.running)return;e.autoplay.paused?h=!0:h&&(o=u,h=!1);const P=e.autoplay.paused?u:m+o-new Date().getTime();e.autoplay.timeLeft=P,n("autoplayTimeLeft",P,P/d),a=requestAnimationFrame(()=>{w()})},T=()=>{let P;return e.virtual&&e.params.virtual.enabled?P=e.slides.find(R=>R.classList.contains("swiper-slide-active")):P=e.slides[e.activeIndex],P?parseInt(P.getAttribute("data-swiper-autoplay"),10):void 0},x=P=>{if(e.destroyed||!e.autoplay.running)return;cancelAnimationFrame(a),w();let z=typeof P>"u"?e.params.autoplay.delay:P;d=e.params.autoplay.delay,o=e.params.autoplay.delay;const R=T();!Number.isNaN(R)&&R>0&&typeof P>"u"&&(z=R,d=R,o=R),u=z;const U=e.params.speed,le=()=>{!e||e.destroyed||(e.params.autoplay.reverseDirection?!e.isBeginning||e.params.loop||e.params.rewind?(e.slidePrev(U,!0,!0),n("autoplay")):e.params.autoplay.stopOnLastSlide||(e.slideTo(e.slides.length-1,U,!0,!0),n("autoplay")):!e.isEnd||e.params.loop||e.params.rewind?(e.slideNext(U,!0,!0),n("autoplay")):e.params.autoplay.stopOnLastSlide||(e.slideTo(0,U,!0,!0),n("autoplay")),e.params.cssMode&&(m=new Date().getTime(),requestAnimationFrame(()=>{x()})))};return z>0?(clearTimeout(l),l=setTimeout(()=>{le()},z)):requestAnimationFrame(()=>{le()}),z},C=()=>{m=new Date().getTime(),e.autoplay.running=!0,x(),n("autoplayStart")},I=()=>{e.autoplay.running=!1,clearTimeout(l),cancelAnimationFrame(a),n("autoplayStop")},M=(P,z)=>{if(e.destroyed||!e.autoplay.running)return;clearTimeout(l),P||(S=!0);const R=()=>{n("autoplayPause"),e.params.autoplay.waitForTransition?e.wrapperEl.addEventListener("transitionend",f):y()};if(e.autoplay.paused=!0,z){b&&(u=e.params.autoplay.delay),b=!1,R();return}u=(u||e.params.autoplay.delay)-(new Date().getTime()-m),!(e.isEnd&&u<0&&!e.params.loop)&&(u<0&&(u=0),R())},y=()=>{e.isEnd&&u<0&&!e.params.loop||e.destroyed||!e.autoplay.running||(m=new Date().getTime(),S?(S=!1,x(u)):x(),e.autoplay.paused=!1,n("autoplayResume"))},E=()=>{if(e.destroyed||!e.autoplay.running)return;const P=F();P.visibilityState==="hidden"&&(S=!0,M(!0)),P.visibilityState==="visible"&&y()},A=P=>{P.pointerType==="mouse"&&(S=!0,c=!0,!(e.animating||e.autoplay.paused)&&M(!0))},O=P=>{P.pointerType==="mouse"&&(c=!1,e.autoplay.paused&&y())},_=()=>{e.params.autoplay.pauseOnMouseEnter&&(e.el.addEventListener("pointerenter",A),e.el.addEventListener("pointerleave",O))},D=()=>{e.el&&typeof e.el!="string"&&(e.el.removeEventListener("pointerenter",A),e.el.removeEventListener("pointerleave",O))},L=()=>{F().addEventListener("visibilitychange",E)},G=()=>{F().removeEventListener("visibilitychange",E)};s("init",()=>{e.params.autoplay.enabled&&(_(),L(),C())}),s("destroy",()=>{D(),G(),e.autoplay.running&&I()}),s("_freeModeStaticRelease",()=>{(p||S)&&y()}),s("_freeModeNoMomentumRelease",()=>{e.params.autoplay.disableOnInteraction?I():M(!0,!0)}),s("beforeTransitionStart",(P,z,R)=>{e.destroyed||!e.autoplay.running||(R||!e.params.autoplay.disableOnInteraction?M(!0,!0):I())}),s("sliderFirstMove",()=>{if(!(e.destroyed||!e.autoplay.running)){if(e.params.autoplay.disableOnInteraction){I();return}g=!0,p=!1,S=!1,v=setTimeout(()=>{S=!0,p=!0,M(!0)},200)}}),s("touchEnd",()=>{if(!(e.destroyed||!e.autoplay.running||!g)){if(clearTimeout(v),clearTimeout(l),e.params.autoplay.disableOnInteraction){p=!1,g=!1;return}p&&e.params.cssMode&&y(),p=!1,g=!1}}),s("slideChange",()=>{e.destroyed||!e.autoplay.running||(b=!0)}),Object.assign(e.autoplay,{start:C,stop:I,pause:M,resume:y})}const Re=i=>{try{return document.querySelector(i)||!1}catch{return!1}},pi=i=>{try{return document.querySelectorAll(i)}catch{return document.querySelectorAll("")}},X=(i,e={},t="")=>{try{const s=document.createElement(i);for(const[n,r]of Object.entries(e))n==="className"?s.className=String(r):n==="id"?s.id=String(r):s.setAttribute(n,String(r));return t&&(s.innerHTML=t),s}catch{return document.createElement("div")}},K=(i,e)=>{try{i.appendChild(e)}catch{}},mi=(i,e)=>{try{i.prepend(e)}catch{}},we=i=>{try{i.remove()}catch{}},j=(i,e)=>{try{for(const[t,s]of Object.entries(e))i.style.setProperty(t,String(s))}catch{}},Ve={USER_AGENT_SUBSTR_START:0,USER_AGENT_SUBSTR_LENGTH:4},hi={MAX_ERROR_LOG_ENTRIES:50,DOM_READY_TIMEOUT:5e3,SLIDE_NUMBER_MIN:1,SLIDE_NUMBER_MAX:30,TRANSITION_TIME_MIN:1e3,TRANSITION_TIME_MAX:3e4,CONFIG_MAX_SLIDES_MIN:1,CONFIG_MAX_SLIDES_MAX:50},Se={SLIDE_INCREMENT:1,INITIAL_SLIDE_INDEX:1},Te={CHECK_INTERVAL:10,DATA_CHECK_INTERVAL:100,DEFAULT_TRANSITION_TIME:5e3},$e={SWIPER_AD_CONTAINER_ID:"swiperAdContainer",HEADER_ICON_ID:"wusong8899HeaderAdvIcon"},gi={AD_SWIPER:"adSwiper"},ye={SWIPER_PAGINATION_EL:".swiper-pagination",SWIPER_BUTTON_NEXT_EL:".swiper-button-next",SWIPER_BUTTON_PREV_EL:".swiper-button-prev"},ae={ID:"wusong8899-header-advertisement",TRANSLATION_PREFIX:"wusong8899-header-advertisement",MAX_SLIDES:30,HEADER_ICON_URL:"https://ex.cc/assets/files/date/test.png"},vi=()=>{try{const{userAgent:i}=navigator;return i.substring(Ve.USER_AGENT_SUBSTR_START,Ve.USER_AGENT_SUBSTR_LENGTH)==="Mobi"}catch{return!1}},N={env:"production",app:{extensionId:ae.ID,translationPrefix:ae.TRANSLATION_PREFIX},slider:{maxSlides:ae.MAX_SLIDES,defaultTransitionTime:Te.DEFAULT_TRANSITION_TIME,checkTime:Te.CHECK_INTERVAL,dataCheckInterval:Te.DATA_CHECK_INTERVAL,dom:{containerId:$e.SWIPER_AD_CONTAINER_ID,swiperClass:gi.AD_SWIPER},swiper:{spaceBetween:20,effect:"slide",centeredSlides:!0,slidesPerView:1.5,pagination:{el:ye.SWIPER_PAGINATION_EL,type:"bullets"},navigation:{nextEl:ye.SWIPER_BUTTON_NEXT_EL,prevEl:ye.SWIPER_BUTTON_PREV_EL}}},ui:{headerIconId:$e.HEADER_ICON_ID,headerIconUrl:ae.HEADER_ICON_URL}},Fe={MIN_SLIDES_FOR_DUAL_VIEW:8,MIN_SLIDES_FOR_SINGLE_VIEW_LOOP:4};class wi{constructor(){this.maxSlides=N.slider.maxSlides,this.checkTime=N.slider.checkTime}getForumAttribute(e){try{const t=q&&q.forum,s=t&&t.attribute;return typeof s=="function"?s.call(t,e):void 0}catch{return}}attachAdvertiseHeader(e){try{this.destroy();const t=this.createContainer(),s=this.createSwiperElement(t),n=this.createSwiperWrapper(s),r=this.populateSlides(n);this.createPagination(s),this.createNavigation(s),this.container=t,this.appendToDOM(t),setTimeout(()=>{this.initializeSwiper(this.getTransitionTime(),r)},this.checkTime)}catch{}}removeExistingNavigation(){const e=Re(`#${N.slider.dom.containerId}`);e&&we(e);const t=pi(".item-nav");for(const s of t)we(s)}createContainer(){this.removeExistingNavigation();const e=X("div",{id:N.slider.dom.containerId,className:"adContainer"});return e.style.cssText=`
            display: block !important;
            min-height: 300px !important;
            background: transparent !important;
            border: none !important;
            padding: 0 !important;
            max-width: 1200px !important;
            width: 100% !important;
            margin: 0 auto 20px auto !important;
            overflow: hidden !important;
        `,e}createSwiperElement(e){const t=X("div",{className:`swiper ${N.slider.dom.swiperClass}`});let s="300px";return vi()&&(s="200px"),t.style.cssText=`
            width: 100% !important;
            height: ${s} !important;
            position: relative !important;
            display: block !important;
            min-height: ${s} !important;
            background: transparent !important;
            border: none !important;
            border-radius: 12px !important;
            overflow: hidden !important;
        `,K(e,t),t}createSwiperWrapper(e){const t=X("div",{className:"swiper-wrapper"});return j(t,{width:"100%",height:"100%",display:"flex"}),K(e,t),t}getTransitionTime(){const e=this.getForumAttribute("wusong8899-header-advertisement.TransitionTime");return e?Number.parseInt(String(e),10):N.slider.defaultTransitionTime}populateSlides(e){let t=0;for(let s=Se.INITIAL_SLIDE_INDEX;s<=this.maxSlides;s+=Se.SLIDE_INCREMENT){const n=this.getForumAttribute(`wusong8899-header-advertisement.Image${s}`),r=this.getForumAttribute(`wusong8899-header-advertisement.Link${s}`);if(n){const l=this.createSlide(String(n),String(r||""));K(e,l),t+=Se.SLIDE_INCREMENT}}return t}createSlide(e,t){const s=X("div",{className:"swiper-slide"});j(s,{width:"100%",height:"100%",display:"flex","align-items":"center","justify-content":"center"});let n="";return t&&(n=`window.location.href="${t}"`),s.innerHTML=`<img onclick='${n}' src='${e}' style='width: 100%; height: 100%; object-fit: cover; display: block;' />`,s}createPagination(e){const t=X("div",{className:"swiper-pagination"});K(e,t)}createNavigation(e){const t=X("div",{className:"swiper-button-prev"}),s=X("div",{className:"swiper-button-next"});j(t,{position:"absolute",top:"50%",left:"15px",transform:"translateY(-50%)","z-index":"100",width:"40px",height:"40px",background:"rgba(255,255,255,0.9)",color:"#333",border:"1px solid rgba(0,0,0,0.1)","border-radius":"50%",cursor:"pointer",display:"flex","align-items":"center","justify-content":"center","font-size":"18px","font-weight":"bold","box-shadow":"0 2px 8px rgba(0,0,0,0.15)",transition:"all 0.3s ease"}),j(s,{position:"absolute",top:"50%",right:"15px",transform:"translateY(-50%)","z-index":"100",width:"40px",height:"40px",background:"rgba(255,255,255,0.9)",color:"#333",border:"1px solid rgba(0,0,0,0.1)","border-radius":"50%",cursor:"pointer",display:"flex","align-items":"center","justify-content":"center","font-size":"18px","font-weight":"bold","box-shadow":"0 2px 8px rgba(0,0,0,0.15)",transition:"all 0.3s ease"}),t.addEventListener("mouseenter",()=>{j(t,{background:"rgba(255,255,255,1)",transform:"translateY(-50%) scale(1.1)"})}),t.addEventListener("mouseleave",()=>{j(t,{background:"rgba(255,255,255,0.9)",transform:"translateY(-50%) scale(1)"})}),s.addEventListener("mouseenter",()=>{j(s,{background:"rgba(255,255,255,1)",transform:"translateY(-50%) scale(1.1)"})}),s.addEventListener("mouseleave",()=>{j(s,{background:"rgba(255,255,255,0.9)",transform:"translateY(-50%) scale(1)"})}),K(e,t),K(e,s)}appendToDOM(e){const t=Re("#content .container");t&&mi(t,e)}initializeSwiper(e,t){try{const s=this.calculateSwiperConfig(t);this.swiper=new $(`.${N.slider.dom.swiperClass}`,{loop:s.enableLoop,autoplay:{delay:e,disableOnInteraction:!1},spaceBetween:N.slider.swiper.spaceBetween,effect:N.slider.swiper.effect,centeredSlides:s.centeredSlides,slidesPerView:s.slidesPerView,breakpoints:{320:{slidesPerView:1.2,spaceBetween:15},768:{slidesPerView:1.3,spaceBetween:18},1024:{slidesPerView:s.slidesPerView,spaceBetween:N.slider.swiper.spaceBetween}},pagination:{el:N.slider.swiper.pagination.el,type:N.slider.swiper.pagination.type,clickable:!0},navigation:{nextEl:N.slider.swiper.navigation.nextEl,prevEl:N.slider.swiper.navigation.prevEl},initialSlide:0,modules:[ci,ui,fi]})}catch{}}calculateSwiperConfig(e){return e>=Fe.MIN_SLIDES_FOR_DUAL_VIEW?{enableLoop:!0,slidesPerView:1.5,centeredSlides:!0}:e>=Fe.MIN_SLIDES_FOR_SINGLE_VIEW_LOOP?{enableLoop:!0,slidesPerView:1,centeredSlides:!0}:{enableLoop:!1,slidesPerView:1,centeredSlides:!0}}destroy(){this.swiper&&(this.swiper.destroy(!0,!0),delete this.swiper),this.container&&(we(this.container),delete this.container)}}class Q{constructor(){this.errorLog=[],this.isInitialized=!1}static getInstance(){return Q.instance||(Q.instance=new Q),Q.instance}initialize(){try{return this.isInitialized||(this.setupGlobalErrorHandling(),this.isInitialized=!0),!0}catch{return!1}}handleSync(e,t){try{return e()}catch(s){return this.logError(s,t),!1}}handleAsync(e,t){return e().catch(s=>(this.logError(s,t),!1))}logError(e,t){try{const s={timestamp:new Date,error:e,context:t};this.errorLog.push(s),this.errorLog.length>hi.MAX_ERROR_LOG_ENTRIES&&this.errorLog.shift()}catch{}}setupGlobalErrorHandling(){try{globalThis.addEventListener("unhandledrejection",e=>{this.logError(new Error(String(e.reason)),"Unhandled Promise Rejection")})}catch{}}getErrorLog(){return[...this.errorLog]}clearErrorLog(){this.errorLog=[]}}class Y{constructor(){}static getInstance(){return Y.instance||(Y.instance=new Y),Y.instance}isTagsPage(){try{return q.current.get("routeName")==="tags"}catch{try{return globalThis.location.pathname.includes("/tags")}catch{return!1}}}getConfig(){return N}isSlideshowConfigured(){try{for(let s=1;s<=N.slider.maxSlides;s+=1)if(q.forum.attribute(`wusong8899-header-advertisement.Image${s}`))return!0;return!1}catch{return!1}}}q.initializers.add(N.app.extensionId,()=>{const i=Q.getInstance(),e=Y.getInstance();if(!i.initialize())return;const t=new wi;He.extend(We.prototype,"view",function(n){i.handleSync(()=>{e.isTagsPage()&&Si(n,t)},"HeaderPrimary view extension")})});const Si=(i,e,t)=>{try{if(Y.getInstance().isSlideshowConfigured())try{e.attachAdvertiseHeader(i)}catch{}q.session.user||Ti()}catch{}},Ti=()=>{let i=document.getElementById(N.ui.headerIconId);if(i===null){const e=q.forum.attribute("wusong8899-header-advertisement.HeaderIconUrl")||N.ui.headerIconUrl;i=document.createElement("div"),i.id=N.ui.headerIconId,i.className="HeaderIcon-container",i.innerHTML=`<img src="${e}" alt="Header Icon" class="HeaderIcon-image" />`;const t=document.querySelector("#app-navigation .App-backControl .Button.Button--icon.Navigation-drawer");t&&t.parentNode&&t.parentNode.insertBefore(i,t.nextSibling)}}})(flarum.core.compat["common/extend"],flarum.core.compat["forum/app"],flarum.core.compat["forum/components/HeaderPrimary"]);
//# sourceMappingURL=forum.js.map

module.exports={};